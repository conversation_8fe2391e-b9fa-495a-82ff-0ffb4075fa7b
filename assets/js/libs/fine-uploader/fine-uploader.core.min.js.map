{"version": 3, "sources": ["?", "../client/js/util.js", "../client/js/export.js", "../client/js/error/error.js", "../client/js/version.js", "../client/js/features.js", "../client/js/promise.js", "../client/js/blob-proxy.js", "../client/js/button.js", "../client/js/upload-data.js", "../client/js/uploader.basic.api.js", "../client/js/uploader.basic.js", "../client/js/ajax.requester.js", "../client/js/upload-handler/upload.handler.js", "../client/js/upload-handler/upload.handler.controller.js", "../client/js/window.receive.message.js", "../client/js/upload-handler/form.upload.handler.js", "../client/js/upload-handler/xhr.upload.handler.js", "../client/js/deletefile.ajax.requester.js", "../client/js/image-support/megapix-image.js", "../client/js/image-support/image.js", "../client/js/image-support/exif.js", "../client/js/identify.js", "../client/js/image-support/validation.image.js", "../client/js/session.js", "../client/js/session.ajax.requester.js", "../client/js/image-support/scaler.js", "../client/js/third-party/ExifRestorer.js", "../client/js/total-progress.js", "../client/js/paste.js", "../client/js/form-support.js", "../client/js/traditional/traditional.form.upload.handler.js", "../client/js/traditional/traditional.xhr.upload.handler.js", "../client/js/traditional/all-chunks-done.ajax.requester.js"], "names": ["global", "qq", "element", "hide", "style", "display", "this", "attach", "type", "fn", "addEventListener", "attachEvent", "detach", "removeEventListener", "detachEvent", "contains", "descendant", "compareDocumentPosition", "insertBefore", "elementB", "parentNode", "remove", "<PERSON><PERSON><PERSON><PERSON>", "css", "styles", "Error", "opacity", "filter", "Math", "round", "extend", "hasClass", "name", "considerParent", "re", "RegExp", "test", "className", "addClass", "removeClass", "replace", "getByClass", "first", "candidates", "result", "querySelector", "querySelectorAll", "getElementsByTagName", "each", "idx", "val", "push", "getFirstByClass", "children", "child", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "nextS<PERSON>ling", "setText", "text", "innerText", "textContent", "clearText", "hasAttribute", "attrName", "attrVal", "exec", "getAttribute", "undefined", "canvasToBlob", "canvas", "mime", "quality", "dataUriToBlob", "toDataURL", "dataUri", "arrayBuffer", "byteString", "intArray", "mimeString", "createBlob", "data", "BlobBuilder", "window", "WebKitBlobBuilder", "MozBlobBuilder", "MSBlobBuilder", "blobBuilder", "append", "getBlob", "Blob", "split", "indexOf", "atob", "decodeURI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "Uint8Array", "character", "charCodeAt", "log", "message", "level", "console", "isObject", "variable", "Object", "prototype", "toString", "call", "isFunction", "isArray", "value", "buffer", "constructor", "isItemList", "maybeItemList", "isNodeList", "maybeNodeList", "item", "namedItem", "isString", "maybeString", "trimStr", "string", "String", "trim", "format", "str", "args", "Array", "slice", "arguments", "newStr", "nextIdxToReplace", "strBefore", "substring", "strAfter", "isFile", "maybeFile", "File", "isFileList", "maybeFileList", "FileList", "isFileOrInput", "maybeFileOrInput", "isInput", "maybeInput", "notFile", "evaluateType", "normalizedType", "toLowerCase", "HTMLInputElement", "tagName", "isBlob", "maybeBlob", "isXhrUploadSupported", "input", "document", "createElement", "multiple", "FormData", "createXhrInstance", "upload", "XMLHttpRequest", "ActiveXObject", "error", "isFolderDropSupported", "dataTransfer", "items", "webkitGetAsEntry", "isFileChunkingSupported", "androidStock", "webkitSlice", "mozSlice", "sliceBlob", "fileOrBlob", "start", "end", "slicer", "arrayBufferToHex", "bytesAsHex", "bytes", "byt", "byteAsHexStr", "readBlobToHex", "blob", "startOffset", "initialBlob", "fileReader", "FileReader", "promise", "Promise", "onload", "success", "onerror", "failure", "readAsA<PERSON>y<PERSON><PERSON>er", "second", "extendNested", "prop", "override", "target", "sourceFn", "super_", "source", "srcPropName", "srcPropVal", "arr", "elt", "from", "len", "hasOwnProperty", "getUniqueId", "c", "r", "random", "v", "ie", "navigator", "userAgent", "ie7", "ie8", "ie10", "ie11", "edge", "safari", "vendor", "chrome", "opera", "firefox", "windows", "platform", "android", "ios6", "ios", "ios7", "ios8", "ios800", "iosChrome", "iosSafari", "iosSafariWebView", "preventDefault", "e", "returnValue", "toElement", "div", "html", "innerHTML", "iterableItem", "callback", "keyOrIndex", "retVal", "Storage", "key", "getItem", "char<PERSON>t", "bind", "oldFunc", "context", "newArgs", "concat", "apply", "obj2url", "obj", "temp", "prefixDone", "uristrings", "prefix", "add", "nextObj", "i", "nextTemp", "encodeURIComponent", "join", "obj2FormData", "formData", "arrayKeyName", "obj2Inputs", "form", "setAttribute", "append<PERSON><PERSON><PERSON>", "parseJson", "json", "JSON", "parse", "eval", "getExtension", "filename", "extIdx", "lastIndexOf", "substr", "getFilename", "blobOrFileInput", "fileName", "DisposeSupport", "disposers", "dispose", "disposer", "shift", "addDisposer", "disposeFunction", "define", "amd", "module", "exports", "version", "supportedFeatures", "testSupportsFileInputElement", "tempInput", "supported", "disabled", "ex", "isChrome21Or<PERSON>igher", "match", "isChrome14Or<PERSON><PERSON><PERSON>", "isCrossOriginXhrSupported", "xhr", "withCredentials", "isXdrSupported", "XDomainRequest", "isCrossOriginAjaxSupported", "isFolderSelectionSupported", "webkitdirectory", "isLocalStorageSupported", "localStorage", "setItem", "isDragAndDropSupported", "span", "supportsUploading", "supportsUploadingBlobs", "supportsFileDrop", "supportsAjaxFileUploading", "supportsFolderDrop", "supportsChunking", "supportsResume", "supportsUploadViaPaste", "supportsUploadCors", "supportsDeleteFileXdr", "supportsDeleteFileCorsXhr", "supportsDeleteFileCors", "supportsFolderSelection", "supportsImagePreviews", "supportsUploadProgress", "postMessage", "ajaxUploading", "blobUploading", "canDetermineSize", "chunking", "deleteFileCors", "deleteFileCorsXdr", "deleteFileCorsXhr", "dialogElement", "HTMLDialogElement", "fileDrop", "folderDrop", "folderSelection", "imagePreviews", "imageValidation", "itemSizeValidation", "pause", "progressBar", "resume", "scaling", "tiffPreviews", "unlimitedScaledImageSize", "uploading", "uploadCors", "uploadCustomHeaders", "uploadNonMultipart", "uploadViaPaste", "isGenericPromise", "<PERSON><PERSON><PERSON><PERSON>", "then", "success<PERSON><PERSON>s", "failureArgs", "successCallbacks", "failureCallbacks", "doneCallbacks", "state", "onSuccess", "onFailure", "done", "BlobProxy", "referenceBlob", "onCreate", "create", "UploadButton", "o", "createInput", "BUTTON_ID_ATTR_NAME", "buttonId", "options", "title", "self", "setMultiple", "folders", "acceptFiles", "position", "right", "top", "fontFamily", "fontSize", "margin", "padding", "cursor", "height", "disposeSupport", "onChange", "hoverClass", "focusClass", "ios8BrowserCrashWorkaround", "overflow", "direction", "getInput", "getButtonId", "isMultiple", "optInput", "removeAttribute", "setAcceptFiles", "reset", "UploadData", "uploaderProxy", "getDataByIds", "idOrIds", "entries", "id", "getDataByUuids", "uuids", "uuid", "byUuid", "getDataByStatus", "status", "statusResults", "statuses", "index", "statusEnum", "statusResultIndexes", "byStatus", "dataIndex", "byProxyGroupId", "byBatchId", "addFile", "spec", "SUBMITTING", "originalName", "size", "batchId", "proxyGroupId", "onStatusChange", "retrieve", "optionalFilter", "setStatus", "newStatus", "oldStatus", "byStatusOldStatusIndex", "splice", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newUuid", "oldUuid", "updateName", "newName", "updateSize", "newSize", "setParentId", "targetId", "parentId", "getIdsInProxyGroup", "getIdsInBatch", "SUBMITTED", "REJECTED", "QUEUED", "CANCELED", "PAUSED", "UPLOADING", "UPLOAD_RETRYING", "UPLOAD_SUCCESSFUL", "UPLOAD_FAILED", "DELETE_FAILED", "DELETING", "DELETED", "basePublicApi", "addBlobs", "blobDataOrArray", "params", "endpoint", "addFiles", "addInitialFiles", "cannedFileList", "cannedFile", "_addCannedFile", "_maybeHandleIos8SafariWorkaround", "_storedIds", "_currentBatchId", "processBlob", "_handleNewFile", "_options", "blobs", "defaultName", "verifiedFiles", "processBlobData", "blobData", "processCanvas", "processCanvasData", "canvasData", "normalizedQuality", "processFileOrInput", "fileOrInput", "files", "file", "normalizeData", "fileContainer", "_prepareItemsForUpload", "cancel", "_handler", "cancelAll", "storedIdsCopy", "storedFileId", "clearStoredFiles", "continueUpload", "uploadData", "_uploadData", "enabled", "getName", "_uploadFile", "deleteFile", "_onSubmitDelete", "doesExist", "fileOrBlobId", "<PERSON><PERSON><PERSON><PERSON>", "drawThumbnail", "fileId", "imgOrCanvas", "maxSize", "fromServer", "customResizeFunction", "fileOrUrl", "promiseToReturn", "_imageGenerator", "_thumbnailUrls", "scale", "getFile", "container", "generate", "modifiedContainer", "reason", "getButton", "_getButton", "_buttonIdsForFileIds", "getEndpoint", "_endpointStore", "get", "getInProgress", "getParentId", "uploadDataEntry", "getUploads", "getResumableFilesData", "getSize", "getNetUploads", "_netUploaded", "getRemainingAllowedItems", "allowedItems", "_currentItemLimit", "_netUploadedOrQueued", "getUuid", "debug", "pauseUpload", "_autoRetries", "_retryTimeouts", "_preventRetries", "_buttons", "button", "_paramsStore", "_paste<PERSON><PERSON><PERSON>", "session", "refreshOnReset", "_refreshSessionData", "_succeededSinceLastAllComplete", "_failedSinceLastAllComplete", "_totalProgress", "retry", "_manualRetry", "scaleImage", "specs", "Scaler", "setCustomHeaders", "headers", "_customHeadersStore", "set", "setDeleteFileCustomHeaders", "_deleteFileCustomHeadersStore", "setDeleteFileEndpoint", "_deleteFileEndpointStore", "setDeleteFileParams", "_deleteFileParamsStore", "setEndpoint", "setForm", "elementOrId", "_updateFormSupportAndParams", "setItemLimit", "newItemLimit", "setName", "setParams", "setUuid", "uploadStoredFiles", "_itemError", "_uploadStoredFiles", "basePrivateApi", "sessionData", "deleteFileEndpoint", "deleteFileParams", "thumbnailUrl", "_annotateWithButtonId", "associatedInput", "qqButtonId", "_getButtonId", "_batchError", "callbacks", "onError", "_createDeleteHandler", "DeleteFileAjaxRequester", "method", "toUpperCase", "maxConnections", "uuidParamName", "request", "uuidName", "customHeaders", "paramsStore", "endpointStore", "cors", "onDelete", "_onDelete", "onDeleteComplete", "xhrOrXdr", "isError", "_onDeleteComplete", "_createPasteHandler", "PasteSupport", "targetElement", "paste", "pasteReceived", "_handleCheckedCallback", "onPasteReceived", "_handlePasteSuccess", "identifier", "_createStore", "initialValue", "_readOnlyValues_", "store", "catchall", "perIdReadOnlyValues", "readOnly<PERSON><PERSON>ues", "copy", "orig", "getReadOnlyValues", "includeReadOnlyValues", "existing", "values", "addReadOnly", "_createUploadDataTracker", "_onUploadStatusChange", "_maybeAllComplete", "setTimeout", "_createUploadButton", "allowMultiple", "workarounds", "iosEmptyVideos", "_isAllowedExtension", "allowedExtensions", "accept", "validation", "classes", "buttonFocus", "buttonHover", "ios8BrowserCrash", "inputName", "_onInputChange", "fileInputTitle", "_disposeSupport", "_createUploadHandler", "additionalOptions", "namespace", "lastOnProgress", "preventRetryParam", "preventRetryResponseProperty", "onProgress", "loaded", "total", "_onProgress", "onComplete", "_onComplete", "onCancel", "cancelFinalizationEffort", "_onCancel", "onUploadPrep", "_onUploadPrep", "onUpload", "_onUpload", "onUploadChunk", "chunkData", "_onUploadChunk", "onUploadChunkSuccess", "onResume", "onAutoRetry", "responseJSON", "_onAutoRetry", "onUuidChanged", "setSize", "_setSize", "getDataByUuid", "isQueued", "UploadHandlerController", "_fileOrBlobRejected", "_formatSize", "max", "toFixed", "sizeSymbols", "_generateExtraButtonSpecs", "_extraButtonSpecs", "extraButtons", "extraButtonOptionEntry", "extraButtonSpec", "_initExtraButton", "extraButtonsSpec", "_defaultButtonId", "buttonOrFileInputOrFile", "inputs", "fileInput", "fileBlobOrInput", "_getNotFinished", "_getValidationBase", "_getValidationDescriptor", "fileWrapper", "_getValidationDescriptors", "fileWrappers", "fileDescriptors", "_handleCameraAccess", "camera", "acceptIosCamera", "optionRoot", "details", "callbackRetVal", "successParam", "newFileWrapperList", "actualFile", "handler", "_customNewFileHandler", "_handleNewFileGeneric", "addFileToHandler", "_trackButton", "fileList", "extSuppliedName", "extension", "_initFormSupportAndParams", "_formSupport", "FormSupport", "attachedToForm", "getFormInputsAsObject", "autoUpload", "newAutoUpload", "newEndpoint", "_isDeletePossible", "expected", "allowXdr", "allowed", "valid", "allowedExt", "extRegex", "code", "maybeNameOrNames", "replacement", "extensionsForMessage", "placeholder<PERSON><PERSON>", "messages", "names", "validationBase", "allowedExtension", "formatFileName", "sizeLimit", "minSizeLimit", "placeholder", "_onBeforeManualRetry", "notFinished", "_onAllComplete", "ios8SafariUploads", "alert", "unsupportedBrowserIos8Safari", "_maybeParseAndSendUploadError", "response", "errorReason", "defaultResponseError", "_maybeProcessNextItemAfterOnValidateCallback", "validItem", "stopOnFirstInvalidFile", "validationDescriptor", "onValidate", "_onValidateCallbackSuccess", "_onValidateCallbackFailure", "successful", "failed", "onAllComplete", "_shouldAutoRetry", "_onBeforeAutoRetry", "autoAttemptDelay", "itemLimit", "onManualRetry", "clearTimeout", "storedItemIndex", "expunge", "fileIndex", "onIndividualProgress", "_onSubmit", "_onSubmitCallbackSuccess", "_onSubmitted", "onSubmitted", "_storeForLater", "onSuccessCallback", "additionalMandatedParams", "adjustedOnSuccessCallback", "onSubmitDelete", "_delete<PERSON><PERSON>ler", "sendDelete", "_onTotalProgress", "onTotalProgress", "_onValidateBatchCallbackFailure", "_onValidateBatchCallbackSuccess", "validationDescriptors", "errorMessage", "proposedNetFilesUploadedOrQueued", "tooManyItemsError", "nextIndex", "_validateFileOrBlobData", "_upload", "onValidateBatch", "_preventLeaveInProgress", "event", "onLeave", "Session", "_session", "addFileRecord", "refresh", "_sessionRequestComplete", "onSessionRequestComplete", "onNewSize", "enableAuto", "maxAutoAttempts", "formElementOrId", "onSubmit", "idToUpload", "stillSubmitting", "<PERSON><PERSON><PERSON><PERSON>", "ImageValidation", "validate", "image", "errorCode", "_wrapCallbacks", "safeCallback", "errorMsg", "exception", "callback<PERSON><PERSON>", "callback<PERSON><PERSON><PERSON>", "FineUploaderBasic", "disableCancelForFormUploads", "filenameParam", "forceMultipart", "paramsInBody", "totalFileSizeName", "maxHeight", "max<PERSON><PERSON><PERSON>", "minHeight", "min<PERSON><PERSON><PERSON>", "maybeXhr", "maybeXhrOrXdr", "attemptNumber", "fileOrBlobData", "typeError", "sizeError", "minSizeError", "emptyError", "noFilesError", "maxHeightImageError", "maxWidthImageError", "minHeightImageError", "minWidthImageError", "retryFailTooManyItems", "concurrent", "mandatory", "paramNames", "partIndex", "partByteOffset", "chunkSize", "totalFileSize", "totalParts", "partSize", "recordsExpireIn", "resuming", "fileOrBlobName", "sendCredentials", "interceptSubmit", "customResizer", "sendOriginal", "orient", "defaultType", "defaultQuality", "failureText", "includeExif", "sizes", "ImageGenerator", "_scaler", "handleNewFile", "TotalProgress", "entry", "AjaxRequester", "isSimpleMethod", "containsNonSimpleHeaders", "containsNonSimple", "header", "isXdr", "getCorsAjaxTransport", "ontimeout", "onprogress", "getXhrOrXdr", "suppliedXhr", "requestData", "dequeue", "nextId", "queue", "sendRequest", "xdrError", "isResponseSuccessful", "getParams", "onDemandParams", "additionalParams", "mandatedParams", "optXhr", "url", "payload", "onSend", "createUrl", "additionalQueryParams", "getXdrLoadHandler", "getXdrErrorHandler", "onreadystatechange", "getXhrReadyStateChangeHandler", "registerForUploadProgress", "open", "setHeaders", "send", "shouldParamsBeInQueryString", "contentType", "stringify", "addToPath", "readyState", "lengthComputable", "onDemandHeaders", "additionalHeaders", "allHeaders", "acceptHeader", "setRequestHeader", "allowXRequestedWithAndCacheControl", "responseCode", "successfulResponseCodes", "prepareToSend", "validMethods", "DELETE", "PATCH", "POST", "PUT", "GET", "initTransport", "path", "cacheBuster", "with<PERSON><PERSON>", "appendToPath", "withParams", "with<PERSON><PERSON>yP<PERSON><PERSON>", "_additionalQueryParams_", "withHeaders", "withPayload", "thePayload", "withCache<PERSON><PERSON>", "qqtimestamp", "Date", "getTime", "canceled", "UploadHandler", "proxy", "fileState", "fileItem", "onCancelRetVal", "getThirdPartyFileId", "_getFileState", "_setThirdPartyFileId", "thirdPartyFileId", "_wasCanceled", "preventRetryResponse", "controller", "chunkingPossible", "concurrentChunkingPossible", "chunked", "chunkIdx", "_getChunkData", "attemptingResume", "chunkProgress", "_getChunkDataForCallback", "finalize", "finalizeChunks", "normaizedResponse", "normalizeResponse", "_maybeDeletePersistedChunkData", "cleanup", "hasMoreParts", "remaining", "nextPart", "nextIdx", "_getTotalChunks", "reevaluateChunking", "sendNext", "inProgressChunks", "inProgress", "connectionManager", "available", "uploadChunk", "clearCachedChunk", "responseToReport", "inProgressChunkIdx", "_maybePersistChunkedState", "inProgressIdx", "unshift", "ignoreFailure", "keys", "_getXhrs", "ckid", "ckXhr", "abort", "_cancelled", "moveInProgressToRemaining", "free", "clearXhr", "_open", "_openChunks", "_waiting", "openChunkEntriesCount", "openChunksCount", "openChunkIndexes", "dontAllowNext", "allowNext", "waitingIndex", "connectionsIndex", "getProxyOrBlob", "getWaitingOrConnected", "waitingOrConnected", "chunks", "parseInt", "isUsingConnection", "pop", "openChunksEntry", "simple", "uploadFile", "maybeNewUuid", "_clearXhrs", "getProxy", "initHandler", "handlerType", "traditional", "handlerModuleSubtype", "_removeExpiredChunkingRecords", "isDeferredEligibleForUpload", "<PERSON><PERSON><PERSON><PERSON>", "generatedBlob", "updateBlob", "maybeSendDeferredFiles", "errorResponse", "idsInGroup", "uploadedThisId", "idInGroup", "now", "originalResponse", "_shouldChunkThisFile", "blobToUpload", "cancelRetVal", "isProxied", "isResumable", "WindowReceiveMessage", "callbackWrapperDetachers", "receiveMessage", "onMessageCallbackWrapper", "stopReceivingMessages", "detacher", "FormUploadHandler", "expungeFile", "detachLoadEvents", "isCors", "postMessageCallbackTimers", "corsMessageReceiver", "iframe", "getElementById", "_getIframeName", "getFileIdForIframeName", "iframeName", "initIframeForUpload", "body", "registerPostMessageCallback", "onloadCallbacks", "onloadCallback", "_parseJsonResponse", "_detachLoadEvent", "formHandlerInstanceId", "_attachLoadEvent", "responseDescriptor", "contentDocument", "_createIframe", "_initFormForUpload", "targetName", "innerHtmlOrMessage", "XhrUploadHandler", "xhrId", "ajaxRequester", "_getAjaxRequester", "chunkFiles", "resumeEnabled", "blobOrProxy", "_initTempState", "_maybePrepareForResume", "cachedChunks", "tempState", "xhrs", "ajaxRequesters", "response<PERSON><PERSON><PERSON>", "lastChunkIdx", "_getXhr", "resumableFilesData", "_iterateResumeRecords", "notResumable", "optInProgress", "optRemaining", "reverse", "paused", "totalChunks", "parts", "newBlob", "chunkId", "_createXhr", "optChunkIdx", "_registerXhr", "chunkIndex", "fileSize", "startBytes", "endBytes", "part", "count", "startByte", "endByte", "_getLocalStorageId", "formatVersion", "_getMimeType", "_getPersistableData", "ceil", "_markNotResumable", "localStorageId", "removeItem", "persistedData", "lastUpdated", "_registerProgressHandler", "progressCalculator", "totalSuccessfullyLoadedForFile", "loadedForRequest", "totalForRequest", "estActualChunkLoaded", "totalLoadedForFile", "chunkLoaded", "optAjaxRequester", "xhrsId", "expirationDays", "expirationDate", "setDate", "getDate", "getMandatedParams", "_method", "requester", "detectSubsampling", "img", "ctx", "iw", "naturalWidth", "ih", "naturalHeight", "width", "getContext", "drawImage", "getImageData", "detectVerticalSquash", "alpha", "ratio", "sy", "ey", "py", "renderImageToDataURL", "doSquash", "renderImageToCanvas", "maybeCalculateDownsampledDimensions", "maxPixels", "origHeight", "origWidth", "newHeight", "sqrt", "newWidth", "modifiedDimensions", "save", "resize", "renderImageToCanvasWithCustomResizer", "imageHeight", "imageWidth", "orientation", "targetHeight", "targetWidth", "transformCoordinate", "tmpCtx", "sx", "dx", "d", "tmpCanvas", "vertSquashRatio", "dw", "dh", "dy", "clearRect", "restore", "qqImageRendered", "resizeInfo", "sourceCanvas", "sourceCanvasContext", "targetCanvas", "translate", "rotate", "PI", "MegaPixImage", "srcImage", "<PERSON><PERSON><PERSON><PERSON>", "Image", "URL", "createObjectURL", "webkitURL", "src", "listeners", "imageLoadListeners", "render", "opt", "imgWidth", "imgHeight", "optionsKey", "optionsValue", "oldTargetSrc", "onrender", "isImg", "el", "isCanvas", "isImgCorsSupported", "crossOrigin", "isCanvasSupported", "determineMimeOfFileName", "nameWithPath", "pathSegments", "isCrossOrigin", "targetProtocol", "targetHostname", "targetPort", "targetAnchor", "href", "protocol", "port", "hostname", "location", "registerImgLoadListeners", "registerCanvasDrawImageListener", "registerThumbnailRenderedListener", "registered", "draw", "drawPreview", "Identify", "megapixErrorHandler", "isPreviewable", "dummyExif", "exif", "Exif", "mpImg", "Orientation", "failureMsg", "drawOnCanvasOrImgFromUrl", "canvasOrImg", "tempImg", "tempImgRender", "drawOnImgFromUrlWithCssScaling", "drawFromUrl", "fileBlobOrUrl", "_testing", "parseLittleEndian", "hex", "pow", "seekToApp1", "offset", "theOffset", "thePromise", "segmentLength", "getApp1Offset", "isLittleEndian", "app1Start", "getDirEntryCount", "littleEndian", "getIfd", "dirEntries", "getDirEntries", "ifdHex", "getTagValues", "TAG_VAL_OFFSET", "tagsToFind", "TAG_IDS", "vals", "tagValHex", "tag<PERSON><PERSON><PERSON><PERSON><PERSON>", "idHex", "tagsToFindIdx", "TAG_INFO", "274", "parser", "onParseFailure", "app1Offset", "dirEntryCount", "tagValues", "isIdentifiable", "magicBytes", "questionableBytes", "identifiable", "magicBytesEntries", "magicBytesArrayEntry", "previewable", "isPreviewableSync", "PREVIEWABLE_MIME_TYPES", "fileMime", "isRecognizedImage", "image/jpeg", "image/gif", "image/png", "image/bmp", "image/tiff", "hasNonZeroLimits", "limits", "atLeastOne", "limit", "getWidthHeight", "sizeDetermination", "getFailingLimit", "dimensions", "failingLimit", "limitName", "limitValue", "limitM<PERSON><PERSON>", "dimensionPropName", "actualValue", "validationEffort", "isJsonResponseValid", "handleFileItems", "fileItems", "someItemsIgnored", "err", "refreshEffort", "refreshCompleteCallback", "requesterOptions", "SessionAjaxRequester", "queryServer", "responseText", "includeOriginal", "failedToScaleText", "_getSortedSizes", "getFileRecords", "originalFileUuid", "originalFileName", "originalBlobOrBlobData", "records", "originalBlob", "sizeRecord", "outputType", "_determineOutputType", "requestedType", "refType", "_getName", "_generateScaledImage", "failedText", "api", "scaledIds", "originalId", "record", "blobSize", "scaledId", "qqparentuuid", "qqparentsize", "param", "scalingEffort", "scalingOptions", "scaler", "referenceType", "scaledVersionProperties", "startOfExt", "versionType", "scaledName", "scaledExt", "nameAppendage", "sort", "a", "b", "sourceFile", "imageGenerator", "scaledImageDataUri", "signalSuccess", "_insertExifHeader", "scaledImageDataUriWithExif", "originalImage", "reader", "insertionEffort", "originalImageDataUri", "ExifRestorer", "readAsDataURL", "_dataUriToBlob", "_createBlob", "KEY_STR", "encode64", "chr1", "chr2", "enc1", "enc2", "enc3", "output", "chr3", "enc4", "isNaN", "origFileBase64", "resizedFileBase64", "expectedBase64Header", "rawImage", "decode64", "segments", "slice2Segments", "exifManipulation", "exifArray", "getExifArray", "newImageArray", "insertExif", "<PERSON><PERSON><PERSON><PERSON>", "seg", "x", "imageData", "buf", "separatePoint", "mae", "ato", "array", "rawImageArray", "head", "endPoint", "base64test", "perFileProgress", "totalLoaded", "totalSize", "lastLoadedSent", "lastTotalSent", "callbackProxy", "noRetryableFiles", "retryable", "none", "failedId", "updateTotalProgress", "onNew", "newLoaded", "newTotal", "oldLoaded", "oldTotal", "isImage", "registerPasteHandler", "detachPasteHandler", "clipboardData", "getAsFile", "unregisterPasteHandler", "startUpload", "determineNewEndpoint", "formEl", "validateForm", "nativeSubmit", "checkValidity", "maybeUploadOnSubmit", "submit", "determineFormEl", "_form2Obj", "notIrrelevantType", "irrelevantTypes", "radioOrCheckbox", "ignoreValue", "checked", "selectValue", "select", "selected", "elements", "getIframeContentJson", "doc", "innerHtml", "contentWindow", "nodeValue", "createForm", "responseFromMessage", "multipart", "addChunkingSpecificParams", "allChunksDoneRequester", "AllChunksDoneAjaxRequester", "createReadyStateChangedHandler", "onUploadOrChunkComplete", "getChunksCompleteParams", "isErrorUploadResponse", "parseResponse", "sendChunksCompleteRequest", "complete", "setParamsAndGetEntityToSend", "setUploadHeaders", "extraHeaders", "toSend", "promises", "endpoint<PERSON><PERSON><PERSON>"], "mappings": ";CAAA,SAAUA,QCEV,GAAIC,IAAK,SAASC,GACd,YAEA,QACIC,KAAM,WAEF,MADAD,GAAQE,MAAMC,QAAU,OACjBC,MAIXC,OAAQ,SAASC,EAAMC,GAMnB,MALIP,GAAQQ,iBACRR,EAAQQ,iBAAiBF,EAAMC,GAAI,GAC5BP,EAAQS,aACfT,EAAQS,YAAY,KAAOH,EAAMC,GAE9B,WACHR,GAAGC,GAASU,OAAOJ,EAAMC,KAIjCG,OAAQ,SAASJ,EAAMC,GAMnB,MALIP,GAAQW,oBACRX,EAAQW,oBAAoBL,EAAMC,GAAI,GAC/BP,EAAQS,aACfT,EAAQY,YAAY,KAAON,EAAMC,GAE9BH,MAGXS,SAAU,SAASC,GAKf,QAAKA,IAKDd,IAAYc,IAIZd,EAAQa,SACDb,EAAQa,SAASC,MAGgC,EAA9CA,EAAWC,wBAAwBf,OAOrDgB,aAAc,SAASC,GAEnB,MADAA,GAASC,WAAWF,aAAahB,EAASiB,GACnCb,MAGXe,OAAQ,WAEJ,MADAnB,GAAQkB,WAAWE,YAAYpB,GACxBI,MAOXiB,IAAK,SAASC,GAEV,GAAqB,MAAjBtB,EAAQE,MACR,KAAM,IAAIH,IAAGwB,MAAM,6EAWvB,OAPsB,OAAlBD,EAAOE,SAC8B,gBAA1BxB,GAAQE,MAAMsB,SAAqD,mBAArBxB,GAAe,UACpEsB,EAAOG,OAAS,iBAAmBC,KAAKC,MAAM,IAAML,EAAOE,SAAW,KAG9EzB,GAAG6B,OAAO5B,EAAQE,MAAOoB,GAElBlB,MAGXyB,SAAU,SAASC,EAAMC,GACrB,GAAIC,GAAK,GAAIC,QAAO,QAAUH,EAAO,QACrC,OAAOE,GAAGE,KAAKlC,EAAQmC,eAAiBJ,IAAkBC,EAAGE,KAAKlC,EAAQkB,WAAWiB,aAGzFC,SAAU,SAASN,GAIf,MAHK/B,IAAGC,GAAS6B,SAASC,KACtB9B,EAAQmC,WAAa,IAAML,GAExB1B,MAGXiC,YAAa,SAASP,GAClB,GAAIE,GAAK,GAAIC,QAAO,QAAUH,EAAO,QAErC,OADA9B,GAAQmC,UAAYnC,EAAQmC,UAAUG,QAAQN,EAAI,KAAKM,QAAQ,aAAc,IACtElC,MAGXmC,WAAY,SAASJ,EAAWK,GAC5B,GAAIC,GACAC,IAEJ,OAAIF,IAASxC,EAAQ2C,cACV3C,EAAQ2C,cAAc,IAAMR,GAE9BnC,EAAQ4C,iBACN5C,EAAQ4C,iBAAiB,IAAMT,IAG1CM,EAAazC,EAAQ6C,qBAAqB,KAE1C9C,GAAG+C,KAAKL,EAAY,SAASM,EAAKC,GAC1BjD,GAAGiD,GAAKnB,SAASM,IACjBO,EAAOO,KAAKD,KAGbR,EAAQE,EAAO,GAAKA,IAG/BQ,gBAAiB,SAASf,GACtB,MAAOpC,IAAGC,GAASuC,WAAWJ,GAAW,IAG7CgB,SAAU,WAIN,IAHA,GAAIA,MACAC,EAAQpD,EAAQqD,WAEbD,GACoB,IAAnBA,EAAME,UACNH,EAASF,KAAKG,GAElBA,EAAQA,EAAMG,WAGlB,OAAOJ,IAGXK,QAAS,SAASC,GAGd,MAFAzD,GAAQ0D,UAAYD,EACpBzD,EAAQ2D,YAAcF,EACfrD,MAGXwD,UAAW,WACP,MAAO7D,IAAGC,GAASwD,QAAQ,KAK/BK,aAAc,SAASC,GACnB,GAAIC,EAEJ,OAAI/D,GAAQ6D,eAEH7D,EAAQ6D,aAAaC,IAKkC,MAArD,WAAaE,KAAKhE,EAAQiE,aAAaH,KAG9CC,EAAU/D,EAAQ8D,GAEFI,SAAZH,GAKiC,MAA9B,WAAaC,KAAKD,QAMxC,WACG,YAEAhE,IAAGoE,aAAe,SAASC,EAAQC,EAAMC,GACrC,MAAOvE,IAAGwE,cAAcH,EAAOI,UAAUH,EAAMC,KAGnDvE,GAAGwE,cAAgB,SAASE,GACxB,GAAIC,GAAaC,EAgBbC,EAAUC,EAfVC,EAAa,SAASC,EAAMV,GACxB,GAAIW,GAAcC,OAAOD,aACjBC,OAAOC,mBACPD,OAAOE,gBACPF,OAAOG,cACXC,EAAcL,GAAe,GAAIA,EAErC,OAAIK,IACAA,EAAYC,OAAOP,GACZM,EAAYE,QAAQlB,IAGpB,GAAImB,OAAMT,IAAQzE,KAAM+D,IAyB3C,OAlBIM,GADAF,EAAQgB,MAAM,KAAK,GAAGC,QAAQ,WAAa,EAC9BC,KAAKlB,EAAQgB,MAAM,KAAK,IAGxBG,UAAUnB,EAAQgB,MAAM,KAAK,IAI9CZ,EAAaJ,EAAQgB,MAAM,KAAK,GAC3BA,MAAM,KAAK,GACXA,MAAM,KAAK,GAGhBf,EAAc,GAAImB,aAAYlB,EAAWmB,QACzClB,EAAW,GAAImB,YAAWrB,GAC1B3E,GAAG+C,KAAK6B,EAAY,SAAS5B,EAAKiD,GAC9BpB,EAAS7B,GAAOiD,EAAUC,WAAW,KAGlCnB,EAAWJ,EAAaG,IAGnC9E,GAAGmG,IAAM,SAASC,EAASC,GACnBnB,OAAOoB,UACFD,GAAmB,SAAVA,EAKNnB,OAAOoB,QAAQD,GACfnB,OAAOoB,QAAQD,GAAOD,GAGtBlB,OAAOoB,QAAQH,IAAI,IAAME,EAAQ,KAAOD,GAR5ClB,OAAOoB,QAAQH,IAAIC,KAc/BpG,GAAGuG,SAAW,SAASC,GACnB,MAAOA,KAAaA,EAASjD,UAAyD,oBAA7CkD,OAAOC,UAAUC,SAASC,KAAKJ,IAG5ExG,GAAG6G,WAAa,SAASL,GACrB,MAA6B,kBAAf,IASlBxG,GAAG8G,QAAU,SAASC,GAClB,MAAiD,mBAA1CN,OAAOC,UAAUC,SAASC,KAAKG,IACjCA,GAAS7B,OAAOY,aAAeiB,EAAMC,QAAUD,EAAMC,OAAOC,cAAgBnB,aAIrF9F,GAAGkH,WAAa,SAASC,GACrB,MAAyD,kCAAlDV,OAAOC,UAAUC,SAASC,KAAKO,IAK1CnH,GAAGoH,WAAa,SAASC,GACrB,MAAyD,sBAAlDZ,OAAOC,UAAUC,SAASC,KAAKS,IAGjCA,EAAcC,MAAQD,EAAcE,WAG7CvH,GAAGwH,SAAW,SAASC,GACnB,MAAuD,oBAAhDhB,OAAOC,UAAUC,SAASC,KAAKa,IAG1CzH,GAAG0H,QAAU,SAASC,GAClB,MAAIC,QAAOlB,UAAUmB,KACVF,EAAOE,OAGXF,EAAOpF,QAAQ,aAAc,KAOxCvC,GAAG8H,OAAS,SAASC,GAEjB,GAAIC,GAAQC,MAAMvB,UAAUwB,MAAMtB,KAAKuB,UAAW,GAC9CC,EAASL,EACTM,EAAmBD,EAAOzC,QAAQ,KAetC,OAbA3F,IAAG+C,KAAKiF,EAAM,SAAShF,EAAKC,GACxB,GAAIqF,GAAYF,EAAOG,UAAU,EAAGF,GAChCG,EAAWJ,EAAOG,UAAUF,EAAmB,EAMnD,IAJAD,EAASE,EAAYrF,EAAMuF,EAC3BH,EAAmBD,EAAOzC,QAAQ,KAAM0C,EAAmBpF,EAAI8C,QAG3DsC,EAAmB,EACnB,OAAO,IAIRD,GAGXpI,GAAGyI,OAAS,SAASC,GACjB,MAAOxD,QAAOyD,MAAsD,kBAA9ClC,OAAOC,UAAUC,SAASC,KAAK8B,IAGzD1I,GAAG4I,WAAa,SAASC,GACrB,MAAO3D,QAAO4D,UAA8D,sBAAlDrC,OAAOC,UAAUC,SAASC,KAAKiC,IAG7D7I,GAAG+I,cAAgB,SAASC,GACxB,MAAOhJ,IAAGyI,OAAOO,IAAqBhJ,GAAGiJ,QAAQD,IAGrDhJ,GAAGiJ,QAAU,SAASC,EAAYC,GAC9B,GAAIC,GAAe,SAAS7I,GACxB,GAAI8I,GAAiB9I,EAAK+I,aAE1B,OAAIH,GAC0B,SAAnBE,EAGe,SAAnBA,EAGX,UAAInE,OAAOqE,kBAC4C,8BAA/C9C,OAAOC,UAAUC,SAASC,KAAKsC,IAC3BA,EAAW3I,MAAQ6I,EAAaF,EAAW3I,WAKnD2I,EAAWM,SAC8B,UAArCN,EAAWM,QAAQF,eACfJ,EAAW3I,MAAQ6I,EAAaF,EAAW3I,QAS3DP,GAAGyJ,OAAS,SAASC,GACjB,GAAIxE,OAAOO,MAAsD,kBAA9CgB,OAAOC,UAAUC,SAASC,KAAK8C,GAC9C,OAAO,GAIf1J,GAAG2J,qBAAuB,WACtB,GAAIC,GAAQC,SAASC,cAAc,QAGnC,OAFAF,GAAMrJ,KAAO,OAGU4D,SAAnByF,EAAMG,UACc,mBAATpB,OACa,mBAAbqB,WACoC,mBAAnChK,IAAGiK,oBAAqBC,QAI5ClK,GAAGiK,kBAAoB,WACnB,GAAI/E,OAAOiF,eACP,MAAO,IAAIA,eAGf,KACI,MAAO,IAAIC,eAAc,sBAE7B,MAAOC,GAEH,MADArK,IAAGmG,IAAI,wCAAyC,SACzC,OAIfnG,GAAGsK,sBAAwB,SAASC,GAChC,MAAOA,GAAaC,OAChBD,EAAaC,MAAMzE,OAAS,GAC5BwE,EAAaC,MAAM,GAAGC,kBAG9BzK,GAAG0K,wBAA0B,WACzB,OAAQ1K,GAAG2K,gBACP3K,GAAG2J,yBACuBxF,SAAzBwE,KAAKjC,UAAUwB,OAAsD/D,SAA/BwE,KAAKjC,UAAUkE,aAAyDzG,SAA5BwE,KAAKjC,UAAUmE,WAG1G7K,GAAG8K,UAAY,SAASC,EAAYC,EAAOC,GACvC,GAAIC,GAASH,EAAW7C,OAAS6C,EAAWF,UAAYE,EAAWH,WAEnE,OAAOM,GAAOtE,KAAKmE,EAAYC,EAAOC,IAG1CjL,GAAGmL,iBAAmB,SAASnE,GAC3B,GAAIoE,GAAa,GACbC,EAAQ,GAAIrF,YAAWgB,EAY3B,OAVAhH,IAAG+C,KAAKsI,EAAO,SAASrI,EAAKsI,GACzB,GAAIC,GAAeD,EAAI3E,SAAS,GAE5B4E,GAAaxF,OAAS,IACtBwF,EAAe,IAAMA,GAGzBH,GAAcG,IAGXH,GAGXpL,GAAGwL,cAAgB,SAASC,EAAMC,EAAa3F,GAC3C,GAAI4F,GAAc3L,GAAG8K,UAAUW,EAAMC,EAAaA,EAAc3F,GAC5D6F,EAAa,GAAIC,YACjBC,EAAU,GAAI9L,IAAG+L,OAUrB,OARAH,GAAWI,OAAS,WAChBF,EAAQG,QAAQjM,GAAGmL,iBAAiBS,EAAWjJ,UAGnDiJ,EAAWM,QAAUJ,EAAQK,QAE7BP,EAAWQ,kBAAkBT,GAEtBG,GAGX9L,GAAG6B,OAAS,SAASY,EAAO4J,EAAQC,GAahC,MAZAtM,IAAG+C,KAAKsJ,EAAQ,SAASE,EAAMtJ,GACvBqJ,GAAgBtM,GAAGuG,SAAStD,IACRkB,SAAhB1B,EAAM8J,KACN9J,EAAM8J,OAEVvM,GAAG6B,OAAOY,EAAM8J,GAAOtJ,GAAK,IAG5BR,EAAM8J,GAAQtJ,IAIfR,GAaXzC,GAAGwM,SAAW,SAASC,EAAQC,GAC3B,GAAIC,MACAC,EAASF,EAASC,EAUtB,OARA3M,IAAG+C,KAAK6J,EAAQ,SAASC,EAAaC,GACN3I,SAAxBsI,EAAOI,KACPF,EAAOE,GAAeJ,EAAOI,IAGjCJ,EAAOI,GAAeC,IAGnBL,GAMXzM,GAAG2F,QAAU,SAASoH,EAAKC,EAAKC,GAC5B,GAAIF,EAAIpH,QACJ,MAAOoH,GAAIpH,QAAQqH,EAAKC,EAG5BA,GAAOA,GAAQ,CACf,IAAIC,GAAMH,EAAIhH,MAMd,KAJIkH,EAAO,IACPA,GAAQC,GAGLD,EAAOC,EAAKD,GAAQ,EACvB,GAAIF,EAAII,eAAeF,IAASF,EAAIE,KAAUD,EAC1C,MAAOC,EAGf,QAAO,GAIXjN,GAAGoN,YAAc,WACb,MAAO,uCAAuC7K,QAAQ,QAAS,SAAS8K,GAEpE,GAAIC,GAAoB,GAAhB3L,KAAK4L,SAAgB,EAAGC,EAAS,KAALH,EAAWC,EAAS,EAAJA,EAAU,CAC9D,OAAOE,GAAE7G,SAAS,OAM1B3G,GAAGyN,GAAK,WACJ,MAAOC,WAAUC,UAAUhI,QAAQ,WAAY,GAC3C+H,UAAUC,UAAUhI,QAAQ,cAAe,GAGnD3F,GAAG4N,IAAM,WACL,MAAOF,WAAUC,UAAUhI,QAAQ,aAAc,GAGrD3F,GAAG6N,IAAM,WACL,MAAOH,WAAUC,UAAUhI,QAAQ,aAAc,GAGrD3F,GAAG8N,KAAO,WACN,MAAOJ,WAAUC,UAAUhI,QAAQ,cAAe,GAGtD3F,GAAG+N,KAAO,WACN,MAAO/N,IAAGyN,MAAQC,UAAUC,UAAUhI,QAAQ,YAAa,GAG/D3F,GAAGgO,KAAO,WACN,MAAON,WAAUC,UAAUhI,QAAQ,SAAW,GAGlD3F,GAAGiO,OAAS,WACR,MAA4B9J,UAArBuJ,UAAUQ,QAAwBR,UAAUQ,OAAOvI,QAAQ,YAAa,GAGnF3F,GAAGmO,OAAS,WACR,MAA4BhK,UAArBuJ,UAAUQ,QAAwBR,UAAUQ,OAAOvI,QAAQ,aAAc,GAGpF3F,GAAGoO,MAAQ,WACP,MAA4BjK,UAArBuJ,UAAUQ,QAAwBR,UAAUQ,OAAOvI,QAAQ,YAAa,GAGnF3F,GAAGqO,QAAU,WACT,OAASrO,GAAGgO,SAAWhO,GAAG+N,QAAUL,UAAUC,UAAUhI,QAAQ,cAAe,GAA2BxB,SAArBuJ,UAAUQ,QAA6C,KAArBR,UAAUQ,QAGrIlO,GAAGsO,QAAU,WACT,MAA8B,UAAvBZ,UAAUa,UAGrBvO,GAAGwO,QAAU,WACT,MAAOd,WAAUC,UAAUrE,cAAc3D,QAAQ,cAAe,GAKpE3F,GAAG2K,aAAe,WACd,MAAO3K,IAAGwO,WAAad,UAAUC,UAAUrE,cAAc3D,QAAQ,UAAY,GAGjF3F,GAAGyO,KAAO,WACN,MAAOzO,IAAG0O,OAAShB,UAAUC,UAAUhI,QAAQ,aAAc,GAGjE3F,GAAG2O,KAAO,WACN,MAAO3O,IAAG0O,OAAShB,UAAUC,UAAUhI,QAAQ,aAAc,GAGjE3F,GAAG4O,KAAO,WACN,MAAO5O,IAAG0O,OAAShB,UAAUC,UAAUhI,QAAQ,aAAc,GAIjE3F,GAAG6O,OAAS,WACR,MAAO7O,IAAG0O,OAAShB,UAAUC,UAAUhI,QAAQ,eAAgB,GAGnE3F,GAAG0O,IAAM,WAEL,MAAOhB,WAAUC,UAAUhI,QAAQ,WAAY,GACxC+H,UAAUC,UAAUhI,QAAQ,WAAY,GACxC+H,UAAUC,UAAUhI,QAAQ,aAAc,GAGrD3F,GAAG8O,UAAY,WACX,MAAO9O,IAAG0O,OAAShB,UAAUC,UAAUhI,QAAQ,YAAa,GAGhE3F,GAAG+O,UAAY,WACX,MAAO/O,IAAG0O,QAAU1O,GAAG8O,aAAepB,UAAUC,UAAUhI,QAAQ,aAAc,GAGpF3F,GAAGgP,iBAAmB,WAClB,MAAOhP,IAAG0O,QAAU1O,GAAG8O,cAAgB9O,GAAG+O,aAM9C/O,GAAGiP,eAAiB,SAASC,GACrBA,EAAED,eACFC,EAAED,iBAEFC,EAAEC,aAAc,GAQxBnP,GAAGoP,UAAa,WACZ,GAAIC,GAAMxF,SAASC,cAAc,MACjC,OAAO,UAASwF,GACZD,EAAIE,UAAYD,CAChB,IAAIrP,GAAUoP,EAAI/L,UAElB,OADA+L,GAAIhO,YAAYpB,GACTA,MAKfD,GAAG+C,KAAO,SAASyM,EAAcC,GAC7B,GAAIC,GAAYC,CAEhB,IAAIH,EAEA,GAAItK,OAAO0K,SAAWJ,EAAavI,cAAgB/B,OAAO0K,QACtD,IAAKF,EAAa,EAAGA,EAAaF,EAAazJ,SAC3C4J,EAASF,EAASD,EAAaK,IAAIH,GAAaF,EAAaM,QAAQN,EAAaK,IAAIH,KAClFC,KAAW,GAFoCD,SAStD,IAAI1P,GAAG8G,QAAQ0I,IAAiBxP,GAAGkH,WAAWsI,IAAiBxP,GAAGoH,WAAWoI,GAC9E,IAAKE,EAAa,EAAGA,EAAaF,EAAazJ,SAC3C4J,EAASF,EAASC,EAAYF,EAAaE,IACvCC,KAAW,GAFoCD,SAOtD,IAAI1P,GAAGwH,SAASgI,GACjB,IAAKE,EAAa,EAAGA,EAAaF,EAAazJ,SAC3C4J,EAASF,EAASC,EAAYF,EAAaO,OAAOL,IAC9CC,KAAW,GAFoCD,SAQvD,KAAKA,IAAcF,GACf,GAAI/I,OAAOC,UAAUyG,eAAevG,KAAK4I,EAAcE,KACnDC,EAASF,EAASC,EAAYF,EAAaE,IACvCC,KAAW,GACX,OASxB3P,GAAGgQ,KAAO,SAASC,EAASC,GACxB,GAAIlQ,GAAG6G,WAAWoJ,GAAU,CACxB,GAAIjI,GAAQC,MAAMvB,UAAUwB,MAAMtB,KAAKuB,UAAW,EAElD,OAAO,YACH,GAAIgI,GAAUnQ,GAAG6B,UAAWmG,EAI5B,OAHIG,WAAUpC,SACVoK,EAAUA,EAAQC,OAAOnI,MAAMvB,UAAUwB,MAAMtB,KAAKuB,aAEjD8H,EAAQI,MAAMH,EAASC,IAItC,KAAM,IAAI3O,OAAM,wCAmBpBxB,GAAGsQ,QAAU,SAASC,EAAKC,EAAMC,GAE7B,GAAIC,MACAC,EAAS,IACTC,EAAM,SAASC,EAASC,GACpB,GAAIC,GAAWP,EACR,QAAQrO,KAAKqO,GACdA,EACAA,EAAO,IAAMM,EAAI,IACjBA,CACY,eAAbC,GAAoC,cAAND,GAC/BJ,EAAWxN,KACa,gBAAZ2N,GACF7Q,GAAGsQ,QAAQO,EAASE,GAAU,GACe,sBAA5CtK,OAAOC,UAAUC,SAASC,KAAKiK,GAChCG,mBAAmBD,GAAY,IAAMC,mBAAmBH,KACxDG,mBAAmBD,GAAY,IAAMC,mBAAmBH,IAqB9E,QAhBKJ,GAAcD,GACfG,EAAU,KAAKxO,KAAKqO,GAAU,MAAMrO,KAAKqO,GAAS,GAAK,IAAM,IAC7DE,EAAWxN,KAAKsN,GAChBE,EAAWxN,KAAKlD,GAAGsQ,QAAQC,KACqB,mBAAxC9J,OAAOC,UAAUC,SAASC,KAAK2J,IAA8C,mBAARA,GAC7EvQ,GAAG+C,KAAKwN,EAAK,SAASvN,EAAKC,GACvB2N,EAAI3N,EAAKD,KAEU,mBAARuN,IAAiC,OAARA,GAAiC,gBAARA,GACjEvQ,GAAG+C,KAAKwN,EAAK,SAAShE,EAAMtJ,GACxB2N,EAAI3N,EAAKsJ,KAGbmE,EAAWxN,KAAK8N,mBAAmBR,GAAQ,IAAMQ,mBAAmBT,IAGpEC,EACOE,EAAWO,KAAKN,GAEhBD,EAAWO,KAAKN,GAClBpO,QAAQ,KAAM,IACdA,QAAQ,OAAQ,MAI7BvC,GAAGkR,aAAe,SAASX,EAAKY,EAAUC,GAmBtC,MAlBKD,KACDA,EAAW,GAAInH,WAGnBhK,GAAG+C,KAAKwN,EAAK,SAASV,EAAK5M,GACvB4M,EAAMuB,EAAeA,EAAe,IAAMvB,EAAM,IAAMA,EAElD7P,GAAGuG,SAAStD,GACZjD,GAAGkR,aAAajO,EAAKkO,EAAUtB,GAE1B7P,GAAG6G,WAAW5D,GACnBkO,EAAS5L,OAAOsK,EAAK5M,KAGrBkO,EAAS5L,OAAOsK,EAAK5M,KAItBkO,GAGXnR,GAAGqR,WAAa,SAASd,EAAKe,GAC1B,GAAI1H,EAeJ,OAbK0H,KACDA,EAAOzH,SAASC,cAAc,SAGlC9J,GAAGkR,aAAaX,GACZhL,OAAQ,SAASsK,EAAK5M,GAClB2G,EAAQC,SAASC,cAAc,SAC/BF,EAAM2H,aAAa,OAAQ1B,GAC3BjG,EAAM2H,aAAa,QAAStO,GAC5BqO,EAAKE,YAAY5H,MAIlB0H,GAOXtR,GAAGyR,UAAY,SAASC,MAEpB,MAAIxM,QAAOyM,MAAQ3R,GAAG6G,WAAW8K,KAAKC,OAC3BD,KAAKC,MAAMF,MAEXG,KAAK,IAAMH,KAAO,MAUjC1R,GAAG8R,aAAe,SAASC,GACvB,GAAIC,GAASD,EAASE,YAAY,KAAO,CAEzC,IAAID,EAAS,EACT,MAAOD,GAASG,OAAOF,EAAQD,EAAShM,OAASiM,IAIzDhS,GAAGmS,YAAc,SAASC,GAGtB,MAAIpS,IAAGiJ,QAAQmJ,GAEJA,EAAgBrL,MAAMxE,QAAQ,YAAa,IAE7CvC,GAAGyI,OAAO2J,IACkB,OAA7BA,EAAgBC,UAAkDlO,SAA7BiO,EAAgBC,SAC9CD,EAAgBC,SAIxBD,EAAgBrQ,MAM3B/B,GAAGsS,eAAiB,WAChB,GAAIC,KAEJ,QAEIC,QAAS,WACL,GAAIC,EACJ,GACIA,GAAWF,EAAUG,QACjBD,GACAA,UAGDA,IAIXnS,OAAQ,WACJ,GAAI0H,GAAOG,SAEX9H,MAAKsS,YAAY3S,GAAGgI,EAAK,IAAI1H,OAAO+P,MAAMhQ,KAAM4H,MAAMvB,UAAUwB,MAAMtB,KAAKuB,UAAW,MAI1FwK,YAAa,SAASC,GAClBL,EAAUrP,KAAK0P,SCt2B9B,WACG,YACsB,mBAAXC,SAAyBA,OAAOC,IACvCD,OAAO,WACH,MAAO7S,MAGY,mBAAX+S,SAA0BA,OAAOC,QAC7CD,OAAOC,QAAUhT,GAGjBD,OAAOC,GAAKA,MCRnB,WACG,YAEAA,IAAGwB,MAAQ,SAAS4E,GAChB/F,KAAK+F,QAAU,kBAAoBpG,GAAGiT,QAAU,KAAO7M,GAG3DpG,GAAGwB,MAAMkF,UAAY,GAAIlF,UCV7BxB,GAAGiT,QAAU,SCAbjT,GAAGkT,kBAAqB,WACpB,YAkBA,SAASC,KACL,GACIC,GADAC,GAAY,CAGhB,KACID,EAAYvJ,SAASC,cAAc,SACnCsJ,EAAU7S,KAAO,OACjBP,GAAGoT,GAAWlT,OAEVkT,EAAUE,WACVD,GAAY,GAGpB,MAAOE,GACHF,GAAY,EAGhB,MAAOA,GAIX,QAASG,KACL,OAAQxT,GAAGmO,UAAYnO,GAAGoO,UAC+CjK,SAArEuJ,UAAUC,UAAU8F,MAAM,uCAIlC,QAASC,KACL,OAAQ1T,GAAGmO,UAAYnO,GAAGoO,UAC+CjK,SAArEuJ,UAAUC,UAAU8F,MAAM,uCAIlC,QAASE,KACL,GAAIzO,OAAOiF,eAAgB,CACvB,GAAIyJ,GAAM5T,GAAGiK,mBAGb,OAA+B9F,UAAxByP,EAAIC,gBAGf,OAAO,EAIX,QAASC,KACL,MAAiC3P,UAA1Be,OAAO6O,eAKlB,QAASC,KACL,QAAIL,KAIGG,IAGX,QAASG,KAEL,MAA2D9P,UAApD0F,SAASC,cAAc,SAASoK,gBAG3C,QAASC,KACL,IACI,QAASjP,OAAOkP,cAEZpU,GAAG6G,WAAW3B,OAAOkP,aAAaC,SAE1C,MAAOhK,GAEH,OAAO,GAIf,QAASiK,KACL,GAAIC,GAAO1K,SAASC,cAAc,OAElC,QAAQ,aAAeyK,IAAS,eAAiBA,IAAQ,UAAYA,MAChEvU,GAAGwO,YAAcxO,GAAG0O,MAhG7B,GAAI8F,GACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,CAwHJ,OAnCAd,GAAoBrB,IAEpBwB,EAA4BH,GAAqBxU,GAAG2J,uBAEpD8K,EAAyBE,IAA8B3U,GAAG2K,eAE1D+J,EAAmBC,GAA6BL,IAEhDM,EAAqBF,GAAoBlB,IAEzCqB,EAAmBF,GAA6B3U,GAAG0K,0BAEnDoK,EAAiBH,GAA6BE,GAAoBV,IAElEY,EAAyBJ,GAA6BjB,IAEtDsB,EAAqBR,IAA6CrQ,SAAvBe,OAAOqQ,aAA6BZ,GAE/EO,EAA4BvB,IAE5BsB,EAAwBnB,IAExBqB,EAAyBnB,IAEzBoB,EAA0BnB,IAE1BoB,EAAwBV,GAAmDxQ,SAAtBe,OAAO2G,WAE5DyJ,EAA0B,WACtB,QAAIX,KACQ3U,GAAG2K,iBAAmB3K,GAAG8O,iBAMrC0G,cAAeb,EACfc,cAAehB,EACfiB,iBAAkBf,EAClBgB,SAAUd,EACVe,eAAgBT,EAChBU,kBAAmBZ,EACnBa,kBAAmBZ,EACnBa,gBAAiB7Q,OAAO8Q,kBACxBC,SAAUvB,EACVwB,WAAYtB,EACZuB,gBAAiBf,EACjBgB,cAAef,EACfgB,gBAAiBhB,EACjBiB,mBAAoB3B,EACpB4B,MAAO1B,EACP2B,YAAalB,EACbmB,OAAQ3B,EACR4B,QAASrB,GAAyBZ,EAClCkC,aAAc3W,GAAGiO,SACjB2I,0BAA2B5W,GAAG0O,MAC9BmI,UAAWrC,EACXsC,WAAY9B,EACZ+B,oBAAqBpC,EACrBqC,mBAAoBrC,EACpBsC,eAAgBlC,MChKxB/U,GAAGkX,iBAAmB,SAASC,GAC3B,YACA,UAAUA,GAAgBA,EAAaC,MAAQpX,GAAG6G,WAAWsQ,EAAaC,QAG9EpX,GAAG+L,QAAU,WACT,YAEA,IAAIsL,GAAaC,EACbC,KACAC,KACAC,KACAC,EAAQ,CAEZ1X,IAAG6B,OAAOxB,MACN+W,KAAM,SAASO,EAAWC,GAgBtB,MAfc,KAAVF,GACIC,GACAJ,EAAiBrU,KAAKyU,GAEtBC,GACAJ,EAAiBtU,KAAK0U,IAGrBF,KAAU,EACfE,GAAaA,EAAUvH,MAAM,KAAMiH,GAE9BK,GACLA,EAAUtH,MAAM,KAAMgH,GAGnBhX,MAGXwX,KAAM,SAASpI,GAQX,MAPc,KAAViI,EACAD,EAAcvU,KAAKuM,GAGnBA,EAASY,MAAM,KAAsBlM,SAAhBmT,EAA4BD,EAAcC,GAG5DjX,MAGX4L,QAAS,WAgBL,MAfAyL,GAAQ,EACRL,EAAclP,UAEVoP,EAAiBxR,QACjB/F,GAAG+C,KAAKwU,EAAkB,SAASvU,EAAKyM,GACpCA,EAASY,MAAM,KAAMgH,KAIzBI,EAAc1R,QACd/F,GAAG+C,KAAK0U,EAAe,SAASzU,EAAKyM,GACjCA,EAASY,MAAM,KAAMgH,KAItBhX,MAGX8L,QAAS,WAgBL,MAfAuL,IAAQ,EACRJ,EAAcnP,UAEVqP,EAAiBzR,QACjB/F,GAAG+C,KAAKyU,EAAkB,SAASxU,EAAKyM,GACpCA,EAASY,MAAM,KAAMiH,KAIzBG,EAAc1R,QACd/F,GAAG+C,KAAK0U,EAAe,SAASzU,EAAKyM,GACjCA,EAASY,MAAM,KAAMiH,KAItBjX,SC3EnBL,GAAG8X,UAAY,SAASC,EAAeC,GACnC,YAEAhY,IAAG6B,OAAOxB,MACN0X,cAAeA,EAEfE,OAAQ,WACJ,MAAOD,GAASD,OCF5B/X,GAAGkY,aAAe,SAASC,GACvB,YA0CA,SAASC,KACL,GAAIxO,GAAQC,SAASC,cAAc,QAmEnC,OAjEAF,GAAM2H,aAAavR,GAAGkY,aAAaG,oBAAqBC,GACxD1O,EAAM2H,aAAa,QAASgH,EAAQC,OAEpCC,EAAKC,YAAYH,EAAQxO,SAAUH,GAE/B2O,EAAQI,SAAW3Y,GAAGkT,kBAAkBiD,iBAExCvM,EAAM2H,aAAa,kBAAmB,IAGtCgH,EAAQK,aACRhP,EAAM2H,aAAa,SAAUgH,EAAQK,aAGzChP,EAAM2H,aAAa,OAAQ,QAC3B3H,EAAM2H,aAAa,OAAQgH,EAAQxW,MAEnC/B,GAAG4J,GAAOtI,KACNuX,SAAU,WAIVC,MAAO,EACPC,IAAK,EACLC,WAAY,QASZC,SAAUjZ,GAAGyN,OAASzN,GAAG6N,MAAQ,SAAW,QAC5CqL,OAAQ,EACRC,QAAS,EACTC,OAAQ,UACR3X,QAAS,KAKZzB,GAAG4N,OAAS5N,GAAG4J,GAAOtI,KAAK+X,OAAQ,SAEpCd,EAAQtY,QAAQuR,YAAY5H,GAE5B0P,EAAehZ,OAAOsJ,EAAO,SAAU,WACnC2O,EAAQgB,SAAS3P,KAIrB0P,EAAehZ,OAAOsJ,EAAO,YAAa,WACtC5J,GAAGuY,EAAQtY,SAASoC,SAASkW,EAAQiB,cAEzCF,EAAehZ,OAAOsJ,EAAO,WAAY,WACrC5J,GAAGuY,EAAQtY,SAASqC,YAAYiW,EAAQiB,cAG5CF,EAAehZ,OAAOsJ,EAAO,QAAS,WAClC5J,GAAGuY,EAAQtY,SAASoC,SAASkW,EAAQkB,cAEzCH,EAAehZ,OAAOsJ,EAAO,OAAQ,WACjC5J,GAAGuY,EAAQtY,SAASqC,YAAYiW,EAAQkB,cAGrC7P,EA5GX,GAgCIA,GAAO0O,EAhCPG,EAAOpY,KAEPiZ,EAAiB,GAAItZ,IAAGsS,eAExBiG,GAEIK,YAAa,KAGb3Y,QAAS,KAETwZ,WAAY,yBAGZd,SAAS,EAGTa,WAAY,yBAEZE,4BAA4B,EAG5B3P,UAAU,EAGVhI,KAAM,SAGNwX,SAAU,SAAS3P,KAEnB4O,MAAO,KAKfxY,IAAG6B,OAAO0W,EAASJ,GAEnBG,EAAWtY,GAAGoN,cA2EdpN,GAAGuY,EAAQtY,SAASqB,KAChBuX,SAAU,WACVc,SAAU,SAEVC,UAAW,QAIf5Z,GAAG6B,OAAOxB,MACNwZ,SAAU,WACN,MAAOjQ,IAGXkQ,YAAa,WACT,MAAOxB,IAGXI,YAAa,SAASqB,EAAYC,GAC9B,GAAIpQ,GAAQoQ,GAAY3Z,KAAKwZ,UAKzBtB,GAAQmB,4BAA8B1Z,GAAG4O,SAAW5O,GAAG8O,aAAe9O,GAAGgP,oBACzEpF,EAAM2H,aAAa,WAAY,IAI3BwI,EACAnQ,EAAM2H,aAAa,WAAY,IAG/B3H,EAAMqQ,gBAAgB,aAKlCC,eAAgB,SAAStB,GACjBA,IAAgBL,EAAQK,aACxBhP,EAAM2H,aAAa,SAAUqH,IAIrCuB,MAAO,WACCvQ,EAAMzI,YACNnB,GAAG4J,GAAOxI,SAGdpB,GAAGuY,EAAQtY,SAASqC,YAAYiW,EAAQkB,YACxC7P,EAAQ,KACRA,EAAQwO,OAIhBxO,EAAQwO,KAGZpY,GAAGkY,aAAaG,oBAAsB,eCxLtCrY,GAAGoa,WAAa,SAASC,GACrB,YAQA,SAASC,GAAaC,GAClB,GAAIva,GAAG8G,QAAQyT,GAAU,CACrB,GAAIC,KAMJ,OAJAxa,IAAG+C,KAAKwX,EAAS,SAASvX,EAAKyX,GAC3BD,EAAQtX,KAAK8B,EAAKyV,MAGfD,EAGX,MAAOxV,GAAKuV,GAGhB,QAASG,GAAeC,GACpB,GAAI3a,GAAG8G,QAAQ6T,GAAQ,CACnB,GAAIH,KAMJ,OAJAxa,IAAG+C,KAAK4X,EAAO,SAAS3X,EAAK4X,GACzBJ,EAAQtX,KAAK8B,EAAK6V,EAAOD,OAGtBJ,EAGX,MAAOxV,GAAK6V,EAAOF,IAGvB,QAASG,GAAgBC,GACrB,GAAIC,MACAC,KAAc7K,OAAO2K,EAYzB,OAVA/a,IAAG+C,KAAKkY,EAAU,SAASC,EAAOC,GAC9B,GAAIC,GAAsBC,EAASF,EAEPhX,UAAxBiX,GACApb,GAAG+C,KAAKqY,EAAqB,SAAStK,EAAGwK,GACrCN,EAAc9X,KAAK8B,EAAKsW,QAK7BN,EAhDX,GAAIhW,MACA6V,KACAQ,KACAE,KACAC,IA+CJxb,IAAG6B,OAAOxB,MAeNob,QAAS,SAASC,GACd,GAAIX,GAASW,EAAKX,QAAU/a,GAAG+a,OAAOY,WAClClB,EAAKzV,EAAK9B,MACNnB,KAAM2Z,EAAK3Z,KACX6Z,aAAcF,EAAK3Z,KACnB6Y,KAAMc,EAAKd,KACXiB,KAAmB,MAAbH,EAAKG,MAAe,EAAKH,EAAKG,KACpCd,OAAQA,IACP,CA8BT,OA5BIW,GAAKI,UACL9W,EAAKyV,GAAIqB,QAAUJ,EAAKI,QAEQ3X,SAA5BqX,EAAUE,EAAKI,WACfN,EAAUE,EAAKI,aAEnBN,EAAUE,EAAKI,SAAS5Y,KAAKuX,IAG7BiB,EAAKK,eACL/W,EAAKyV,GAAIsB,aAAeL,EAAKK,aAEa5X,SAAtCoX,EAAeG,EAAKK,gBACpBR,EAAeG,EAAKK,kBAExBR,EAAeG,EAAKK,cAAc7Y,KAAKuX,IAG3CzV,EAAKyV,GAAIA,GAAKA,EACdI,EAAOa,EAAKd,MAAQH,EAEKtW,SAArBkX,EAASN,KACTM,EAASN,OAEbM,EAASN,GAAQ7X,KAAKuX,GAEtBJ,EAAc2B,eAAevB,EAAI,KAAMM,GAEhCN,GAGXwB,SAAU,SAASC,GACf,MAAIlc,IAAGuG,SAAS2V,IAAmBlX,EAAKe,OACV5B,SAAtB+X,EAAezB,GACRH,EAAa4B,EAAezB,IAGNtW,SAAxB+X,EAAetB,KACbF,EAAewB,EAAetB,MAGhCsB,EAAenB,OACbD,EAAgBoB,EAAenB,QADrC,OAKE/a,GAAG6B,UAAWmD,GAAM,IAInCmV,MAAO,WACHnV,KACA6V,KACAQ,KACAG,MAGJW,UAAW,SAAS1B,EAAI2B,GACpB,GAAIC,GAAYrX,EAAKyV,GAAIM,OACrBuB,EAAyBtc,GAAG2F,QAAQ0V,EAASgB,GAAY5B,EAE7DY,GAASgB,GAAWE,OAAOD,EAAwB,GAEnDtX,EAAKyV,GAAIM,OAASqB,EAEUjY,SAAxBkX,EAASe,KACTf,EAASe,OAEbf,EAASe,GAAWlZ,KAAKuX,GAEzBJ,EAAc2B,eAAevB,EAAI4B,EAAWD,IAGhDI,YAAa,SAAS/B,EAAIgC,GACtB,GAAIC,GAAU1X,EAAKyV,GAAIG,IAEvB5V,GAAKyV,GAAIG,KAAO6B,EAChB5B,EAAO4B,GAAWhC,QACXI,GAAO6B,IAGlBC,WAAY,SAASlC,EAAImC,GACrB5X,EAAKyV,GAAI1Y,KAAO6a,GAGpBC,WAAY,SAASpC,EAAIqC,GACrB9X,EAAKyV,GAAIoB,KAAOiB,GAIpBC,YAAa,SAASC,EAAUC,GAC5BjY,EAAKgY,GAAUC,SAAWA,GAG9BC,mBAAoB,SAASzC,GACzB,GAAIsB,GAAe/W,EAAKyV,GAAIsB,YAE5B,OAAIA,GACOR,EAAeQ,OAK9BoB,cAAe,SAAS1C,GACpB,GAAIqB,GAAU9W,EAAKyV,GAAIqB,OAEvB,OAAON,GAAUM,OAK7B9b,GAAG+a,QACCY,WAAY,aACZyB,UAAW,YACXC,SAAU,WACVC,OAAQ,SACRC,SAAU,WACVC,OAAQ,SACRC,UAAW,YACXC,gBAAiB,kBACjBC,kBAAmB,oBACnBC,cAAe,gBACfC,cAAe,gBACfC,SAAU,WACVC,QAAS,WCxMZ,WACG,YAEA/d,IAAGge,eAECC,SAAU,SAASC,EAAiBC,EAAQC,GACxC/d,KAAKge,SAASH,EAAiBC,EAAQC,IAG3CE,gBAAiB,SAASC,GACtB,GAAI9F,GAAOpY,IAEXL,IAAG+C,KAAKwb,EAAgB,SAASrD,EAAOsD,GACpC/F,EAAKgG,eAAeD,MAI5BH,SAAU,SAASrZ,EAAMmZ,EAAQC,GAC7B/d,KAAKqe,kCAEL,IAAI5C,GAAqC,IAA3Bzb,KAAKse,WAAW5Y,OAAe/F,GAAGoN,cAAgB/M,KAAKue,gBAEjEC,EAAc7e,GAAGgQ,KAAK,SAASvE,GAC3BpL,KAAKye,gBACDrT,KAAMA,EACN1J,KAAM1B,KAAK0e,SAASC,MAAMC,aAC3BnD,EAASoD,IACb7e,MAEH8e,EAAkBnf,GAAGgQ,KAAK,SAASoP,GAC/B/e,KAAKye,eAAeM,EAAUtD,EAASoD,IACxC7e,MAEHgf,EAAgBrf,GAAGgQ,KAAK,SAAS3L,GAC7B,GAAIoH,GAAOzL,GAAGoE,aAAaC,EAE3BhE,MAAKye,gBACDrT,KAAMA,EACN1J,KAAM1B,KAAK0e,SAASC,MAAMC,YAAc,QACzCnD,EAASoD,IACb7e,MAEHif,EAAoBtf,GAAGgQ,KAAK,SAASuP,GACjC,GAAIC,GAAoBD,EAAWhb,SAAWgb,EAAWhb,QAAU,IAC/DkH,EAAOzL,GAAGoE,aAAamb,EAAWlb,OAAQkb,EAAWhf,KAAMif,EAE/Dnf,MAAKye,gBACDrT,KAAMA,EACN1J,KAAMwd,EAAWxd,MAClB+Z,EAASoD,IACb7e,MAEHof,EAAqBzf,GAAGgQ,KAAK,SAAS0P,GAClC,GAAI1f,GAAGiJ,QAAQyW,IAAgB1f,GAAGkT,kBAAkBsC,cAAe,CAC/D,GAAImK,GAAQ1X,MAAMvB,UAAUwB,MAAMtB,KAAK8Y,EAAYC,OAC/ClH,EAAOpY,IAEXL,IAAG+C,KAAK4c,EAAO,SAAS3c,EAAK4c,GACzBnH,EAAKqG,eAAec,EAAM9D,EAASoD,SAIvC7e,MAAKye,eAAeY,EAAa5D,EAASoD,IAE/C7e,MAEHwf,EAAgB,WACR7f,GAAG4I,WAAW5D,KACdA,EAAOiD,MAAMvB,UAAUwB,MAAMtB,KAAK5B,IAEtCA,KAAUoL,OAAOpL,IAGrByT,EAAOpY,KACP6e,IAEJ7e,MAAKue,gBAAkB9C,EAEnB9W,IACA6a,IAEA7f,GAAG+C,KAAKiC,EAAM,SAAShC,EAAK8c,GACpB9f,GAAG+I,cAAc+W,GACjBL,EAAmBK,GAEd9f,GAAGyJ,OAAOqW,GACfjB,EAAYiB,GAEP9f,GAAGuG,SAASuZ,GACbA,EAAcrU,MAAQqU,EAAc/d,KACpCod,EAAgBW,GAEXA,EAAczb,QAAUyb,EAAc/d,MAC3Cud,EAAkBQ,GAGjBA,EAActW,SAAmD,WAAxCsW,EAActW,QAAQF,cACpD+V,EAAcS,GAGdrH,EAAKtS,IAAI2Z,EAAgB,6CAA8C,UAI/Ezf,KAAK8F,IAAI,YAAc+Y,EAAcnZ,OAAS,WAC9C1F,KAAK0f,uBAAuBb,EAAef,EAAQC,KAI3D4B,OAAQ,SAASvF,GACbpa,KAAK4f,SAASD,OAAOvF,IAGzByF,UAAW,WACP,GAAIC,MACA1H,EAAOpY,IAEXL,IAAG6B,OAAOse,EAAe9f,KAAKse,YAC9B3e,GAAG+C,KAAKod,EAAe,SAASnd,EAAKod,GACjC3H,EAAKuH,OAAOI,KAGhB/f,KAAK4f,SAASC,aAGlBG,iBAAkB,WACdhgB,KAAKse,eAGT2B,eAAgB,SAAS7F,GACrB,GAAI8F,GAAalgB,KAAKmgB,YAAYvE,UAAUxB,GAAIA,GAEhD,UAAKza,GAAGkT,kBAAkBqD,QAAUlW,KAAK0e,SAASpJ,SAAS8K,WAIvDF,EAAWxF,SAAW/a,GAAG+a,OAAOyC,QAChCnd,KAAK8F,IAAInG,GAAG8H,OAAO,yDAA0D2S,EAAIpa,KAAKqgB,QAAQjG,KAC9Fpa,KAAKsgB,YAAYlG,IACV,IAGPpa,KAAK8F,IAAInG,GAAG8H,OAAO,sDAAuD2S,EAAIpa,KAAKqgB,QAAQjG,IAAM,UAG9F,KAGXmG,WAAY,SAASnG,GACjB,MAAOpa,MAAKwgB,gBAAgBpG,IAIhCqG,UAAW,SAASC,GAChB,MAAO1gB,MAAK4f,SAASe,QAAQD,IAOjCE,cAAe,SAASC,EAAQC,EAAaC,EAASC,EAAYC,GAC9D,GACIC,GAAWhJ,EADXiJ,EAAkB,GAAIxhB,IAAG+L,OAqC7B,OAlCI1L,MAAKohB,iBACLF,EAAYlhB,KAAKqhB,eAAeR,GAChC3I,GACI+I,qBAAsBA,EACtBF,QAASA,EAAU,EAAIA,EAAU,KACjCO,MAAOP,EAAU,IAKhBC,GAAcrhB,GAAGkT,kBAAkBkD,gBACpCmL,EAAYlhB,KAAKuhB,QAAQV,IAIZ,MAAbK,EACAC,EAAgBrV,SAAS0V,UAAWV,EAAa9W,MAAO,2BAGxDhK,KAAKohB,gBAAgBK,SAASP,EAAWJ,EAAa5I,GAASnB,KAC3D,SAAiB2K,GACbP,EAAgBvV,QAAQ8V,IAG5B,SAAiBF,EAAWG,GACxBR,EAAgBrV,SAAS0V,UAAWA,EAAWxX,MAAO2X,GAAU,oCAM5ER,EAAgBrV,SAAS0V,UAAWV,EAAa9W,MAAO,mCAGrDmX,GAGXS,UAAW,SAASf,GAChB,MAAO7gB,MAAK6hB,WAAW7hB,KAAK8hB,qBAAqBjB,KAGrDkB,YAAa,SAASlB,GAClB,MAAO7gB,MAAKgiB,eAAeC,IAAIpB,IAGnCU,QAAS,SAASb,GACd,MAAO1gB,MAAK4f,SAAS2B,QAAQb,IAAiB,MAGlDwB,cAAe,WACX,MAAOliB,MAAKmgB,YAAYvE,UACpBlB,QACI/a,GAAG+a,OAAO0C,UACVzd,GAAG+a,OAAO2C,gBACV1d,GAAG+a,OAAOuC,UAEfvX,QAGP2a,QAAS,SAASjG,GACd,MAAOpa,MAAKmgB,YAAYvE,UAAUxB,GAAIA,IAAK1Y,MAI/CygB,YAAa,SAAS/H,GAClB,GAAIgI,GAAkBpiB,KAAKqiB,YAAYjI,GAAIA,IACvCwC,EAAW,IAQf,OANIwF,IACiCte,SAA7Bse,EAAgBxF,WAChBA,EAAWwF,EAAgBxF,UAI5BA,GAGX0F,sBAAuB,WACnB,MAAOtiB,MAAK4f,SAAS0C,yBAGzBC,QAAS,SAASnI,GACd,MAAOpa,MAAKmgB,YAAYvE,UAAUxB,GAAIA,IAAKoB,MAG/CgH,cAAe,WACX,MAAOxiB,MAAKyiB,cAGhBC,yBAA0B,WACtB,GAAIC,GAAe3iB,KAAK4iB,iBAExB,OAAID,GAAe,EACRA,EAAe3iB,KAAK6iB,qBAGxB,MAGXR,WAAY,SAASxG,GACjB,MAAO7b,MAAKmgB,YAAYvE,SAASC,IAGrCiH,QAAS,SAAS1I,GACd,MAAOpa,MAAKmgB,YAAYvE,UAAUxB,GAAIA,IAAKG,MAG/CzU,IAAK,SAAS4B,EAAK1B,IACXhG,KAAK0e,SAASqE,OAAW/c,GAAmB,SAAVA,EAG7BA,GAAmB,SAAVA,GACdrG,GAAGmG,IAAI,kBAAoBnG,GAAGiT,QAAU,KAAOlL,EAAK1B,GAHpDrG,GAAGmG,IAAI,kBAAoBnG,GAAGiT,QAAU,KAAOlL,IAQvDsb,YAAa,SAAS5I,GAClB,GAAI8F,GAAalgB,KAAKmgB,YAAYvE,UAAUxB,GAAIA,GAEhD,KAAKza,GAAGkT,kBAAkBqD,QAAUlW,KAAK0e,SAASpJ,SAAS8K,QACvD,OAAO,CAIX,IAAIzgB,GAAG2F,SAAS3F,GAAG+a,OAAO0C,UAAWzd,GAAG+a,OAAO2C,iBAAkB6C,EAAWxF,SAAW,EAAG,CACtF,GAAI1a,KAAK4f,SAAS1J,MAAMkE,GAEpB,MADApa,MAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAOyC,SAClC,CAGPnd,MAAK8F,IAAInG,GAAG8H,OAAO,mCAAoC2S,EAAIpa,KAAKqgB,QAAQjG,IAAM,aAIlFpa,MAAK8F,IAAInG,GAAG8H,OAAO,wDAAyD2S,EAAIpa,KAAKqgB,QAAQjG,IAAM,QAGvG,QAAO,GAGXN,MAAO,WACH9Z,KAAK8F,IAAI,yBAET9F,KAAK4f,SAAS9F,QACd9Z,KAAKse,cACLte,KAAKijB,gBACLjjB,KAAKkjB,kBACLljB,KAAKmjB,mBACLnjB,KAAKqhB,kBAEL1hB,GAAG+C,KAAK1C,KAAKojB,SAAU,SAASzgB,EAAK0gB,GACjCA,EAAOvJ,UAGX9Z,KAAKsjB,aAAaxJ,QAClB9Z,KAAKgiB,eAAelI,QACpB9Z,KAAK6iB,qBAAuB,EAC5B7iB,KAAKyiB,aAAe,EACpBziB,KAAKmgB,YAAYrG,QACjB9Z,KAAK8hB,wBAEL9hB,KAAKujB,eAAiBvjB,KAAKujB,cAAczJ,QACzC9Z,KAAK0e,SAAS8E,QAAQC,gBAAkBzjB,KAAK0jB,sBAE7C1jB,KAAK2jB,kCACL3jB,KAAK4jB,+BAEL5jB,KAAK6jB,gBAAkB7jB,KAAK6jB,eAAe/J,SAG/CgK,MAAO,SAAS1J,GACZ,MAAOpa,MAAK+jB,aAAa3J,IAG7B4J,WAAY,SAAS5J,EAAI6J,GACrB,GAAI7L,GAAOpY,IAEX,OAAOL,IAAGukB,OAAO7d,UAAU2d,WAAW5J,EAAI6J,GACtCne,IAAKnG,GAAGgQ,KAAKyI,EAAKtS,IAAKsS,GACvBmJ,QAAS5hB,GAAGgQ,KAAKyI,EAAKmJ,QAASnJ,GAC/B8H,WAAY9H,EAAK+H,eAIzBgE,iBAAkB,SAASC,EAAShK,GAChCpa,KAAKqkB,oBAAoBC,IAAIF,EAAShK,IAG1CmK,2BAA4B,SAASH,EAAShK,GAC1Cpa,KAAKwkB,8BAA8BF,IAAIF,EAAShK,IAGpDqK,sBAAuB,SAAS1G,EAAU3D,GACtCpa,KAAK0kB,yBAAyBJ,IAAIvG,EAAU3D,IAGhDuK,oBAAqB,SAAS7G,EAAQ1D,GAClCpa,KAAK4kB,uBAAuBN,IAAIxG,EAAQ1D,IAI5CyK,YAAa,SAAS9G,EAAU3D,GAC5Bpa,KAAKgiB,eAAesC,IAAIvG,EAAU3D,IAGtC0K,QAAS,SAASC,GACd/kB,KAAKglB,4BAA4BD,IAGrCE,aAAc,SAASC,GACnBllB,KAAK4iB,kBAAoBsC,GAG7BC,QAAS,SAAS/K,EAAImC,GAClBvc,KAAKmgB,YAAY7D,WAAWlC,EAAImC,IAGpC6I,UAAW,SAAStH,EAAQ1D,GACxBpa,KAAKsjB,aAAagB,IAAIxG,EAAQ1D,IAGlCiL,QAAS,SAASjL,EAAIgC,GAClB,MAAOpc,MAAKmgB,YAAYhE,YAAY/B,EAAIgC,IAG5CkJ,kBAAmB,WACgB,IAA3BtlB,KAAKse,WAAW5Y,OAChB1F,KAAKulB,WAAW,gBAGhBvlB,KAAKwlB,uBAQjB7lB,GAAG8lB,gBAECrH,eAAgB,SAASsH,GACrB,GAAItL,GAAKpa,KAAKmgB,YAAY/E,SACtBb,KAAMmL,EAAYnL,KAClB7Y,KAAMgkB,EAAYhkB,KAClB8Z,KAAMkK,EAAYlK,KAClBd,OAAQ/a,GAAG+a,OAAO4C,mBAatB,OAVAoI,GAAYC,oBAAsB3lB,KAAKykB,sBAAsBiB,EAAYC,mBAAoBvL,GAC7FsL,EAAYE,kBAAoB5lB,KAAK2kB,oBAAoBe,EAAYE,iBAAkBxL,GAEnFsL,EAAYG,eACZ7lB,KAAKqhB,eAAejH,GAAMsL,EAAYG,cAG1C7lB,KAAKyiB,eACLziB,KAAK6iB,uBAEEzI,GAGX0L,sBAAuB,SAASvG,EAAMwG,GAC9BpmB,GAAGyI,OAAOmX,KACVA,EAAKyG,WAAahmB,KAAKimB,aAAaF,KAI5CG,YAAa,SAASngB,GAClB/F,KAAK0e,SAASyH,UAAUC,QAAQ,KAAM,KAAMrgB,EAASjC,SAGzDuiB,qBAAsB,WAClB,GAAIjO,GAAOpY,IAEX,OAAO,IAAIL,IAAG2mB,yBACVC,OAAQvmB,KAAK0e,SAAS6B,WAAWgG,OAAOC,cACxCC,eAAgBzmB,KAAK0e,SAAS+H,eAC9BC,cAAe1mB,KAAK0e,SAASiI,QAAQC,SACrCC,cAAe7mB,KAAKwkB,8BACpBsC,YAAa9mB,KAAK4kB,uBAClBmC,cAAe/mB,KAAK0kB,yBACpBsC,KAAMhnB,KAAK0e,SAASsI,KACpBlhB,IAAKnG,GAAGgQ,KAAKyI,EAAKtS,IAAKsS,GACvB6O,SAAU,SAAS7M,GACfhC,EAAK8O,UAAU9M,GACfhC,EAAKsG,SAASyH,UAAUc,SAAS7M,IAErC+M,iBAAkB,SAAS/M,EAAIgN,EAAUC,GACrCjP,EAAKkP,kBAAkBlN,EAAIgN,EAAUC,GACrCjP,EAAKsG,SAASyH,UAAUgB,iBAAiB/M,EAAIgN,EAAUC,OAMnEE,oBAAqB,WACjB,GAAInP,GAAOpY,IAEX,OAAO,IAAIL,IAAG6nB,cACVC,cAAeznB,KAAK0e,SAASgJ,MAAMD,cACnCtB,WACIrgB,IAAKnG,GAAGgQ,KAAKyI,EAAKtS,IAAKsS,GACvBuP,cAAe,SAASvc,GACpBgN,EAAKwP,wBACDlmB,KAAM,kBACN0N,SAAUzP,GAAGgQ,KAAKyI,EAAKsG,SAASyH,UAAU0B,gBAAiBzP,EAAMhN,GACjEkM,UAAW3X,GAAGgQ,KAAKyI,EAAK0P,oBAAqB1P,EAAMhN,GACnD2c,WAAY,sBAOhCC,aAAc,SAASC,EAAcC,GACjC,GAAIC,MACAC,EAAWH,EACXI,KACAC,EAAiBJ,EACjBK,EAAO,SAASC,GACZ,MAAI7oB,IAAGuG,SAASsiB,GACL7oB,GAAG6B,UAAWgnB,GAElBA,GAEXC,EAAoB,WAChB,MAAI9oB,IAAG6G,WAAW8hB,GACPA,IAEJA,GAEXI,EAAwB,SAAStO,EAAIuO,GAC7BL,GAAkB3oB,GAAGuG,SAASyiB,IAC9BhpB,GAAG6B,OAAOmnB,EAAUF,KAGpBJ,EAAoBjO,IACpBza,GAAG6B,OAAOmnB,EAAUN,EAAoBjO,IAIpD,QACIkK,IAAK,SAAS1hB,EAAKwX,GAEL,MAANA,GACA+N,KACAC,EAAWG,EAAK3lB,IAGhBulB,EAAM/N,GAAMmO,EAAK3lB,IAIzBqf,IAAK,SAAS7H,GACV,GAAIwO,EAYJ,OARIA,GADM,MAANxO,GAAc+N,EAAM/N,GACX+N,EAAM/N,GAGNmO,EAAKH,GAGlBM,EAAsBtO,EAAIwO,GAEnBL,EAAKK,IAGhBC,YAAa,SAASzO,EAAIwO,GAElBjpB,GAAGuG,SAASiiB,KAED,OAAP/N,EACIza,GAAG6G,WAAWoiB,GACdN,EAAiBM,GAGjBN,EAAiBA,MACjB3oB,GAAG6B,OAAO8mB,EAAgBM,KAI9BP,EAAoBjO,GAAMiO,EAAoBjO,OAC9Cza,GAAG6B,OAAO6mB,EAAoBjO,GAAKwO,MAK/C7nB,OAAQ,SAAS8f,GACb,aAAcsH,GAAMtH,IAGxB/G,MAAO,WACHqO,KACAE,KACAD,EAAWH,KAKvBa,yBAA0B,WACtB,GAAI1Q,GAAOpY,IAEX,OAAO,IAAIL,IAAGoa,YACVsG,QAAS,SAASjG,GACd,MAAOhC,GAAKiI,QAAQjG,IAExB0I,QAAS,SAAS1I,GACd,MAAOhC,GAAK0K,QAAQ1I,IAExBmI,QAAS,SAASnI,GACd,MAAOhC,GAAKmK,QAAQnI,IAExBuB,eAAgB,SAASvB,EAAI4B,EAAWD,GACpC3D,EAAK2Q,sBAAsB3O,EAAI4B,EAAWD,GAC1C3D,EAAKsG,SAASyH,UAAUxK,eAAevB,EAAI4B,EAAWD,GACtD3D,EAAK4Q,kBAAkB5O,EAAI2B,GAEvB3D,EAAKyL,gBACLoF,WAAW,WACP7Q,EAAKyL,eAAelI,eAAevB,EAAI4B,EAAWD,IACnD,OAcnBmN,oBAAqB,SAAS7N,GAM1B,QAAS8N,KACL,QAAIxpB,GAAGkT,kBAAkBsC,kBAEjBiD,EAAKsG,SAAS0K,YAAYC,gBAC1B1pB,GAAG0O,QACF1O,GAAGyO,QACJgK,EAAKkR,oBAAoBC,EAAmB,WAK1BzlB,SAAlBuX,EAAK3R,SACE0O,EAAKsG,SAAShV,SAGlB2R,EAAK3R,WApBpB,GAGI2Z,GAHAjL,EAAOpY,KACPuY,EAAc8C,EAAKmO,QAAUxpB,KAAK0e,SAAS+K,WAAWlR,YACtDgR,EAAoBlO,EAAKkO,mBAAqBvpB,KAAK0e,SAAS+K,WAAWF,iBA6C3E,OArBAlG,GAAS,GAAI1jB,IAAGkY,cACZU,YAAaA,EACb3Y,QAASyb,EAAKzb,QACdwZ,WAAYpZ,KAAK0e,SAASgL,QAAQC,YAClCrR,QAAS+C,EAAK/C,QACda,WAAYnZ,KAAK0e,SAASgL,QAAQE,YAClCvQ,2BAA4BrZ,KAAK0e,SAAS0K,YAAYS,iBACtDngB,SAAUyf,IACVznB,KAAM1B,KAAK0e,SAASiI,QAAQmD,UAC5B5Q,SAAU,SAAS3P,GACf6O,EAAK2R,eAAexgB,IAExB4O,MAAqB,MAAdkD,EAAKlD,MAAgBnY,KAAK0e,SAASrb,KAAK2mB,eAAiB3O,EAAKlD,QAGzEnY,KAAKiqB,gBAAgB3X,YAAY,WAC7B+Q,EAAOlR,YAGXiG,EAAKgL,SAASvgB,KAAKwgB,GAEZA,GAGX6G,qBAAsB,SAASC,EAAmBC,GAC9C,GAAIhS,GAAOpY,KACPqqB,KACAnS,GACI6K,MAAO/iB,KAAK0e,SAASqE,MACrB0D,eAAgBzmB,KAAK0e,SAAS+H,eAC9BO,KAAMhnB,KAAK0e,SAASsI,KACpBF,YAAa9mB,KAAKsjB,aAClByD,cAAe/mB,KAAKgiB,eACpB1M,SAAUtV,KAAK0e,SAASpJ,SACxBc,OAAQpW,KAAK0e,SAAStI,OACtBuI,MAAO3e,KAAK0e,SAASC,MACrB7Y,IAAKnG,GAAGgQ,KAAKyI,EAAKtS,IAAKsS,GACvBkS,kBAAmBtqB,KAAK0e,SAASoF,MAAMyG,6BACvCC,WAAY,SAASpQ,EAAI1Y,EAAM+oB,EAAQC,GAC/BD,EAAS,GAAKC,EAAQ,IAItBL,EAAejQ,GACXiQ,EAAejQ,GAAIqQ,SAAWA,GAAUJ,EAAejQ,GAAIsQ,QAAUA,IACrEtS,EAAKuS,YAAYvQ,EAAI1Y,EAAM+oB,EAAQC,GACnCtS,EAAKsG,SAASyH,UAAUqE,WAAWpQ,EAAI1Y,EAAM+oB,EAAQC,KAIzDtS,EAAKuS,YAAYvQ,EAAI1Y,EAAM+oB,EAAQC,GACnCtS,EAAKsG,SAASyH,UAAUqE,WAAWpQ,EAAI1Y,EAAM+oB,EAAQC,IAGzDL,EAAejQ,IAAOqQ,OAAQA,EAAQC,MAAOA,KAGjDE,WAAY,SAASxQ,EAAI1Y,EAAMY,EAAQiR,SAC5B8W,GAAejQ,EAEtB,IACI9K,GADAoL,EAAStC,EAAKiK,YAAYjI,GAAIA,IAAKM,MAOnCA,KAAW/a,GAAG+a,OAAO4C,mBAAqB5C,IAAW/a,GAAG+a,OAAO6C,gBAInEjO,EAAS8I,EAAKyS,YAAYzQ,EAAI1Y,EAAMY,EAAQiR,GAIxCjE,YAAmB3P,IAAG+L,QACtB4D,EAAOkI,KAAK,WACRY,EAAKsG,SAASyH,UAAUyE,WAAWxQ,EAAI1Y,EAAMY,EAAQiR,KAIzD6E,EAAKsG,SAASyH,UAAUyE,WAAWxQ,EAAI1Y,EAAMY,EAAQiR,KAG7DuX,SAAU,SAAS1Q,EAAI1Y,EAAMqpB,GACzB,GAAItf,GAAU,GAAI9L,IAAG+L,OAgBrB,OAdA0M,GAAKwP,wBACDlmB,KAAM,WACN0N,SAAUzP,GAAGgQ,KAAKyI,EAAKsG,SAASyH,UAAU2E,SAAU1S,EAAMgC,EAAI1Y,GAC9D6V,UAAW9L,EAAQK,QACnBwL,UAAW,WACPyT,EAAyBhU,KAAK,WAC1BqB,EAAK4S,UAAU5Q,EAAI1Y,KAGvB+J,EAAQG,WAEZmc,WAAY3N,IAGT3O,GAEXwf,aAActrB,GAAGgQ,KAAK3P,KAAKkrB,cAAelrB,MAC1CmrB,SAAU,SAAS/Q,EAAI1Y,GACnB0W,EAAKgT,UAAUhR,EAAI1Y,GACnB0W,EAAKsG,SAASyH,UAAUgF,SAAS/Q,EAAI1Y,IAEzC2pB,cAAe,SAASjR,EAAI1Y,EAAM4pB,GAC9BlT,EAAKmT,eAAenR,EAAIkR,GACxBlT,EAAKsG,SAASyH,UAAUkF,cAAcjR,EAAI1Y,EAAM4pB,IAEpDE,qBAAsB,SAASpR,EAAIkR,EAAWhpB,EAAQiR,GAClD6E,EAAKsG,SAASyH,UAAUqF,qBAAqBxb,MAAMoI,EAAMtQ,YAE7D2jB,SAAU,SAASrR,EAAI1Y,EAAM4pB,GACzB,MAAOlT,GAAKsG,SAASyH,UAAUsF,SAASrR,EAAI1Y,EAAM4pB,IAEtDI,YAAa,SAAStR,EAAI1Y,EAAMiqB,EAAcpY,GAC1C,MAAO6E,GAAKwT,aAAa5b,MAAMoI,EAAMtQ,YAEzC+jB,cAAe,SAASzR,EAAIgC,GACxBhE,EAAKtS,IAAI,sCAAwCsS,EAAK0K,QAAQ1I,GAAM,SAAWgC,EAAU,KACzFhE,EAAKiN,QAAQjL,EAAIgC,IAErBiE,QAAS1gB,GAAGgQ,KAAKyI,EAAKiI,QAASjI,GAC/B0K,QAASnjB,GAAGgQ,KAAKyI,EAAK0K,QAAS1K,GAC/BmK,QAAS5iB,GAAGgQ,KAAKyI,EAAKmK,QAASnK,GAC/B0T,QAASnsB,GAAGgQ,KAAKyI,EAAK2T,SAAU3T,GAChC4T,cAAe,SAASzR,GACpB,MAAOnC,GAAKiK,YAAY9H,KAAMA,KAElC0R,SAAU,SAAS7R,GACf,GAAIM,GAAStC,EAAKiK,YAAYjI,GAAIA,IAAKM,MACvC,OAAOA,KAAW/a,GAAG+a,OAAOuC,QACxBvC,IAAW/a,GAAG+a,OAAOqC,WACrBrC,IAAW/a,GAAG+a,OAAO2C,iBACrB3C,IAAW/a,GAAG+a,OAAOyC,QAE7BN,mBAAoBzE,EAAK+H,YAAYtD,mBACrCC,cAAe1E,EAAK+H,YAAYrD,cAexC,OAZAnd,IAAG+C,KAAK1C,KAAK0e,SAASiI,QAAS,SAASza,EAAMtJ,GAC1CsV,EAAQhM,GAAQtJ,IAGpBsV,EAAQ2O,cAAgB7mB,KAAKqkB,oBAEzB8F,GACAxqB,GAAG+C,KAAKynB,EAAmB,SAAS3a,EAAK5M,GACrCsV,EAAQ1I,GAAO5M,IAIhB,GAAIjD,IAAGusB,wBAAwBhU,EAASkS,IAGnD+B,oBAAqB,SAAS/R,GAC1Bpa,KAAK6iB,uBACL7iB,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAOsC,WAG7CoP,YAAa,SAASphB,GAClB,GAAIyF,IAAI,CACR,GACIzF,IAAgB,IAChByF,UACKzF,EAAQ,IAEjB,OAAO1J,MAAK+qB,IAAIrhB,EAAO,IAAKshB,QAAQ,GAAKtsB,KAAK0e,SAASrb,KAAKkpB,YAAY9b,IAK5E+b,0BAA2B,WACvB,GAAIpU,GAAOpY,IAEXA,MAAKysB,qBAEL9sB,GAAG+C,KAAK1C,KAAK0e,SAASgO,aAAc,SAAS/pB,EAAKgqB,GAC9C,GAAIjjB,GAAWijB,EAAuBjjB,SAClC+f,EAAa9pB,GAAG6B,UAAW4W,EAAKsG,SAAS+K,YAAY,GACrDmD,EAAkBjtB,GAAG6B,UAAWmrB,EAEnB7oB,UAAb4F,IACAA,EAAW0O,EAAKsG,SAAShV,UAGzBkjB,EAAgBnD,YAChB9pB,GAAG6B,OAAOioB,EAAYkD,EAAuBlD,YAAY,GAG7D9pB,GAAG6B,OAAOorB,GACNljB,SAAUA,EACV+f,WAAYA,IACb,GAEHrR,EAAKyU,iBAAiBD,MAI9B/K,WAAY,SAAS5J,GACjB,GAAI6U,GAAmB9sB,KAAKysB,kBAAkBxU,EAE9C,OAAI6U,GACOA,EAAiBltB,QAEnBqY,IAAajY,KAAK+sB,iBAChB/sB,KAAK0e,SAAS2E,OADpB,QAYT4C,aAAc,SAAS+G,GACnB,GAAIC,GAAQC,EACRC,EAAkBH,CAQtB,IALIG,YAA2BxtB,IAAG8X,YAC9B0V,EAAkBA,EAAgBzV,eAIlCyV,IAAoBxtB,GAAGyJ,OAAO+jB,GAAkB,CAChD,GAAIxtB,GAAGyI,OAAO+kB,GACV,MAAOA,GAAgBnH,UAEtB,IAA8C,UAA1CmH,EAAgBhkB,QAAQF,eACU,SAAvCkkB,EAAgBjtB,KAAK+I,cAErB,MAAOkkB,GAAgBtpB,aAAalE,GAAGkY,aAAaG,oBAYxD,IATAiV,EAASE,EAAgB1qB,qBAAqB,SAE9C9C,GAAG+C,KAAKuqB,EAAQ,SAAStqB,EAAK4G,GAC1B,GAAmC,SAA/BA,EAAM1F,aAAa,QAEnB,MADAqpB,GAAY3jB,GACL,IAIX2jB,EACA,MAAOA,GAAUrpB,aAAalE,GAAGkY,aAAaG,uBAK1DoV,gBAAiB,WACb,MAAOptB,MAAKmgB,YAAYvE,UACpBlB,QACI/a,GAAG+a,OAAO0C,UACVzd,GAAG+a,OAAO2C,gBACV1d,GAAG+a,OAAOuC,OACVtd,GAAG+a,OAAOY,WACV3b,GAAG+a,OAAOqC,UACVpd,GAAG+a,OAAOyC,UAEfzX,QAKP2nB,mBAAoB,SAASpV,GACzB,GAAI2U,GAAkB5sB,KAAKysB,kBAAkBxU,EAE7C,OAAO2U,GAAkBA,EAAgBnD,WAAazpB,KAAK0e,SAAS+K,YAGxE6D,yBAA0B,SAASC,GAC/B,MAAIA,GAAYhO,eAAgB5f,IAAG8X,WAE3B/V,KAAM/B,GAAGmS,YAAYyb,EAAYhO,KAAK7H,eACtC8D,KAAM+R,EAAYhO,KAAK7H,cAAc8D,OAKzC9Z,KAAM1B,KAAKqiB,YAAYjI,GAAImT,EAAYnT,KAAK1Y,KAC5C8Z,KAAMxb,KAAKqiB,YAAYjI,GAAImT,EAAYnT,KAAKoB,OAIpDgS,0BAA2B,SAASC,GAChC,GAAIrV,GAAOpY,KACP0tB,IAMJ,OAJA/tB,IAAG+C,KAAK+qB,EAAc,SAAS9qB,EAAK4qB,GAChCG,EAAgB7qB,KAAKuV,EAAKkV,yBAAyBC,MAGhDG,GAIXC,oBAAqB,WACjB,GAAI3tB,KAAK0e,SAASkP,OAAOvf,KAAO1O,GAAG0O,MAAO,CACtC,GAAIwf,GAAkB,yBAClBxK,EAASrjB,KAAK0e,SAASkP,OAAOvK,OAC9BpL,EAAWoL,EAASrjB,KAAKimB,aAAa5C,GAAUrjB,KAAK+sB,iBACrDe,EAAa9tB,KAAK0e,QAGlBzG,IAAYA,IAAajY,KAAK+sB,mBAC9Be,EAAa9tB,KAAKysB,kBAAkBxU,IAIxC6V,EAAWpkB,UAAW,EAGoB,OAAtCokB,EAAWrE,WAAWlR,YACtBuV,EAAWrE,WAAWlR,YAAcsV,EAGpCC,EAAWrE,WAAWlR,aAAe,IAAMsV,EAI/CluB,GAAG+C,KAAK1C,KAAKojB,SAAU,SAASzgB,EAAK0gB,GACjC,GAAIA,EAAO5J,gBAAkBxB,EAIzB,MAHAoL,GAAOhL,YAAYyV,EAAWpkB,UAC9B2Z,EAAOxJ,eAAeiU,EAAWvV,cAE1B,MAMvBqP,uBAAwB,SAASmG,GAC7B,GAAI3V,GAAOpY,KACPguB,EAAiBD,EAAQ3e,UAE7B,OAAIzP,IAAGkX,iBAAiBmX,IACpBhuB,KAAK8F,IAAIioB,EAAQrsB,KAAO,kBAAoBqsB,EAAQrsB,KAAO,gCAAkCqsB,EAAQhG,YAC9FiG,EAAejX,KAClB,SAASkX,GACL7V,EAAKtS,IAAIioB,EAAQrsB,KAAO,wBAA0BqsB,EAAQhG,YAC1DgG,EAAQzW,UAAU2W,IAEtB,WACQF,EAAQxW,WACRa,EAAKtS,IAAIioB,EAAQrsB,KAAO,wBAA0BqsB,EAAQhG,YAC1DgG,EAAQxW,aAGRa,EAAKtS,IAAIioB,EAAQrsB,KAAO,wBAA0BqsB,EAAQhG,gBAKtEiG,KAAmB,EACnBD,EAAQzW,UAAU0W,GAGdD,EAAQxW,WACRvX,KAAK8F,IAAIioB,EAAQrsB,KAAO,mCAAqCqsB,EAAQhG,WAAa,iCAClFgG,EAAQxW,aAGRvX,KAAK8F,IAAIioB,EAAQrsB,KAAO,mCAAqCqsB,EAAQhG,WAAa,wBAInFiG,IAIXvP,eAAgB,SAASc,EAAM9D,EAASyS,GACpC,GAAI9V,GAAOpY,KACPua,EAAO5a,GAAGoN,cACVyO,GAAO,EACP9Z,EAAO/B,GAAGmS,YAAYyN,GACtB4O,EAAa5O,EAAKnU,MAAQmU,EAC1B6O,EAAUpuB,KAAKquB,sBACXruB,KAAKquB,sBACL1uB,GAAGgQ,KAAKyI,EAAKkW,sBAAuBlW,IAEvCzY,GAAGiJ,QAAQulB,IAAeA,EAAW3S,MAAQ,IAC9CA,EAAO2S,EAAW3S,MAGtB4S,EAAQD,EAAYzsB,EAAM6Y,EAAMiB,EAAM0S,EAAoBzS,EAASzb,KAAK0e,SAASiI,QAAQC,UACrF1G,WAAY9H,EAAK+H,YACjB2G,YAAa1O,EAAKkL,aAClBiL,iBAAkB,SAASnU,EAAImF,GAC3BnH,EAAKwH,SAASrP,IAAI6J,EAAImF,GACtBnH,EAAKyK,uBACLzK,EAAKoW,aAAapU,OAK9BkU,sBAAuB,SAAS/O,EAAM7d,EAAM6Y,EAAMiB,EAAMiT,EAAUhT;AAC9D,GAAIrB,GAAKpa,KAAKmgB,YAAY/E,SAASb,KAAMA,EAAM7Y,KAAMA,EAAM8Z,KAAMA,EAAMC,QAASA,GAEhFzb,MAAK4f,SAASrP,IAAI6J,EAAImF,GACtBvf,KAAKwuB,aAAapU,GAElBpa,KAAK6iB,uBAEL4L,EAAS5rB,MAAMuX,GAAIA,EAAImF,KAAMA,KAGjCuI,oBAAqB,SAAS1c,EAAMsjB,GAChC,GAAIC,GAAYvjB,EAAKlL,KAAKmF,MAAM,KAAK,GACjC3D,EAAOgtB,CAGC,OAARhtB,IACAA,EAAO1B,KAAK0e,SAASgJ,MAAM9I,aAG/Bld,GAAQ,IAAMitB,EAEd3uB,KAAKge,UACDtc,KAAMA,EACN0J,KAAMA,KAKdyhB,iBAAkB,SAASxR,GACvB,GAAIgI,GAASrjB,KAAKkpB,qBACdM,OAAQnO,EAAKoO,WAAWlR,YACxBgR,kBAAmBlO,EAAKoO,WAAWF,kBACnC3pB,QAASyb,EAAKzb,QACd0Y,QAAS+C,EAAK/C,QACd5O,SAAU2R,EAAK3R,SACfyO,MAAOkD,EAAK2O,gBAGhBhqB,MAAKysB,kBAAkBpJ,EAAO5J,eAAiB4B,GAGnDuT,0BAA2B,WACvB5uB,KAAK6uB,aAAelvB,GAAGmvB,aAAe,GAAInvB,IAAGmvB,YACzC9uB,KAAK0e,SAASzN,KAAMtR,GAAGgQ,KAAK3P,KAAKslB,kBAAmBtlB,MAAOL,GAAGgQ,KAAK3P,KAAK8F,IAAK9F,OAG7EA,KAAK6uB,cAAgB7uB,KAAK6uB,aAAaE,gBACvC/uB,KAAKsjB,aAAetjB,KAAKgoB,aACrBhoB,KAAK0e,SAASiI,QAAQ7I,OAAS9d,KAAK6uB,aAAaG,uBAGrDhvB,KAAK0e,SAASuQ,WAAajvB,KAAK6uB,aAAaK,cACzClvB,KAAK6uB,aAAaM,cAClBnvB,KAAK0e,SAASiI,QAAQ5I,SAAW/d,KAAK6uB,aAAaM,cAIvDnvB,KAAKsjB,aAAetjB,KAAKgoB,aAAahoB,KAAK0e,SAASiI,QAAQ7I,SAIpEsR,kBAAmB,WACf,SAAKzvB,GAAG2mB,0BAA4BtmB,KAAK0e,SAAS6B,WAAWH,YAIzDpgB,KAAK0e,SAASsI,KAAKqI,aACf1vB,GAAGkT,kBAAkB4C,sBAIrB9V,GAAGkT,kBAAkB2C,oBAAqBxV,KAAK0e,SAASsI,KAAKsI,aAUzEhG,oBAAqB,SAASiG,EAASvd,GACnC,GAAIwd,IAAQ,CAEZ,QAAKD,EAAQ7pB,SAIb/F,GAAG+C,KAAK6sB,EAAS,SAAS5sB,EAAK8sB,GAK3B,GAAI9vB,GAAGwH,SAASsoB,GAAa,CAEzB,GAAIC,GAAW,GAAI7tB,QAAO,MAAQ4tB,EAAa,IAAK,IAEpD,IAAgC,MAA5Bzd,EAASoB,MAAMsc,GAEf,MADAF,IAAQ,GACD,KAKZA,IAWXjK,WAAY,SAASoK,EAAMC,EAAkB3oB,GASzC,QAASgG,GAAEvL,EAAMmuB,GAAe9pB,EAAUA,EAAQ7D,QAAQR,EAAMmuB,GARhE,GAMIC,GAAsBC,EANtBhqB,EAAU/F,KAAK0e,SAASsR,SAASL,GACjCpG,KACA0G,KAAWlgB,OAAO6f,GAClBluB,EAAOuuB,EAAM,GACbhY,EAAWjY,KAAKimB,aAAahf,GAC7BipB,EAAiBlwB,KAAKqtB,mBAAmBpV,EA+B7C,OA1BAtY,IAAG+C,KAAKwtB,EAAe3G,kBAAmB,SAAS5mB,EAAKwtB,GAKhDxwB,GAAGwH,SAASgpB,IACZ5G,EAAkB1mB,KAAKstB,KAI/BL,EAAuBvG,EAAkB3Y,KAAK,MAAM3H,cAEpDgE,EAAE,SAAUjN,KAAK0e,SAAS0R,eAAe1uB,IACzCuL,EAAE,eAAgB6iB,GAClB7iB,EAAE,cAAejN,KAAKosB,YAAY8D,EAAeG,YACjDpjB,EAAE,iBAAkBjN,KAAKosB,YAAY8D,EAAeI,eAEpDP,EAAmBhqB,EAAQqN,MAAM,cACR,OAArB2c,GACApwB,GAAG+C,KAAKqtB,EAAkB,SAASptB,EAAK4tB,GACpCtjB,EAAEsjB,EAAaN,EAAMttB,MAI7B3C,KAAK0e,SAASyH,UAAUC,QAAQ,KAAM1kB,EAAMqE,EAASjC,QAE9CiC,GAYXge,aAAc,SAAS3J,EAAIhL,GACvB,GAAIpP,KAAKwwB,qBAAqBpW,GAW1B,MAVApa,MAAK6iB,uBACL7iB,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAO2C,iBAErCjO,EACAA,EAASgL,GAGTpa,KAAK4f,SAASkE,MAAM1J,IAGjB,GAIf4O,kBAAmB,SAAS5O,EAAIM,GAC5B,GAAItC,GAAOpY,KACPywB,EAAczwB,KAAKotB,iBAEnB1S,KAAW/a,GAAG+a,OAAO4C,kBACrBtd,KAAK2jB,+BAA+B9gB,KAAKuX,GAEpCM,IAAW/a,GAAG+a,OAAO6C,eAC1Bvd,KAAK4jB,4BAA4B/gB,KAAKuX,GAGtB,IAAhBqW,IACCzwB,KAAK2jB,+BAA+Bje,QAAU1F,KAAK4jB,4BAA4Ble,SAEhFujB,WAAW,WACP7Q,EAAKsY,eAAetY,EAAKuL,+BAAgCvL,EAAKwL,8BAC/D,IAIXvF,iCAAkC,WAC9B,GAAIjG,GAAOpY,IAEX,IAAIA,KAAK0e,SAAS0K,YAAYuH,mBAAqBhxB,GAAG6O,UAAY7O,GAAG+O,YAIjE,KAHAua,YAAW,WACPpkB,OAAO+rB,MAAMxY,EAAKsG,SAASsR,SAASa,+BACrC,GACG,GAAIlxB,IAAGwB,MAAMnB,KAAK0e,SAASsR,SAASa,+BAIlDC,8BAA+B,SAAS1W,EAAI1Y,EAAMqvB,EAAUxd,GAGxD,IAAKwd,EAASnlB,QACV,GAAI2H,GAAsB,MAAfA,EAAImH,SAAmBqW,EAAS/mB,MACvChK,KAAK0e,SAASyH,UAAUC,QAAQhM,EAAI1Y,EAAM,8BAAgC6R,EAAImH,OAAQnH,OAErF,CACD,GAAIyd,GAAcD,EAAS/mB,MAAQ+mB,EAAS/mB,MAAQhK,KAAK0e,SAASrb,KAAK4tB,oBACvEjxB,MAAK0e,SAASyH,UAAUC,QAAQhM,EAAI1Y,EAAMsvB,EAAazd,KAKnE2d,6CAA8C,SAASC,EAAWhnB,EAAO0Q,EAAOiD,EAAQC,GACpF,GAAI3F,GAAOpY,IAEX,IAAImK,EAAMzE,OAASmV,EACf,GAAIsW,IAAcnxB,KAAK0e,SAAS+K,WAAW2H,uBAEvCnI,WAAW,WACP,GAAIoI,GAAuBjZ,EAAKkV,yBAAyBnjB,EAAM0Q,IAC3D5C,EAAWG,EAAK6N,aAAa9b,EAAM0Q,GAAO0E,MAC1C8D,EAASjL,EAAKyJ,WAAW5J,EAE7BG,GAAKwP,wBACDlmB,KAAM,aACN0N,SAAUzP,GAAGgQ,KAAKyI,EAAKsG,SAASyH,UAAUmL,WAAYlZ,EAAMiZ,EAAsBhO,GAClF/L,UAAW3X,GAAGgQ,KAAKyI,EAAKmZ,2BAA4BnZ,EAAMjO,EAAO0Q,EAAOiD,EAAQC,GAChFxG,UAAW5X,GAAGgQ,KAAKyI,EAAKoZ,2BAA4BpZ,EAAMjO,EAAO0Q,EAAOiD,EAAQC,GAChFgK,WAAY,SAAWsJ,EAAqB3vB,KAAO,YAAc2vB,EAAqB7V,QAE3F,OAEF,KAAK2V,EACN,KAAOtW,EAAQ1Q,EAAMzE,OAAQmV,IACzBzC,EAAK+T,oBAAoBhiB,EAAM0Q,GAAOT,KAMtDsW,eAAgB,SAASe,EAAYC,GACjC1xB,KAAK6jB,gBAAkB7jB,KAAK6jB,eAAe8N,cAAcF,EAAYC,EAAQ1xB,KAAKmjB,iBAElFnjB,KAAK0e,SAASyH,UAAUwL,cAAchyB,GAAG6B,UAAWiwB,GAAa9xB,GAAG6B,UAAWkwB,IAE/E1xB,KAAK2jB,kCACL3jB,KAAK4jB,gCAeTgI,aAAc,SAASxR,EAAI1Y,EAAMiqB,EAAcpY,EAAKnE,GAChD,GAAIgJ,GAAOpY,IAIX,IAFAoY,EAAK+K,gBAAgB/I,GAAMuR,EAAavT,EAAKsG,SAASoF,MAAMyG,8BAExDnS,EAAKwZ,iBAAiBxX,EAAI1Y,EAAMiqB,GAiBhC,MAhBAvT,GAAK0Y,8BAA8B9gB,MAAMoI,EAAMtQ,WAC/CsQ,EAAKsG,SAASyH,UAAUuF,YAAYtR,EAAI1Y,EAAM0W,EAAK6K,aAAa7I,IAChEhC,EAAKyZ,mBAAmBzX,EAAI1Y,GAE5B0W,EAAK8K,eAAe9I,GAAM6O,WAAW,WACjC7Q,EAAKtS,IAAI,YAAcpE,EAAO,OAC9B0W,EAAK+H,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAO2C,iBAErCjO,EACAA,EAASgL,GAGThC,EAAKwH,SAASkE,MAAM1J,IAEc,IAAvChC,EAAKsG,SAASoF,MAAMgO,mBAEhB,GAIfD,mBAAoB,SAASzX,EAAI1Y,GAC7B1B,KAAK8F,IAAI,WAAa9F,KAAK0e,SAASoF,MAAMgO,iBAAmB,4BAA8BpwB,EAAO,QAItG8uB,qBAAsB,SAASpW,GAC3B,GACIpI,GADA+f,EAAY/xB,KAAK4iB,iBAGrB,OAAI5iB,MAAKmjB,gBAAgB/I,IACrBpa,KAAK8F,IAAI,gCAAkCsU,EAAI,SACxC,GAEFpa,KAAK4f,SAASe,QAAQvG,IAC3BpI,EAAWhS,KAAKqgB,QAAQjG,GAEpBpa,KAAK0e,SAASyH,UAAU6L,cAAc5X,EAAIpI,MAAc,IAIxD+f,EAAY,GAAK/xB,KAAK6iB,qBAAuB,EAAIkP,GACjD/xB,KAAKulB,WAAW,0BACT,IAGXvlB,KAAK8F,IAAI,wBAA0BkM,EAAW,UAAYoI,EAAK,SACxD,MAGPpa,KAAK8F,IAAI,IAAMsU,EAAK,2BAA4B,UACzC,IAIf4Q,UAAW,SAAS5Q,EAAI1Y,GACpB1B,KAAK6iB,uBAELoP,aAAajyB,KAAKkjB,eAAe9I,GAEjC,IAAI8X,GAAkBvyB,GAAG2F,QAAQtF,KAAKse,WAAYlE,IAC7Cpa,KAAK0e,SAASuQ,YAAciD,GAAmB,GAChDlyB,KAAKse,WAAWpC,OAAOgW,EAAiB,GAG5ClyB,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAOwC,WAG7C2N,YAAa,SAASzQ,EAAI1Y,EAAMY,EAAQiR,GAoBpC,MAnBKjR,GAAOsJ,SASJtJ,EAAOujB,eACP7lB,KAAKqhB,eAAejH,GAAM9X,EAAOujB,cAGrC7lB,KAAKyiB,eACLziB,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAO4C,qBAbzCtd,KAAK6iB,uBACL7iB,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAO6C,eAErCjb,EAAOtC,KAAK0e,SAASoF,MAAMyG,iCAAkC,IAC7DvqB,KAAKmjB,gBAAgB/I,IAAM,IAYnCpa,KAAK8wB,8BAA8B1W,EAAI1Y,EAAMY,EAAQiR,KAE9CjR,EAAOsJ,SAGlBsb,UAAW,SAAS9M,GAChBpa,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAO+C,WAG7C6J,kBAAmB,SAASlN,EAAIgN,EAAUC,GACtC,GAAI3lB,GAAO1B,KAAKqgB,QAAQjG,EAEpBiN,IACArnB,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAO8C,eACzCxd,KAAK8F,IAAI,uBAAyBpE,EAAO,gBAAiB,SAIzBoC,SAA7BsjB,EAAS5T,gBACTxT,KAAK0e,SAASyH,UAAUC,QAAQhM,EAAI1Y,EAAM,wBAAyB0lB,GAGnEpnB,KAAK0e,SAASyH,UAAUC,QAAQhM,EAAI1Y,EAAM,4CAA8C0lB,EAAS1M,OAAQ0M,KAI7GpnB,KAAK6iB,uBACL7iB,KAAKyiB,eACLziB,KAAK4f,SAASuS,QAAQ/X,GACtBpa,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAOgD,SACzC1d,KAAK8F,IAAI,uBAAyBpE,EAAO,sBAIjDqoB,eAAgB,SAASxgB,GACrB,GAAI6oB,EAEJ,IAAIzyB,GAAGkT,kBAAkBsC,cAAe,CACpC,IAAKid,EAAY,EAAGA,EAAY7oB,EAAM+V,MAAM5Z,OAAQ0sB,IAChDpyB,KAAK8lB,sBAAsBvc,EAAM+V,MAAM8S,GAAY7oB,EAGvDvJ,MAAKge,SAASzU,EAAM+V,WAGf/V,GAAM7C,MAAMhB,OAAS,GAC1B1F,KAAKge,SAASzU,EAGlB5J,IAAG+C,KAAK1C,KAAKojB,SAAU,SAASzgB,EAAK0gB,GACjCA,EAAOvJ,WAIf6Q,YAAa,SAASvQ,EAAI1Y,EAAM+oB,EAAQC,GACpC1qB,KAAK6jB,gBAAkB7jB,KAAK6jB,eAAewO,qBAAqBjY,EAAIqQ,EAAQC,IAGhF4H,UAAW,SAASlY,EAAI1Y,KAIxB6wB,yBAA0B,SAASnY,EAAI1Y,GACnC1B,KAAKsyB,UAAUtiB,MAAMhQ,KAAM8H,WAC3B9H,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAOqC,WACzC/c,KAAKwyB,aAAaxiB,MAAMhQ,KAAM8H,WAE1B9H,KAAK0e,SAASuQ,YACdjvB,KAAK0e,SAASyH,UAAUsM,YAAYziB,MAAMhQ,KAAM8H,WAChD9H,KAAKsgB,YAAYlG,KAGjBpa,KAAK0yB,eAAetY,GACpBpa,KAAK0e,SAASyH,UAAUsM,YAAYziB,MAAMhQ,KAAM8H,aAIxD0Y,gBAAiB,SAASpG,EAAIuY,EAAmBC,GAC7C,GACIC,GADAtY,EAAOva,KAAK8iB,QAAQ1I,EAOxB,OAJIuY,KACAE,EAA4BlzB,GAAGgQ,KAAKgjB,EAAmB3yB,KAAMoa,EAAIG,EAAMqY,IAGvE5yB,KAAKovB,qBACLpvB,KAAK4nB,wBACDlmB,KAAM,iBACN0N,SAAUzP,GAAGgQ,KAAK3P,KAAK0e,SAASyH,UAAU2M,eAAgB9yB,KAAMoa,GAChE9C,UAAWub,GACPlzB,GAAGgQ,KAAK3P,KAAK+yB,eAAeC,WAAYhzB,KAAMoa,EAAIG,EAAMqY,GAC5D7K,WAAY3N,KAET,IAGPpa,KAAK8F,IAAI,iCAAmCsU,EAAK,wHACuB,SACjE,IAIfoY,aAAc,SAASpY,KAIvB6Y,iBAAkB,SAASxI,EAAQC,GAC/B1qB,KAAK0e,SAASyH,UAAU+M,gBAAgBzI,EAAQC,IAGpDQ,cAAe,SAAS9Q,KAIxBgR,UAAW,SAAShR,EAAI1Y,GACpB1B,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAO0C,YAG7CmO,eAAgB,SAASnR,EAAIkR,KAI7BvC,sBAAuB,SAAS3O,EAAI4B,EAAWD,GAEvCA,IAAcpc,GAAG+a,OAAOyC,QACxB8U,aAAajyB,KAAKkjB,eAAe9I,KAIzC+Y,gCAAiC,SAAS1F,GACtC,GAAIrV,GAAOpY,IAEXL,IAAG+C,KAAK+qB,EAAc,SAAS9qB,EAAK4qB,GAChCnV,EAAK+T,oBAAoBoB,EAAYnT,OAI7CgZ,gCAAiC,SAASC,EAAuBlpB,EAAO2T,EAAQC,EAAUsF,GACtF,GAAIiQ,GACAvB,EAAY/xB,KAAK4iB,kBACjB2Q,EAAmCvzB,KAAK6iB,oBAE1B,KAAdkP,GAAmBwB,GAAoCxB,EACnD5nB,EAAMzE,OAAS,EACf1F,KAAK4nB,wBACDlmB,KAAM,aACN0N,SAAUzP,GAAGgQ,KAAK3P,KAAK0e,SAASyH,UAAUmL,WAAYtxB,KAAMqzB,EAAsB,GAAIhQ,GACtF/L,UAAW3X,GAAGgQ,KAAK3P,KAAKuxB,2BAA4BvxB,KAAMmK,EAAO,EAAG2T,EAAQC,GAC5ExG,UAAW5X,GAAGgQ,KAAK3P,KAAKwxB,2BAA4BxxB,KAAMmK,EAAO,EAAG2T,EAAQC,GAC5EgK,WAAY,SAAW5d,EAAM,GAAGoV,KAAK7d,KAAO,YAAcyI,EAAM,GAAGoV,KAAK/D,OAI5Exb,KAAKulB,WAAW,iBAIpBvlB,KAAKmzB,gCAAgChpB,GACrCmpB,EAAetzB,KAAK0e,SAASsR,SAASwD,kBACjCtxB,QAAQ,gBAAiBqxB,GACzBrxB,QAAQ,iBAAkB6vB,GAC/B/xB,KAAKkmB,YAAYoN,KAIzB9B,2BAA4B,SAASrnB,EAAO0Q,EAAOiD,EAAQC,GACvD,GAAI0V,GAAY5Y,EAAQ,CAExB7a,MAAKmsB,oBAAoBhiB,EAAM0Q,GAAOT,GAAIjQ,EAAM0Q,GAAO0E,KAAK7d,MAE5D1B,KAAKkxB,8CAA6C,EAAO/mB,EAAOspB,EAAW3V,EAAQC,IAGvFwT,2BAA4B,SAASpnB,EAAO0Q,EAAOiD,EAAQC,GACvD,GAAI3F,GAAOpY,KACPyzB,EAAY5Y,EAAQ,EACpBwW,EAAuBrxB,KAAKstB,yBAAyBnjB,EAAM0Q,GAE/D7a,MAAK0zB,wBAAwBvpB,EAAM0Q,GAAQwW,GACtCta,KACD,WACIqB,EAAKub,QAAQxpB,EAAM0Q,GAAOT,GAAI0D,EAAQC,GACtC3F,EAAK8Y,8CAA6C,EAAM/mB,EAAOspB,EAAW3V,EAAQC,IAEtF,WACI3F,EAAK8Y,8CAA6C,EAAO/mB,EAAOspB,EAAW3V,EAAQC,MAK/F2B,uBAAwB,SAASvV,EAAO2T,EAAQC,GAC5C,GAAqB,IAAjB5T,EAAMzE,OAEN,WADA1F,MAAKulB,WAAW,eAIpB,IAAI8N,GAAwBrzB,KAAKwtB,0BAA0BrjB,GACvD8N,EAAWjY,KAAKimB,aAAa9b,EAAM,GAAGoV,MACtC8D,EAASrjB,KAAK6hB,WAAW5J,EAE7BjY,MAAK4nB,wBACDlmB,KAAM,kBACN0N,SAAUzP,GAAGgQ,KAAK3P,KAAK0e,SAASyH,UAAUyN,gBAAiB5zB,KAAMqzB,EAAuBhQ,GACxF/L,UAAW3X,GAAGgQ,KAAK3P,KAAKozB,gCAAiCpzB,KAAMqzB,EAAuBlpB,EAAO2T,EAAQC,EAAUsF,GAC/G9L,UAAW5X,GAAGgQ,KAAK3P,KAAKmzB,gCAAiCnzB,KAAMmK,GAC/D4d,WAAY,sBAIpB8L,wBAAyB,WACrB,GAAIzb,GAAOpY,IAEXA,MAAKiqB,gBAAgBhqB,OAAO4E,OAAQ,eAAgB,SAASgK,GACzD,GAAIuJ,EAAK8J,gBAKL,MAJArT,GAAIA,GAAKhK,OAAOivB,MAEhBjlB,EAAEC,YAAcsJ,EAAKsG,SAASsR,SAAS+D,QAEhC3b,EAAKsG,SAASsR,SAAS+D,WAQ1CrQ,oBAAqB,WACjB,GAAItL,GAAOpY,KACPkY,EAAUlY,KAAK0e,SAAS8E,OAGxB7jB,IAAGq0B,SAA6C,MAAlCh0B,KAAK0e,SAAS8E,QAAQzF,WAC/B/d,KAAKi0B,WACNt0B,GAAG6B,OAAO0W,GAAU8O,KAAMhnB,KAAK0e,SAASsI,OAExC9O,EAAQpS,IAAMnG,GAAGgQ,KAAK3P,KAAK8F,IAAK9F,MAChCkY,EAAQgc,cAAgBv0B,GAAGgQ,KAAK3P,KAAKoe,eAAgBpe,MAErDA,KAAKi0B,SAAW,GAAIt0B,IAAGq0B,QAAQ9b,IAGnC+Q,WAAW,WACP7Q,EAAK6b,SAASE,UAAUpd,KAAK,SAASga,EAAU3J,GAC5ChP,EAAKgc,0BACLhc,EAAKsG,SAASyH,UAAUkO,yBAAyBtD,GAAU,EAAM3J,IAElE,SAAS2J,EAAU3J,GAElBhP,EAAKsG,SAASyH,UAAUkO,yBAAyBtD,GAAU,EAAO3J,MAEvE,KAIXgN,wBAAyB,aAEzBrI,SAAU,SAAS3R,EAAIqC,GACnBzc,KAAKmgB,YAAY3D,WAAWpC,EAAIqC,GAChCzc,KAAK6jB,gBAAkB7jB,KAAK6jB,eAAeyQ,UAAUla,IAGzDwX,iBAAkB,SAASxX,EAAI1Y,EAAMiqB,GACjC,GAAIzL,GAAalgB,KAAKmgB,YAAYvE,UAAUxB,GAAIA,GAGhD,WAAKpa,KAAKmjB,gBAAgB/I,IACnBpa,KAAK0e,SAASoF,MAAMyQ,YACpBrU,EAAWxF,SAAW/a,GAAG+a,OAAOyC,SAELrZ,SAA1B9D,KAAKijB,aAAa7I,KAClBpa,KAAKijB,aAAa7I,GAAM,GAGxBpa,KAAKijB,aAAa7I,GAAMpa,KAAK0e,SAASoF,MAAM0Q,oBAC5Cx0B,KAAKijB,aAAa7I,IAAO,GAClB,IAOnBsY,eAAgB,SAAStY,GACrBpa,KAAKse,WAAWzb,KAAKuX,IAIzBoU,aAAc,SAASpU,GACnB,GAAInC,EAGAA,GADAtY,GAAGkT,kBAAkBsC,cACVnV,KAAK4f,SAAS2B,QAAQnH,GAAI4L,WAG1BhmB,KAAKimB,aAAajmB,KAAK4f,SAASpG,SAASY,IAGpDnC,IACAjY,KAAK8hB,qBAAqB1H,GAAMnC,IAIxC+M,4BAA6B,SAASyP,GAClCz0B,KAAK0e,SAASzN,KAAKrR,QAAU60B,EAE7Bz0B,KAAK6uB,aAAelvB,GAAGmvB,aAAe,GAAInvB,IAAGmvB,YACrC9uB,KAAK0e,SAASzN,KAAMtR,GAAGgQ,KAAK3P,KAAKslB,kBAAmBtlB,MAAOL,GAAGgQ,KAAK3P,KAAK8F,IAAK9F,OAGjFA,KAAK6uB,cAAgB7uB,KAAK6uB,aAAaE,iBACvC/uB,KAAKsjB,aAAauF,YAAY,KAAM7oB,KAAK6uB,aAAaG,uBAEtDhvB,KAAK0e,SAASuQ,WAAajvB,KAAK6uB,aAAaK,cACzClvB,KAAK6uB,aAAaM,aAClBnvB,KAAK6kB,YAAY7kB,KAAK6uB,aAAaM,eAK/CwE,QAAS,SAASvZ,EAAI0D,EAAQC,GAC1B,GAAIrc,GAAO1B,KAAKqgB,QAAQjG,EAEpB0D,IACA9d,KAAKolB,UAAUtH,EAAQ1D,GAGvB2D,GACA/d,KAAK6kB,YAAY9G,EAAU3D,GAG/Bpa,KAAK4nB,wBACDlmB,KAAM,WACN0N,SAAUzP,GAAGgQ,KAAK3P,KAAK0e,SAASyH,UAAUuO,SAAU10B,KAAMoa,EAAI1Y,GAC9D4V,UAAW3X,GAAGgQ,KAAK3P,KAAKuyB,yBAA0BvyB,KAAMoa,EAAI1Y,GAC5D6V,UAAW5X,GAAGgQ,KAAK3P,KAAKmsB,oBAAqBnsB,KAAMoa,EAAI1Y,GACvDqmB,WAAY3N,KAIpBkG,YAAa,SAASlG,GACbpa,KAAK4f,SAAS/V,OAAOuQ,IACtBpa,KAAKmgB,YAAYrE,UAAU1B,EAAIza,GAAG+a,OAAOuC,SAIjDuI,mBAAoB,WAIhB,IAHA,GAAImP,GAAYC,EACZxc,EAAOpY,KAEJA,KAAKse,WAAW5Y,QACnBivB,EAAa30B,KAAKse,WAAWjM,QAC7BrS,KAAKsgB,YAAYqU,EAIrBC,GAAkB50B,KAAKqiB,YAAY3H,OAAQ/a,GAAG+a,OAAOY,aAAa5V,OAC9DkvB,IACAj1B,GAAGmG,IAAI,qBAAuB8uB,EAAkB,yEAChD3L,WAAW,WACP7Q,EAAKoN,sBACN,OAYXkO,wBAAyB,SAASnG,EAAa8D,GAC3C,GAAIjZ,GAAOpY,KACPuf,EAAQ,WACJ,MAAIgO,GAAYhO,eAAgB5f,IAAG8X,UACxB8V,EAAYhO,KAAK7H,cAErB6V,EAAYhO,QAEvB7d,EAAO2vB,EAAqB3vB,KAC5B8Z,EAAO6V,EAAqB7V,KAC5BvD,EAAWjY,KAAKimB,aAAasH,EAAYhO,MACzC2Q,EAAiBlwB,KAAKqtB,mBAAmBpV,GACzC4c,EAAkB,GAAIl1B,IAAG+L,OAQ7B,OANAmpB,GAAgB9d,KACZ,aACA,WACIqB,EAAK+T,oBAAoBoB,EAAYnT,GAAI1Y,KAG7C/B,GAAG+I,cAAc6W,KAAUvf,KAAKspB,oBAAoB4G,EAAe3G,kBAAmB7nB,IACtF1B,KAAKulB,WAAW,YAAa7jB,EAAM6d,GAC5BsV,EAAgB/oB,WAGd,IAAT0P,GACAxb,KAAKulB,WAAW,aAAc7jB,EAAM6d,GAC7BsV,EAAgB/oB,WAGvB0P,EAAO,GAAK0U,EAAeG,WAAa7U,EAAO0U,EAAeG,WAC9DrwB,KAAKulB,WAAW,YAAa7jB,EAAM6d,GAC5BsV,EAAgB/oB,WAGvB0P,EAAO,GAAKA,EAAO0U,EAAeI,cAClCtwB,KAAKulB,WAAW,eAAgB7jB,EAAM6d,GAC/BsV,EAAgB/oB,YAGvBnM,GAAGm1B,iBAAmBn1B,GAAGkT,kBAAkBkD,eAAiBpW,GAAGyI,OAAOmX,GACtE,GAAI5f,IAAGm1B,gBAAgBvV,EAAM5f,GAAGgQ,KAAKyI,EAAKtS,IAAKsS,IAAO2c,SAAS7E,EAAe8E,OAAOje,KACjF8d,EAAgBjpB,QAChB,SAASqpB,GACL7c,EAAKmN,WAAW0P,EAAY,aAAcvzB,EAAM6d,GAChDsV,EAAgB/oB,YAKxB+oB,EAAgBjpB,UAGbipB,IAGXK,eAAgB,WACZ,GAAI9c,GAAM+c,EAAcjpB,CAExBkM,GAAOpY,KAEPm1B,EAAe,SAASzzB,EAAM0N,EAAUzH,GACpC,GAAIytB,EAEJ,KACI,MAAOhmB,GAASY,MAAMoI,EAAMzQ,GAEhC,MAAO0tB,GACHD,EAAWC,EAAUtvB,SAAWsvB,EAAU/uB,WAC1C8R,EAAKtS,IAAI,wBAA0BpE,EAAO,gBAAkB0zB,EAAU,UAK9E,KAAKlpB,IAAQlM,MAAK0e,SAASyH,WACtB,WACG,GAAImP,GAAcC,CAClBD,GAAeppB,EACfqpB,EAAend,EAAKsG,SAASyH,UAAUmP,GACvCld,EAAKsG,SAASyH,UAAUmP,GAAgB,WACpC,MAAOH,GAAaG,EAAcC,EAAcztB,oBC3zDvE,WACG,YAEAnI,IAAG61B,kBAAoB,SAAS1d,GAC5B,GAAIM,GAAOpY,IAGXA,MAAK0e,UACDqE,OAAO,EACPM,OAAQ,KACR3Z,UAAU,EACV+c,eAAgB,EAChBgP,6BAA6B,EAC7BxG,YAAY,EAEZtI,SACIE,iBACA9I,SAAU,iBACV2X,cAAe,aACfC,gBAAgB,EAChB7L,UAAW,SACXvD,OAAQ,OACRzI,UACA8X,cAAc,EACdC,kBAAmB,kBACnBjP,SAAU,UAGd6C,YACIF,qBACA8G,UAAW,EACXC,aAAc,EACdyB,UAAW,EACXX,wBAAwB,EACxB7Y,YAAa,KACbyc,OACIc,UAAW,EACXC,SAAU,EACVC,UAAW,EACXC,SAAU,IAIlB9P,WACIuO,SAAU,SAASta,EAAI1Y,KACvB+wB,YAAa,SAASrY,EAAI1Y,KAC1BkpB,WAAY,SAASxQ,EAAI1Y,EAAMiqB,EAAcuK,KAC7CvE,cAAe,SAASF,EAAYC,KACpC5G,SAAU,SAAS1Q,EAAI1Y,KACvBypB,SAAU,SAAS/Q,EAAI1Y,KACvB2pB,cAAe,SAASjR,EAAI1Y,EAAM4pB,KAClCE,qBAAsB,SAASpR,EAAIkR,EAAWK,EAAcpY,KAC5DkY,SAAU,SAASrR,EAAIpI,EAAUsZ,KACjCd,WAAY,SAASpQ,EAAI1Y,EAAM+oB,EAAQC,KACvCwI,gBAAiB,SAASzI,EAAQC,KAClCtE,QAAS,SAAShM,EAAI1Y,EAAMigB,EAAQwU,KACpCzK,YAAa,SAAStR,EAAI1Y,EAAM00B,KAChCpE,cAAe,SAAS5X,EAAI1Y,KAC5BkyB,gBAAiB,SAASyC,KAC1B/E,WAAY,SAAS+E,KACrBvD,eAAgB,SAAS1Y,KACzB6M,SAAU,SAAS7M,KACnB+M,iBAAkB,SAAS/M,EAAIgN,EAAUC,KACzCQ,gBAAiB,SAASzc,KAC1BuQ,eAAgB,SAASvB,EAAI4B,EAAWD,KACxCsY,yBAA0B,SAAStD,EAAUnlB,EAASwb,MAG1D4I,UACIsG,UAAW,qEACXC,UAAW,yDACXC,aAAc,4DACdC,WAAY,yDACZC,aAAc,sBACdlD,kBAAmB,6EACnBmD,oBAAqB,qBACrBC,mBAAoB,qBACpBC,oBAAqB,4BACrBC,mBAAoB,4BACpBC,sBAAuB,mDACvBhD,QAAS,8EACTlD,6BAA8B,6KAGlC/M,OACIyQ,YAAY,EACZC,gBAAiB,EACjB1C,iBAAkB,EAClBvH,6BAA8B,gBAGlCb,SACIE,YAAa,yBACbD,YAAa,0BAGjBrU,UACI8K,SAAS,EACT4W,YACI5W,SAAS,GAEb6W,WAAW,EACXC,YACIC,UAAW,cACXC,eAAgB,mBAChBC,UAAW,cACXC,cAAe,kBACfC,WAAY,gBAEhBC,SAAU,IAEV5rB,SACImS,SAAU,OAIlB3H,QACIgK,SAAS,EACTqX,gBAAiB,EACjBP,YACIQ,SAAU,aAIlBtH,eAAgB,SAASuH,GACrB,MAAOA,IAGXt0B,MACI4tB,qBAAsB,gCACtBjH,eAAgB,aAChBuC,aAAc,KAAM,KAAM,KAAM,KAAM,KAAM,OAGhDhM,YACIH,SAAS,EACTmG,OAAQ,SACRxI,SAAU,iBACV8I,iBACA/I,WAGJkJ,MACIqI,UAAU,EACVuI,iBAAiB,EACjBtI,UAAU,GAGd3Q,OACIC,YAAa,aAGjB8I,OACID,cAAe,KACf7I,YAAa,gBAGjBgP,QACIvf,KAAK,EAGLgV,OAAQ,MASZqJ,gBAIAlJ,SACIzF,SAAU,KACVD,UACA+I,iBACApD,gBAAgB,GAIpBxS,MAEIrR,QAAS,UAGTqvB,YAAY,EAGZ4I,iBAAiB,GAIrBxhB,SACIyhB,cAAe,KAGfC,cAAc,EAGdC,QAAQ,EAIRC,YAAa,KAEbC,eAAgB,GAEhBC,YAAa,kBAEbC,aAAa,EAGbC,UAGJjP,aACIC,gBAAgB,EAChBsH,mBAAmB,EACnB9G,kBAAkB,IAK1BlqB,GAAG6B,OAAOxB,KAAK0e,SAAU5G,GAAG,GAE5B9X,KAAKojB,YACLpjB,KAAKysB,qBACLzsB,KAAK8hB,wBAEL9hB,KAAKk1B,iBACLl1B,KAAKiqB,gBAAmB,GAAItqB,IAAGsS,eAE/BjS,KAAKse,cACLte,KAAKijB,gBACLjjB,KAAKkjB,kBACLljB,KAAKmjB,mBACLnjB,KAAKqhB,kBAELrhB,KAAK6iB,qBAAuB,EAC5B7iB,KAAKyiB,aAAe,EACpBziB,KAAKmgB,YAAcngB,KAAK8oB,2BAExB9oB,KAAK4uB,4BAEL5uB,KAAKqkB,oBAAsBrkB,KAAKgoB,aAAahoB,KAAK0e,SAASiI,QAAQE,eACnE7mB,KAAKwkB,8BAAgCxkB,KAAKgoB,aAAahoB,KAAK0e,SAAS6B,WAAWsG,eAEhF7mB,KAAK4kB,uBAAyB5kB,KAAKgoB,aAAahoB,KAAK0e,SAAS6B,WAAWzC,QAEzE9d,KAAKgiB,eAAiBhiB,KAAKgoB,aAAahoB,KAAK0e,SAASiI,QAAQ5I,UAC9D/d,KAAK0kB,yBAA2B1kB,KAAKgoB,aAAahoB,KAAK0e,SAAS6B,WAAWxC,UAE3E/d,KAAK4f,SAAW5f,KAAKkqB,uBAErBlqB,KAAK+yB,eAAiBpzB,GAAG2mB,yBAA2BtmB,KAAKqmB,uBAErDrmB,KAAK0e,SAAS2E,SACdrjB,KAAK+sB,iBAAmB/sB,KAAKkpB,qBACzBtpB,QAASI,KAAK0e,SAAS2E,OACvBlL,MAAOnY,KAAK0e,SAASrb,KAAK2mB,iBAC3BvQ,eAGPzZ,KAAKwsB,4BAELxsB,KAAK2tB,sBAED3tB,KAAK0e,SAASgJ,MAAMD,gBAChB9nB,GAAG6nB,aACHxnB,KAAKujB,cAAgBvjB,KAAKunB,sBAG1BvnB,KAAK8F,IAAI,iCAAkC,UAInD9F,KAAK6zB,0BAEL7zB,KAAKohB,gBAAkBzhB,GAAG24B,gBAAkB,GAAI34B,IAAG24B,eAAe34B,GAAGgQ,KAAK3P,KAAK8F,IAAK9F,OACpFA,KAAK0jB,sBAEL1jB,KAAK2jB,kCACL3jB,KAAK4jB,+BAEL5jB,KAAKu4B,QAAW54B,GAAGukB,QAAU,GAAIvkB,IAAGukB,OAAOlkB,KAAK0e,SAASrI,QAAS1W,GAAGgQ,KAAK3P,KAAK8F,IAAK9F,WAChFA,KAAKu4B,QAAQnY,UACbpgB,KAAKquB,sBAAwB1uB,GAAGgQ,KAAK3P,KAAKu4B,QAAQC,cAAex4B,KAAKu4B,UAGtE54B,GAAG84B,eAAiB94B,GAAGkT,kBAAkBsD,cACzCnW,KAAK6jB,eAAiB,GAAIlkB,IAAG84B,cACzB94B,GAAGgQ,KAAK3P,KAAKizB,iBAAkBjzB,MAE/B,SAASoa,GACL,GAAIse,GAAQtgB,EAAK+H,YAAYvE,UAAUxB,GAAIA,GAC3C,OAAQse,IAASA,EAAMld,MAAS,KAK5Cxb,KAAK4iB,kBAAoB5iB,KAAK0e,SAAS+K,WAAWsI,WAItDpyB,GAAG61B,kBAAkBnvB,UAAY1G,GAAGge,cACpChe,GAAG6B,OAAO7B,GAAG61B,kBAAkBnvB,UAAW1G,GAAG8lB,mBClTjD9lB,GAAGg5B,cAAgB,SAAS7gB,GACxB,YA2CA,SAAS8gB,KACL,MAAOj5B,IAAG2F,SAAS,MAAO,OAAQ,QAAS4S,EAAQqO,SAAW,EAMlE,QAASsS,GAAyBzU,GAC9B,GAAI0U,IAAoB,CASxB,OAPAn5B,IAAG+C,KAAKo2B,EAAmB,SAASn2B,EAAKo2B,GACrC,GAAIp5B,GAAG2F,SAAS,SAAU,kBAAmB,mBAAoB,gBAAiByzB,GAAU,EAExF,MADAD,IAAoB,GACb,IAIRA,EAGX,QAASE,GAAMzlB,GAEX,MAAO2E,GAAQ8O,KAAKqI,UAAoCvrB,SAAxByP,EAAIC,gBAIxC,QAASylB,KACL,GAAI7R,EAeJ,QAbIviB,OAAOiF,gBAAkBjF,OAAOkF,iBAChCqd,EAAWznB,GAAGiK,oBAEmB9F,SAA7BsjB,EAAS5T,kBACT4T,EAAW,GAAI1T,gBAEf0T,EAASzb,OAAS,aAClByb,EAASvb,QAAU,aACnBub,EAAS8R,UAAY,aACrB9R,EAAS+R,WAAa,eAIvB/R,EAIX,QAASgS,GAAYhf,EAAIif,GACrB,GAAIjS,GAAWkS,EAAYlf,GAAI7G,GAkB/B,OAhBK6T,KAEGA,EADAiS,EACWA,EAGPnhB,EAAQ8O,KAAKqI,SACF4J,IAGAt5B,GAAGiK,oBAItB0vB,EAAYlf,GAAI7G,IAAM6T,GAGnBA,EAIX,QAASmS,GAAQnf,GACb,GAEIof,GAFA/oB,EAAI9Q,GAAG2F,QAAQm0B,EAAOrf,GACtBiS,EAAMnU,EAAQuO,qBAGX6S,GAAYlf,GACnBqf,EAAMvd,OAAOzL,EAAG,GAEZgpB,EAAM/zB,QAAU2mB,GAAO5b,EAAI4b,IAC3BmN,EAASC,EAAMpN,EAAM,GACrBqN,EAAYF,IAIpB,QAAS5O,GAAWxQ,EAAIuf,GACpB,GAAIpmB,GAAM6lB,EAAYhf,GAClBmM,EAASrO,EAAQqO,OACjBc,EAAUsS,KAAa,CAE3BJ,GAAQnf,GAEJiN,EACAvhB,EAAIygB,EAAS,gBAAkBnM,EAAK,cAAe,SAE7C4e,EAAMzlB,IAASqmB,EAAqBrmB,EAAImH,UAC9C2M,GAAU,EACVvhB,EAAIygB,EAAS,gBAAkBnM,EAAK,+BAAiC7G,EAAImH,OAAQ,UAGrFxC,EAAQ0S,WAAWxQ,EAAI7G,EAAK8T,GAGhC,QAASwS,GAAUzf,GACf,GAEI0D,GAFAgc,EAAiBR,EAAYlf,GAAI2f,iBACjCC,EAAiB9hB,EAAQ8hB,cAqB7B,OAlBI9hB,GAAQ4O,YAAY7E,MACpBnE,EAAS5F,EAAQ4O,YAAY7E,IAAI7H,IAGjC0f,GACAn6B,GAAG+C,KAAKo3B,EAAgB,SAASp4B,EAAMkB,GACnCkb,EAASA,MACTA,EAAOpc,GAAQkB,IAInBo3B,GACAr6B,GAAG+C,KAAKs3B,EAAgB,SAASt4B,EAAMkB,GACnCkb,EAASA,MACTA,EAAOpc,GAAQkB,IAIhBkb,EAGX,QAAS4b,GAAYtf,EAAI6f,GACrB,GAIIC,GAJA3mB,EAAM6lB,EAAYhf,EAAI6f,GACtB1T,EAASrO,EAAQqO,OACjBzI,EAAS+b,EAAUzf,GACnB+f,EAAUb,EAAYlf,GAAI+f,OA+C9B,OA5CAjiB,GAAQkiB,OAAOhgB,GAEf8f,EAAMG,EAAUjgB,EAAI0D,EAAQwb,EAAYlf,GAAIkgB,uBAGxCtB,EAAMzlB,IACNA,EAAI5H,OAAS4uB,EAAkBngB,GAC/B7G,EAAI1H,QAAU2uB,EAAmBpgB,IAGjC7G,EAAIknB,mBAAqBC,EAA8BtgB,GAG3DugB,EAA0BvgB,GAG1B7G,EAAIqnB,KAAKrU,EAAQ2T,GAAK,GAIlBhiB,EAAQ8O,KAAKqI,UAAYnX,EAAQ8O,KAAK4Q,kBAAoBoB,EAAMzlB,KAChEA,EAAIC,iBAAkB,GAG1BqnB,EAAWzgB,GAEXtU,EAAI,WAAaygB,EAAS,gBAAkBnM,GAExC+f,EACA5mB,EAAIunB,KAAKX,GAEJY,IAAgCjd,EACrCvK,EAAIunB,OAEChd,GAAU5F,EAAQ8iB,aAAe9iB,EAAQ8iB,YAAY/xB,cAAc3D,QAAQ,sCAAwC,EACxHiO,EAAIunB,KAAKn7B,GAAGsQ,QAAQ6N,EAAQ,KAEvBA,GAAU5F,EAAQ8iB,aAAe9iB,EAAQ8iB,YAAY/xB,cAAc3D,QAAQ,qBAAuB,EACvGiO,EAAIunB,KAAKxpB,KAAK2pB,UAAUnd,IAGxBvK,EAAIunB,KAAKhd,GAGNvK,EAGX,QAAS8mB,GAAUjgB,EAAI0D,EAAQwc,GAC3B,GAAIvc,GAAW7F,EAAQ6O,cAAc9E,IAAI7H,GACrC8gB,EAAY5B,EAAYlf,GAAI8gB,SAehC,OAZiBp3B,SAAbo3B,IACAnd,GAAY,IAAMmd,GAGlBH,GAA+Bjd,IAC/BC,EAAWpe,GAAGsQ,QAAQ6N,EAAQC,IAG9Buc,IACAvc,EAAWpe,GAAGsQ,QAAQqqB,EAAuBvc,IAG1CA,EAKX,QAAS2c,GAA8BtgB,GACnC,MAAO,YACgC,IAA/Bgf,EAAYhf,GAAI+gB,YAChBvQ,EAAWxQ,IAKvB,QAASugB,GAA0BvgB,GAC/B,GAAIoQ,GAAatS,EAAQsS,UAErBA,KACA4O,EAAYhf,GAAIvQ,OAAOsvB,WAAa,SAAStqB,GACrCA,EAAEusB,kBACF5Q,EAAWpQ,EAAIvL,EAAE4b,OAAQ5b,EAAE6b,SAQ3C,QAAS6P,GAAkBngB,GACvB,MAAO,YACHwQ,EAAWxQ,IAMnB,QAASogB,GAAmBpgB,GACxB,MAAO,YACHwQ,EAAWxQ,GAAI,IAIvB,QAASygB,GAAWzgB,GAChB,GAAI7G,GAAM6lB,EAAYhf,GAClByM,EAAgB3O,EAAQ2O,cACxBwU,EAAkB/B,EAAYlf,GAAIkhB,sBAClC/U,EAASrO,EAAQqO,OACjBgV,IAGCvC,GAAMzlB,KACP2E,EAAQsjB,cAAgBjoB,EAAIkoB,iBAAiB,SAAUvjB,EAAQsjB,cAG3DtjB,EAAQwjB,qCAKHxjB,EAAQ8O,KAAKqI,UAAcuJ,MAAoBC,EAAyBhS,KACzEtT,EAAIkoB,iBAAiB,mBAAoB,kBACzCloB,EAAIkoB,iBAAiB,gBAAiB,eAI1CvjB,EAAQ8iB,aAA2B,SAAXzU,GAAgC,QAAXA,GAC7ChT,EAAIkoB,iBAAiB,eAAgBvjB,EAAQ8iB,aAGjDr7B,GAAG6B,OAAO+5B,EAAY57B,GAAG6G,WAAWqgB,GAAiBA,EAAczM,GAAMyM,GACzElnB,GAAG6B,OAAO+5B,EAAYF,GAEtB17B,GAAG+C,KAAK64B,EAAY,SAAS75B,EAAMkB,GAC/B2Q,EAAIkoB,iBAAiB/5B,EAAMkB,MAKvC,QAASg3B,GAAqB+B,GAC1B,MAAOh8B,IAAG2F,QAAQ4S,EAAQ0jB,wBAAwB1jB,EAAQqO,QAASoV,IAAiB,EAGxF,QAASE,GAAczhB,EAAI6f,EAAQiB,EAAWnB,EAAkBO,EAAuBgB,EAAmBnB,GACtGb,EAAYlf,IACR8gB,UAAWA,EACXnB,iBAAkBA,EAClBO,sBAAuBA,EACvBgB,kBAAmBA,EACnBnB,QAASA,EAGb,IAAIttB,GAAM4sB,EAAM52B,KAAKuX,EAGrB,IAAIvN,GAAOqL,EAAQuO,eACf,MAAOiT,GAAYtf,EAAI6f,GA7U/B,GAAIn0B,GAAKi1B,EACLtB,KACAH,KACAphB,GACIsjB,aAAc,KACdM,cAAe,QAAS,OAAQ,OAChCvV,OAAQ,OACRyU,YAAa,oCACbvU,eAAgB,EAChBI,iBACAE,iBACAD,eACAkT,kBACA0B,oCAAoC,EACpCE,yBACIG,QAAS,IAAK,IAAK,KACnBC,OAAQ,IAAK,IAAK,IAAK,IAAK,KAC5BC,MAAO,IAAK,IAAK,IAAK,IAAK,KAC3BC,KAAM,IAAK,IAAK,IAAK,IAAK,KAC1BC,KAAM,MAEVnV,MACIqI,UAAU,EACVuI,iBAAiB,GAErB9xB,IAAK,SAAS4B,EAAK1B,KACnBo0B,OAAQ,SAAShgB,KACjBwQ,WAAY,SAASxQ,EAAIgN,EAAUC,KACnCmD,WAAY,KAMpB,IAHA7qB,GAAG6B,OAAO0W,EAASJ,GACnBhS,EAAMoS,EAAQpS,IAEVnG,GAAG2F,QAAQ4S,EAAQ4jB,aAAc5jB,EAAQqO,QAAU,EACnD,KAAM,IAAIplB,OAAM,IAAM+W,EAAQqO,OAAS,wDA8S3CwU,GAAiD,QAAnB7iB,EAAQqO,QAAuC,WAAnBrO,EAAQqO,OAElE5mB,GAAG6B,OAAOxB,MAENo8B,cAAe,SAAShiB,GACpB,GAAIiiB,GAAMve,EAAQsG,EAAS+V,EAASmC,EAAahC,CAEjD,QAEIiC,SAAU,SAASC,GAEf,MADAH,GAAOG,EACAx8B,MAOXy8B,WAAY,SAAS1C,GAEjB,MADAjc,GAASic,EACF/5B,MAGX08B,gBAAiB,SAASC,GAEtB,MADArC,GAAwBqC,EACjB38B,MAIX48B,YAAa,SAAStB,GAElB,MADAlX,GAAUkX,EACHt7B,MAIX68B,YAAa,SAASC,GAElB,MADA3C,GAAU2C,EACH98B,MAIX+8B,gBAAiB,WAEb,MADAT,IAAc,EACPt8B,MAIX86B,KAAM,SAASb,GAKX,MAJIqC,IAAe38B,GAAG2F,SAAS,MAAO,UAAW4S,EAAQqO,SAAW,IAChEzI,EAAOkf,aAAc,GAAIC,OAAOC,WAG7BrB,EAAczhB,EAAI6f,EAAQoC,EAAMve,EAAQwc,EAAuBlW,EAAS+V,MAK3FgD,SAAU,SAAS/iB,GACfmf,EAAQnf,OC1YpBza,GAAGy9B,cAAgB,SAAS/hB,GACxB,YAEA,IAAIgiB,GAAQhiB,EAAKgiB,MACbC,KACAxS,EAAWuS,EAAMvS,SACjBzK,EAAUgd,EAAMhd,OAEpB1gB,IAAG6B,OAAOxB,MACNuQ,IAAK,SAAS6J,EAAImjB,GACdD,EAAUljB,GAAMmjB,EAChBD,EAAUljB,GAAIjK,SAGlBwP,OAAQ,SAASvF,GACb,GAAIhC,GAAOpY,KACP+qB,EAA2B,GAAIprB,IAAG+L,QAClC8xB,EAAiB1S,EAAS1Q,EAAIiG,EAAQjG,GAAK2Q,EAE/CyS,GAAezmB,KAAK,WACZqB,EAAKuI,QAAQvG,KACbkjB,EAAUljB,GAAI+iB,UAAW,EACzB/kB,EAAK+Z,QAAQ/X,IAEjB2Q,EAAyBnf,aAIjCumB,QAAS,SAAS/X,SACPkjB,GAAUljB,IAGrBqjB,oBAAqB,SAASrjB,GAC1B,MAAOkjB,GAAUljB,GAAI5K,KAGzBmR,QAAS,SAASvG,GACd,MAAyBtW,UAAlBw5B,EAAUljB,IAGrBN,MAAO,WACHwjB,MAGJI,cAAe,SAAStjB,GACpB,MAAOkjB,GAAUljB,IAGrBujB,qBAAsB,SAASvjB,EAAIwjB,GAC/BN,EAAUljB,GAAI5K,IAAMouB,GAGxBC,aAAc,SAASzjB,GACnB,QAASkjB,EAAUljB,GAAI+iB,aCpDnCx9B,GAAGusB,wBAA0B,SAASpU,EAAGsS,GACrC,YAEA,IAGc0T,GAAsBh4B,EAAKsoB,EAHrC2P,EAAa/9B,KACbg+B,GAAmB,EACnBC,GAA6B,EAGjC/lB,GACI4O,eACAL,eAAgB,EAChBnR,UACI8K,SAAS,EACT1W,UACI0W,SAAS,IAGjBta,IAAK,SAAS4B,EAAK1B,KACnBwkB,WAAY,SAASpQ,EAAIpI,EAAUyY,EAAQC,KAC3CE,WAAY,SAASxQ,EAAIpI,EAAU+e,EAAUxd,KAC7CuX,SAAU,SAAS1Q,EAAIpI,KACvBiZ,aAAc,SAAS7Q,KACvB+Q,SAAU,SAAS/Q,EAAIpI,KACvBqZ,cAAe,SAASjR,EAAIpI,EAAUsZ,KACtCE,qBAAsB,SAASpR,EAAIkR,EAAWyF,EAAUxd,KACxDmY,YAAa,SAAStR,EAAIpI,EAAU+e,EAAUxd,KAC9CkY,SAAU,SAASrR,EAAIpI,EAAUsZ,KACjCO,cAAe,SAASzR,EAAIgC,KAC5BiE,QAAS,SAASjG,KAClB0R,QAAS,SAAS1R,EAAIqC,KACtBwP,SAAU,SAAS7R,KACnByC,mBAAoB,SAASzC,KAC7B0C,cAAe,SAAS1C,MAG5B8jB,GAEI1mB,KAAM,SAAS4C,EAAI+jB,EAAUpN,EAAUxd,GACnC,GAAI+X,GAAY8C,EAAQgQ,cAAchkB,EAAI+jB,EAE1C/P,GAAQsP,cAActjB,GAAIikB,kBAAmB,QAEtCjQ,GAAQsP,cAActjB,GAAIjK,KAAKmuB,cAAcH,GACpD/P,EAAQsP,cAActjB,GAAIqQ,QAAUa,EAAU9P,KAE9CtD,EAAQsT,qBAAqBpR,EAAIgU,EAAQmQ,yBAAyBjT,GAAYyF,EAAUxd,IAK5FirB,SAAU,SAASpkB,GACf,GAAIoB,GAAOtD,EAAQqK,QAAQnI,GACvB1Y,EAAOwW,EAAQmI,QAAQjG,EAE3BtU,GAAI,qCAAuCsU,EAAK,qBAChDgU,EAAQqQ,eAAerkB,GAAIrD,KACvB,SAASga,EAAUxd,GACfzN,EAAI,2BAA6BsU,EAEjC,IAAIskB,GAAoB70B,EAAO80B,kBAAkB5N,GAAU,EAE3D7Y,GAAQsS,WAAWpQ,EAAI1Y,EAAM8Z,EAAMA,GACnC4S,EAAQwQ,+BAA+BxkB,GACvCvQ,EAAOg1B,QAAQzkB,EAAIskB,EAAmBnrB,IAE1C,SAASwd,EAAUxd,GACf,GAAImrB,GAAoB70B,EAAO80B,kBAAkB5N,GAAU,EAE3DjrB,GAAI,yCAA2CsU,EAAK,MAAQskB,EAAkB10B,MAAO,SAEjF00B,EAAkB5kB,OAClBokB,EAAQpkB,MAAMM,GAGblC,EAAQwT,YAAYtR,EAAI1Y,EAAMg9B,EAAmBnrB,IAClD1J,EAAOg1B,QAAQzkB,EAAIskB,EAAmBnrB,MAMtDurB,aAAc,SAAS1kB,GACnB,QAASgU,EAAQsP,cAActjB,GAAI9E,SAASypB,UAAUr5B,QAG1Ds5B,SAAU,SAAS5kB,GACf,GAAI6kB,GAAU7Q,EAAQsP,cAActjB,GAAI9E,SAASypB,UAAU1sB,OAM3D,OAJI4sB,IAAW7Q,EAAQ8Q,gBAAgB9kB,KACnC6kB,EAAU,MAGPA,GAGXnlB,MAAO,SAASM,GACZtU,EAAI,8FAAgGsU,EAAI,SAExGgU,EAAQwQ,+BAA+BxkB,GACvCgU,EAAQ+Q,mBAAmB/kB,GAC3BgU,EAAQsP,cAActjB,GAAIqQ,OAAS,GAGvC2U,SAAU,SAAShlB,GACf,GAAIoB,GAAOtD,EAAQqK,QAAQnI,GACvB1Y,EAAOwW,EAAQmI,QAAQjG,GACvB+jB,EAAWD,EAAQc,SAAS5kB,GAC5BkR,EAAY8C,EAAQgQ,cAAchkB,EAAI+jB,GACtCzG,EAAWtJ,EAAQsP,cAActjB,GAAIikB,iBACrCgB,EAAmBjR,EAAQsP,cAActjB,GAAI9E,SAASgqB,cAElB,OAApClR,EAAQsP,cAActjB,GAAIqQ,SAC1B2D,EAAQsP,cAActjB,GAAIqQ,OAAS,GAInCiN,GAAYxf,EAAQuT,SAASrR,EAAI1Y,EAAM4pB,MAAe,IACtD4S,EAAQpkB,MAAMM,GACd+jB,EAAWD,EAAQc,SAAS5kB,GAC5BkR,EAAY8C,EAAQgQ,cAAchkB,EAAI+jB,GACtCzG,GAAW,GAIC,MAAZyG,GAAgD,IAA5BkB,EAAiB35B,OACrCw4B,EAAQM,SAASpkB,IAKjBtU,EAAInG,GAAG8H,OAAO,oEAAqE2S,EAAI+jB,EAAU7S,EAAU3gB,MAAQ,EAAG2gB,EAAU1gB,IAAK4Q,IACrItD,EAAQmT,cAAcjR,EAAI1Y,EAAM0sB,EAAQmQ,yBAAyBjT,IACjE+T,EAAiBx8B,KAAKs7B,GACtB/P,EAAQsP,cAActjB,GAAI9E,SAASgqB,WAAaD,EAE5CpB,GACAsB,EAAkB3E,KAAKxgB,EAAI+jB,GAG3BF,GAA8BsB,EAAkBC,aAAepR,EAAQsP,cAActjB,GAAI9E,SAASypB,UAAUr5B,QAC5Gw4B,EAAQkB,SAAShlB,GAGrBgU,EAAQqR,YAAYrlB,EAAI+jB,EAAUzG,GAAU3gB,KAExC,SAAiBga,EAAUxd,GACvBzN,EAAI,wCAA0CsU,EAAK,WAAa+jB,GAEhE/P,EAAQsR,iBAAiBtlB,EAAI+jB,EAE7B,IAAIkB,GAAmBjR,EAAQsP,cAActjB,GAAI9E,SAASgqB,eACtDK,EAAmB91B,EAAO80B,kBAAkB5N,GAAU,GACtD6O,EAAqBjgC,GAAG2F,QAAQ+5B,EAAkBlB,EAEtDr4B,GAAInG,GAAG8H,OAAO,8CAA+C02B,EAAU/jB,IAEvE8jB,EAAQ1mB,KAAK4C,EAAI+jB,EAAUwB,EAAkBpsB,GAEzCqsB,GAAsB,GACtBP,EAAiBnjB,OAAO0jB,EAAoB,GAGhDxR,EAAQyR,0BAA0BzlB,GAE7B8jB,EAAQY,aAAa1kB,IAAmC,IAA5BilB,EAAiB35B,OAGzCw4B,EAAQY,aAAa1kB,GAC1B8jB,EAAQkB,SAAShlB,GAGjBtU,EAAInG,GAAG8H,OAAO,oGAAqG2S,EAAI9I,KAAK2pB,UAAUoE,KANtInB,EAAQM,SAASpkB,IAWzB,SAAiB2W,EAAUxd,GACvBzN,EAAI,qCAAuCsU,EAAK,WAAa+jB,GAE7D/P,EAAQsR,iBAAiBtlB,EAAI+jB,EAE7B,IACI2B,GADAH,EAAmB91B,EAAO80B,kBAAkB5N,GAAU,EAGtD4O,GAAiB7lB,MACjBokB,EAAQpkB,MAAMM,IAGd0lB,EAAgBngC,GAAG2F,QAAQ8oB,EAAQsP,cAActjB,GAAI9E,SAASgqB,WAAYnB,GACtE2B,GAAiB,IACjB1R,EAAQsP,cAActjB,GAAI9E,SAASgqB,WAAWpjB,OAAO4jB,EAAe,GACpE1R,EAAQsP,cAActjB,GAAI9E,SAASypB,UAAUgB,QAAQ5B,KAMxD/P,EAAQsP,cAActjB,GAAIjK,KAAK6vB,gBAG5B/B,IACA7P,EAAQsP,cAActjB,GAAIjK,KAAK6vB,eAAgB,EAE/Cl6B,EAAInG,GAAG8H,OAAO,mFAAoF6J,KAAK2pB,UAAU70B,OAAO65B,KAAK7R,EAAQ8R,SAAS9lB,KAAO9I,KAAK2pB,UAAU7M,EAAQsP,cAActjB,GAAI9E,SAASgqB,cACvM3/B,GAAG+C,KAAK0rB,EAAQ8R,SAAS9lB,GAAK,SAAS+lB,EAAMC,GACzCt6B,EAAInG,GAAG8H,OAAO,sDAAuD2S,EAAI+lB,EAAMC,EAAMjF,aACrFiF,EAAMC,QAGND,EAAME,YAAa,IAIvBlS,EAAQmS,0BAA0BnmB,GAIlCmlB,EAAkBiB,KAAKpmB,GAAI,IAG1BlC,EAAQwT,YAAYtR,EAAI1Y,EAAMi+B,EAAkBpsB,IAIjD1J,EAAOg1B,QAAQzkB,EAAIulB,EAAkBpsB,MAKhDiE,KAAK,WACF4W,EAAQqS,SAASrmB,EAAI+jB,QAMzCoB,GACImB,SACAC,eACAC,YAEApB,UAAW,WACP,GAAInT,GAAMnU,EAAQuO,eACdoa,EAAwB,EACxBC,EAAkB,CAOtB,OALAnhC,IAAG+C,KAAK68B,EAAkBoB,YAAa,SAAS9f,EAAQkgB,GACpDF,IACAC,GAAmBC,EAAiBr7B,SAGjC2mB,GAAOkT,EAAkBmB,MAAMh7B,OAASm7B,EAAwBC,IAM3EN,KAAM,SAASpmB,EAAI4mB,GACf,GAGIxH,GAHAyH,GAAaD,EACbE,EAAevhC,GAAG2F,QAAQi6B,EAAkBqB,SAAUxmB,GACtD+mB,EAAmBxhC,GAAG2F,QAAQi6B,EAAkBmB,MAAOtmB,SAGpDmlB,GAAkBoB,YAAYvmB,GAEjCvQ,EAAOu3B,eAAehnB,YAAeza,IAAG8X,YACxC3R,EAAI,uCAAyCsU,EAAK,qCAC3CgU,GAAQsP,cAActjB,GAAImF,MAIjC2hB,GAAgB,EAChB3B,EAAkBqB,SAAS1kB,OAAOglB,EAAc,GAG3CD,GAAaE,GAAoB,IACtC5B,EAAkBmB,MAAMxkB,OAAOilB,EAAkB,GAEjD3H,EAAS+F,EAAkBqB,SAASvuB,QAChCmnB,GAAU,IACV+F,EAAkBmB,MAAM79B,KAAK22B,GAC7B3vB,EAAOc,MAAM6uB,MAKzB6H,sBAAuB,WACnB,GAAIC,KAqBJ,OAjBA3hC,IAAG+C,KAAK68B,EAAkBoB,YAAa,SAAS9f,EAAQ0gB,GAChDA,GAAUA,EAAO77B,QACjB47B,EAAmBz+B,KAAK2+B,SAAS3gB,MAMzClhB,GAAG+C,KAAK68B,EAAkBmB,MAAO,SAAS/9B,EAAKke,GACtC0e,EAAkBoB,YAAY9f,IAC/BygB,EAAmBz+B,KAAK2+B,SAAS3gB,MAKzCygB,EAAqBA,EAAmBvxB,OAAOwvB,EAAkBqB,WAKrEa,kBAAmB,SAASrnB,GACxB,MAAOza,IAAG2F,QAAQi6B,EAAkBmB,MAAOtmB,IAAO,GAGtDwgB,KAAM,SAASxgB,EAAI+jB,GAKf,MAJgB,OAAZA,GACAoB,EAAkBqB,SAAS/9B,KAAKuX,KAGhCmlB,EAAkBC,cACF,MAAZrB,GACAoB,EAAkBqB,SAASc,MAC3BnC,EAAkBmB,MAAM79B,KAAKuX,KAG5B,WACG,GAAIunB,GAAkBpC,EAAkBoB,YAAYvmB,MACpDunB,GAAgB9+B,KAAKs7B,GACrBoB,EAAkBoB,YAAYvmB,GAAMunB,MAIrC,IAMf7nB,MAAO,WACHylB,EAAkBqB,YAClBrB,EAAkBmB,WAI1BkB,GACI9G,KAAM,SAAS1gB,EAAI1Y,GACf0sB,EAAQsP,cAActjB,GAAIqQ,OAAS,EAEnC3kB,EAAI,qCAAuCsU,GAC3CgU,EAAQyT,WAAWznB,GAAIrD,KACnB,SAASga,EAAUkJ,GACfn0B,EAAI,uCAAyCsU,EAE7C,IAAIulB,GAAmB91B,EAAO80B,kBAAkB5N,GAAU,GACtDvV,EAAOtD,EAAQqK,QAAQnI,EAE3BlC,GAAQsS,WAAWpQ,EAAI1Y,EAAM8Z,EAAMA,GACnC3R,EAAOi4B,aAAa1nB,EAAIulB,GACxB91B,EAAOg1B,QAAQzkB,EAAIulB,EAAkB1F,IAGzC,SAASlJ,EAAUkJ,GACfn0B,EAAI,oCAAsCsU,EAE1C,IAAIulB,GAAmB91B,EAAO80B,kBAAkB5N,GAAU,EAErD7Y,GAAQwT,YAAYtR,EAAI1Y,EAAMi+B,EAAkB1F,IACjDpwB,EAAOg1B,QAAQzkB,EAAIulB,EAAkB1F,OAOzDpwB,GACI8V,OAAQ,SAASvF,GACbtU,EAAI,cAAgBsU,GACpBlC,EAAQ4O,YAAY/lB,OAAOqZ,GAC3BmlB,EAAkBiB,KAAKpmB,IAG3BykB,QAAS,SAASzkB,EAAI2W,EAAUkJ,GAC5B,GAAIv4B,GAAOwW,EAAQmI,QAAQjG,EAE3BlC,GAAQ0S,WAAWxQ,EAAI1Y,EAAMqvB,EAAUkJ,GAEnC7L,EAAQsP,cAActjB,IACtBgU,EAAQ2T,YAAc3T,EAAQ2T,WAAW3nB,GAG7CmlB,EAAkBiB,KAAKpmB,IAK3BgnB,eAAgB,SAAShnB,GACrB,MAAQgU,GAAQ4T,UAAY5T,EAAQ4T,SAAS5nB,IACxCgU,EAAQ7M,SAAW6M,EAAQ7M,QAAQnH,IAG5C6nB,YAAa,WACT,GAAIC,GAAc9X,EAAYzqB,GAAGyqB,GAAazqB,GAAGwiC,YAC7CC,EAAuBziC,GAAGkT,kBAAkBsC,cAAgB,MAAQ,MAExEiZ,GAAU,GAAI8T,GAAYE,EAAuB,iBAC7ClqB,GAEI8T,cAAe9T,EAAQ8T,cACvB3L,QAASnI,EAAQmI,QACjBkC,QAASrK,EAAQqK,QACjBO,QAAS5K,EAAQ4K,QACjBhd,IAAKA,EACLglB,SAAU5S,EAAQ4S,SAClBN,WAAYtS,EAAQsS,WACpBqB,cAAe3T,EAAQ2T,gBAI3BuC,EAAQiU,+BACRjU,EAAQiU,iCAIhBC,4BAA6B,SAASloB,GAClC,MAAOlC,GAAQ+T,SAAS7R,IAM5BmoB,WAAY,SAASnoB,EAAIhP,GAIrB,MAAIA,KAASgjB,EAAQ7M,QAAQnH,IAAOhP,YAAgBzL,IAAG8X,WAKnDS,EAAQ+S,aAAa7Q,GAErBtU,EAAI,+CAAiDsU,GACrDhP,EAAKwM,SAASb,KAAK,SAASyrB,GACxB18B,EAAI,mCAAqCsU,GAGzCgU,EAAQqU,WAAWroB,EAAIooB,GAGvBtqB,EAAQ4T,QAAQ1R,EAAIooB,EAAchnB,MAGlC4S,EAAQ+Q,mBAAmB/kB,GAE3BvQ,EAAO64B,uBAAuBtoB,IAIlC,SAASkZ,GACL,GAAIqP,KAEArP,KACAqP,EAAc34B,MAAQspB,GAG1BxtB,EAAInG,GAAG8H,OAAO,yDAA0D2S,EAAIkZ,GAAe,SAE3Fpb,EAAQ0S,WAAWxQ,EAAIlC,EAAQmI,QAAQjG,GAAKza,GAAG6B,OAAOmhC,EAAe7E,GAAuB,MAC5Fj0B,EAAO64B,uBAAuBtoB,GAC9BmlB,EAAkBiB,KAAKpmB,MAOxB,GAHIvQ,EAAO64B,uBAAuBtoB,IAO7CsoB,uBAAwB,SAAStoB,GAC7B,GAAIwoB,GAAa1qB,EAAQ2E,mBAAmBzC,GACxCyoB,GAAiB,CAoBrB,OAlBID,IAAcA,EAAWl9B,QACzBI,EAAI,0CAA4CsU,GAEhDza,GAAG+C,KAAKkgC,EAAY,SAASjgC,EAAKmgC,GAC9B,GAAIj5B,EAAOy4B,4BAA4BQ,IAAgB1U,EAAQ7M,QAAQuhB,GACnED,EAAiBC,IAAc1oB,EAC/BvQ,EAAOk5B,IAAID,OAEV,IAAIj5B,EAAOy4B,4BAA4BQ,GACxC,OAAO,MAKfD,GAAiB,EACjBh5B,EAAOk5B,IAAI3oB,IAGRyoB,GAGXf,aAAc,SAAS1nB,EAAI2W,GACEjtB,SAArBitB,EAAS3U,SACTlE,EAAQ2T,cAAczR,EAAI2W,EAAS3U,UAO3CuiB,kBAAmB,SAASqE,EAAkBvR,GAC1C,GAAIV,GAAWiS,CAcf,OAVKrjC,IAAGuG,SAAS88B,KACbjS,KAEIpxB,GAAGwH,SAAS67B,KAAsBvR,IAClCV,EAAS/mB,MAAQg5B,IAIzBjS,EAASnlB,QAAU6lB,EAEZV,GAGXgS,IAAK,SAAS3oB,GACV,GAAI1Y,GAAOwW,EAAQmI,QAAQjG,EAE3B,KAAK2jB,EAAWpd,QAAQvG,GACpB,KAAM,IAAIza,IAAGwB,MAAMiZ,EAAK,qCAG5BlC,GAAQiT,SAAS/Q,EAAI1Y,GAEjBs8B,GAAoB5P,EAAQ6U,qBAAqB7oB,GACjD8jB,EAAQkB,SAAShlB,GAGjBwnB,EAAO9G,KAAK1gB,EAAI1Y,IAIxBiJ,MAAO,SAASyP,GACZ,GAAI8oB,GAAer5B,EAAOu3B,eAAehnB,EAEzC,OAAI8oB,GACOr5B,EAAO04B,WAAWnoB,EAAI8oB,IAG7Br5B,EAAOk5B,IAAI3oB,IACJ,IAKnBza,IAAG6B,OAAOxB,MAINuQ,IAAK,SAAS6J,EAAImF,GACd6O,EAAQ7d,IAAIP,MAAMhQ,KAAM8H,YAM5B+B,OAAQ,SAASuQ,GACb,QAAImlB,EAAkB3E,KAAKxgB,IAChBvQ,EAAOc,MAAMyP,IAK5B0J,MAAO,SAAS1J,GAWZ,MANI6jB,KACA7P,EAAQsP,cAActjB,GAAIjK,KAAK6vB,eAAgB,GAK/CT,EAAkBkC,kBAAkBrnB,GAC7BvQ,EAAOc,MAAMyP,GAOb2jB,EAAWl0B,OAAOuQ,IAOjCuF,OAAQ,SAASvF,GACb,GAAI+oB,GAAe/U,EAAQzO,OAAOvF,EAE9Bza,IAAGkX,iBAAiBssB,GACpBA,EAAapsB,KAAK,WACdlN,EAAO8V,OAAOvF,KAGb+oB,KAAiB,GACtBt5B,EAAO8V,OAAOvF,IAOtByF,UAAW,WACP,GACIpP,GADA6wB,EAAqB/B,EAAkB8B,uBAK3C,IAAIC,EAAmB57B,OACnB,IAAK+K,EAAI6wB,EAAmB57B,OAAS,EAAG+K,GAAK,EAAGA,IAC5CstB,EAAWpe,OAAO2hB,EAAmB7wB,GAI7C8uB,GAAkBzlB,SAKtByH,QAAS,SAASnH,GACd,MAAIgU,GAAQ4T,UAAY5T,EAAQ4T,SAAS5nB,GAC9BgU,EAAQ4T,SAAS5nB,GAAI1C,cAGzB0W,EAAQ7M,SAAW6M,EAAQ7M,QAAQnH,IAI9CgpB,UAAW,SAAShpB,GAChB,SAAUgU,EAAQ4T,WAAY5T,EAAQ4T,SAAS5nB,KAGnDZ,SAAU,SAASY,GACf,GAAIgU,EAAQ5U,SACR,MAAO4U,GAAQ5U,SAASY,IAIhCN,MAAO,WACHhU,EAAI,4BACJi4B,EAAWle,YACX0f,EAAkBzlB,QAClBsU,EAAQtU,SAGZqY,QAAS,SAAS/X,GACd,GAAI2jB,EAAWpd,QAAQvG,GACnB,MAAOgU,GAAQ+D,QAAQ/X,IAO/BuG,QAAS,SAASvG,GACd,MAAOgU,GAAQzN,QAAQvG,IAG3BkI,sBAAuB;AACnB,MAAI8L,GAAQ9L,sBACD8L,EAAQ9L,4BAavBmb,oBAAqB,SAASrjB,GAC1B,GAAI2jB,EAAWpd,QAAQvG,GACnB,MAAOgU,GAAQqP,oBAAoBrjB,IAS3ClE,MAAO,SAASkE,GACZ,SAAI2jB,EAAWsF,YAAYjpB,IAAOgU,EAAQlY,OAAS6nB,EAAWpd,QAAQvG,IAAOgU,EAAQlY,MAAMkE,MACvFmlB,EAAkBiB,KAAKpmB,GACvBgU,EAAQmS,0BAA0BnmB,IAC3B,IAMfipB,YAAa,SAASjpB,GAClB,QAASgU,EAAQiV,aAAejV,EAAQiV,YAAYjpB,MAI5Dza,GAAG6B,OAAO0W,EAASJ,GACnBhS,EAAMoS,EAAQpS,IACdk4B,EAAmB9lB,EAAQ5C,SAAS8K,SAAWzgB,GAAGkT,kBAAkByC,SACpE2oB,EAA6BD,GAAoB9lB,EAAQ5C,SAAS0hB,WAAW5W,QAE7E0d,EAAwB,WACpB,GAAI/M,KAIJ,OAFAA,GAAS7Y,EAAQoS,oBAAqB,EAE/ByG,KAGXlnB,EAAOo4B,eCluBXtiC,GAAG2jC,qBAAuB,SAASxrB,GAC/B,YAEA,IAAII,IACIpS,IAAK,SAASC,EAASC,MAE3Bu9B,IAEJ5jC,IAAG6B,OAAO0W,EAASJ,GAEnBnY,GAAG6B,OAAOxB,MACNwjC,eAAgB,SAASppB,EAAIhL,GACzB,GAAIq0B,GAA2B,SAAS3P,GAChC1kB,EAAS0kB,EAAMnvB,MAGnBE,QAAOqQ,YACPquB,EAAyBnpB,GAAMza,GAAGkF,QAAQ5E,OAAO,UAAWwjC,GAG5D39B,IAAI,wDAAyD,UAIrE49B,sBAAuB,SAAStpB,GAC5B,GAAIvV,OAAOqQ,YAAa,CACpB,GAAIyuB,GAAWJ,EAAyBnpB,EACpCupB,IACAA,SCvBpBhkC,GAAGikC,kBAAoB,SAASvoB,GAC5B,YAoBA,SAASwoB,GAAYzpB,SACV0pB,GAAiB1pB,GAKpB2pB,IACA9R,aAAa+R,EAA0B5pB,UAChC4pB,GAA0B5pB,GACjC6pB,EAAoBP,sBAAsBtpB,GAG9C,IAAI8pB,GAAS16B,SAAS26B,eAAe/V,EAAQgW,eAAehqB,GACxD8pB,KAIAA,EAAOhzB,aAAa,MAAO,qBAE3BvR,GAAGukC,GAAQnjC,UAQnB,QAASsjC,GAAuBC,GAC5B,MAAOA,GAAWj/B,MAAM,KAAK,GAUjC,QAASk/B,GAAoB7iC,GACzB,GAAIwiC,GAASvkC,GAAGoP,UAAU,yCAA2CrN,EAAO,OAO5E,OALAwiC,GAAOhzB,aAAa,KAAMxP,GAE1BwiC,EAAOpkC,MAAMC,QAAU,OACvByJ,SAASg7B,KAAKrzB,YAAY+yB,GAEnBA,EAUX,QAASO,GAA4BP,EAAQ90B,GACzC,GAAIk1B,GAAaJ,EAAO9pB,GACpByG,EAASwjB,EAAuBC,GAChC/pB,EAAOuI,EAAQjC,EAEnB6jB,GAAgBnqB,GAAQnL,EAIxB00B,EAAiBjjB,GAAUlhB,GAAGukC,GAAQjkC,OAAO,OAAQ,WAC7CmuB,EAAQ5U,SAASqH,KACjB/a,EAAI,mEAAqEw+B,EAAa,KAEtFN,EAA0BM,GAAcrb,WAAW,WAC/C,GAAIqK,GAAe,gEAAkEgR,CACrFx+B,GAAIwtB,EAAc,SAClBlkB,GACIpF,MAAOspB,KAEZ,QAMX2Q,EAAoBT,eAAec,EAAY,SAASv+B,GACpDD,EAAI,2CAA6CC,EAAU,IAC3D,IAGI4+B,GAFA5T,GADSsT,EAAuBC,GACrBlW,EAAQwW,mBAAmB7+B,IACtCwU,EAAOwW,EAASxW,IAGhBA,IAAQmqB,EAAgBnqB,IACxBzU,EAAI,qCAAuCw+B,GAC3CrS,aAAa+R,EAA0BM,UAChCN,GAA0BM,GAEjClW,EAAQyW,iBAAiBP,GAEzBK,EAAiBD,EAAgBnqB,SAE1BmqB,GAAgBnqB,GACvB0pB,EAAoBP,sBAAsBY,GAC1CK,EAAe5T,IAETxW,GACNzU,EAAI,IAAMC,EAAU,2CAxHhC,GAAImS,GAAUmD,EAAKnD,QACfkW,EAAUpuB,KACVq9B,EAAQhiB,EAAKgiB,MACbyH,EAAwBnlC,GAAGoN,cAC3B23B,KACAZ,KACAE,KACAD,EAAS7rB,EAAQ6rB,OACjBja,EAAY5R,EAAQ4R,UACpBhH,EAAUua,EAAMva,QAChBhd,EAAMu3B,EAAMv3B,IACZm+B,EAAsB,GAAItkC,IAAG2jC,sBAAsBx9B,IAAKA,GAkH5DnG,IAAG6B,OAAOxB,KAAM,GAAIL,IAAGy9B,cAAc/hB,IAErC1b,GAAGwM,SAASnM,KAAM,SAASsM,GACvB,OAIIiE,IAAK,SAAS6J,EAAI8S,GACd5gB,EAAOiE,IAAI6J,GAAK7Q,MAAO2jB,IAEvBA,EAAUhc,aAAa,OAAQ4Y,GAG3BoD,EAAUpsB,YACVnB,GAAGutB,GAAWnsB,UAItBoxB,QAAS,SAAS/X,GACdypB,EAAYzpB,GACZ9N,EAAO6lB,QAAQ/X,IAGnBuG,QAAS,SAASvG,GACd,MAAO9N,GAAOqU,QAAQvG,IACkBtW,SAApCsqB,EAAQsP,cAActjB,GAAI7Q,UAK1C5J,GAAG6B,OAAOxB,MACNwZ,SAAU,SAASY,GACf,MAAOgU,GAAQsP,cAActjB,GAAI7Q,OAWrCw7B,iBAAkB,SAASb,EAAQ90B,GAE/B,GAAI41B,EAEAjB,GACAU,EAA4BP,EAAQ90B,GAGpC00B,EAAiBI,EAAO9pB,IAAMza,GAAGukC,GAAQjkC,OAAO,OAAQ,WAMpD,GALA6F,EAAI,yBAA2Bo+B,EAAO9pB,IAKjC8pB,EAAOpjC,WAAZ,CAIA,IAEI,GAAIojC,EAAOe,iBACPf,EAAOe,gBAAgBT,MACkB,SAAzCN,EAAOe,gBAAgBT,KAAKt1B,UAK5B,OAGR,MAAOlF,GAEHlE,EAAI,8EAAgFkE,EAAMjE,QAAU,IAAK,SACzGi/B,GAAsBp5B,SAAS,GAGnCwD,EAAS41B,OAWrBE,cAAe,SAAS9qB,GACpB,GAAIkqB,GAAalW,EAAQgW,eAAehqB,EAExC,OAAOmqB,GAAoBD,IAQ/BO,iBAAkB,SAASzqB,GACMtW,SAAzBggC,EAAiB1pB,KACjB0pB,EAAiB1pB,WACV0pB,GAAiB1pB,KAQhCgqB,eAAgB,SAASvjB,GACrB,MAAOA,GAAS,IAAMikB,GAY1BK,mBAAoB,SAAS9pB,GACzB,GAAIkL,GAASlL,EAAKkL,OACdxI,EAAW1C,EAAK0C,SAChBD,EAASzC,EAAKyC,OACd8X,EAAeva,EAAKua,aACpBwP,EAAa/pB,EAAK+pB,WAClBn0B,EAAOtR,GAAGoP,UAAU,iBAAmBwX,EAAS,2CAChD2T,EAAMnc,CAcV,OAZI6X,GACAj2B,GAAGqR,WAAW8M,EAAQ7M,GAGtBipB,EAAMv6B,GAAGsQ,QAAQ6N,EAAQC,GAG7B9M,EAAKC,aAAa,SAAUgpB,GAC5BjpB,EAAKC,aAAa,SAAUk0B,GAC5Bn0B,EAAKnR,MAAMC,QAAU,OACrByJ,SAASg7B,KAAKrzB,YAAYF,GAEnBA,GAOX2zB,mBAAoB,SAASS,GACzB,GAAItU,KAEJ,KACIA,EAAWpxB,GAAGyR,UAAUi0B,GAE5B,MAAOr7B,GACHlE,EAAI,0DAA4DkE,EAAMjE,QAAU,IAAK,SAGzF,MAAOgrB,OCnSnBpxB,GAAG2lC,iBAAmB,SAASjqB,GAC3B,YAkBA,SAASglB,GAAMjmB,GACXza,GAAG+C,KAAK0rB,EAAQ8R,SAAS9lB,GAAK,SAASmrB,EAAOhyB,GAC1C,GAAIiyB,GAAgBpX,EAAQqX,kBAAkBrrB,EAAImrB,EAElDhyB,GAAIknB,mBAAqB,KACzBlnB,EAAI1J,OAAOsvB,WAAa,KACxB5lB,EAAI8sB,QACJmF,GAAiBA,EAAcrI,UAAYqI,EAAcrI,SAAS/iB,KAvB1E,GAAIgU,GAAUpuB,KACVoqB,EAAY/O,EAAKnD,QAAQkS,UACzBiT,EAAQhiB,EAAKgiB,MACb/nB,EAAW+F,EAAKnD,QAAQ5C,SACxBc,EAASiF,EAAKnD,QAAQ9B,OACtBsvB,EAAapwB,GAAY+F,EAAKnD,QAAQ5C,SAAS8K,SAAWzgB,GAAGkT,kBAAkByC,SAC/EqwB,EAAgBvvB,GAAUiF,EAAKnD,QAAQ9B,OAAOgK,SAAWslB,GAAc/lC,GAAGkT,kBAAkBuD,OAC5FiK,EAAUgd,EAAMhd,QAChBkC,EAAU8a,EAAM9a,QAChBO,EAAUua,EAAMva,QAChBf,EAAcsb,EAAMtb,YACpBiK,EAAgBqR,EAAMrR,cACtBH,EAAgBwR,EAAMxR,cACtBrB,EAAa6S,EAAM7S,WACnB1kB,EAAMu3B,EAAMv3B,GAahBnG,IAAG6B,OAAOxB,KAAM,GAAIL,IAAGy9B,cAAc/hB,IAErC1b,GAAGwM,SAASnM,KAAM,SAASsM,GACvB,OAIIiE,IAAK,SAAS6J,EAAIwrB,GACd,GAAIjmC,GAAGyI,OAAOw9B,IAAgBjmC,GAAGyJ,OAAOw8B,GACpCt5B,EAAOiE,IAAI6J,GAAKmF,KAAMqmB,QAErB,CAAA,KAAIA,YAAuBjmC,IAAG8X,WAI/B,KAAM,IAAItW,OAAM,2CAHhBmL,GAAOiE,IAAI6J,GAAKijB,MAAOuI,IAM3BxX,EAAQyX,eAAezrB,GACvBurB,GAAiBvX,EAAQ0X,uBAAuB1rB,IAGpD+X,QAAS,SAAS/X,GACdimB,EAAMjmB,GACNgU,EAAQwQ,+BAA+BxkB,GACvCgU,EAAQ2T,WAAW3nB,GACnB9N,EAAO6lB,QAAQ/X,OAK3Bza,GAAG6B,OAAOxB,MAEN0/B,iBAAkB,SAAStlB,EAAI+jB,SACpB/P,GAAQsP,cAActjB,GAAIjK,KAAK41B,aAAa5H,IAGvDsC,SAAU,SAASrmB,EAAI+jB,GACnB,GAAI6H,GAAY5X,EAAQsP,cAActjB,GAAIjK,IAEtC61B,GAAUC,YACHD,GAAUC,KAAK9H,GAEtB6H,EAAUE,sBACHF,GAAUE,eAAe/H,IAMxCM,eAAgB,SAASrkB,EAAI+rB,GACzB,GAAIC,GAAehY,EAAQ8Q,gBAAgB9kB,GAAM,EAC7C7G,EAAM6a,EAAQiY,QAAQjsB,EAAIgsB,EAE9B,OAAID,IACO,GAAIxmC,IAAG+L,SAAUE,QAAQu6B,EAAe5yB,GAAMA,IAGlD,GAAI5T,IAAG+L,SAAUE,WAAY2H,IAGxCgO,QAAS,SAASnH,GACd,MAAOgU,GAAQzN,QAAQvG,IAAOgU,EAAQsP,cAActjB,GAAImF,MAG5DyiB,SAAU,SAAS5nB,GACf,MAAOgU,GAAQzN,QAAQvG,IAAOgU,EAAQsP,cAActjB,GAAIijB,OAO5D/a,sBAAuB,WACnB,GAAIgkB,KAmBJ,OAjBAlY,GAAQmY,sBAAsB,SAAS/2B,EAAK0Q,GACxCkO,EAAQmS,0BAA0B,KAAMrgB,EAAW5K,SAASgqB,WAAapf,EAAW5K,SAASypB,UAE7F,IAAIp6B,IACAjD,KAAMwe,EAAWxe,KACjBq9B,UAAW7e,EAAW5K,SAASypB,UAC/BvjB,KAAM0E,EAAW1E,KACjBjB,KAAM2F,EAAW3F,KAGjB2F,GAAW1Q,MACX7K,EAAK6K,IAAM0Q,EAAW1Q,KAG1B82B,EAAmBzjC,KAAK8B,KAGrB2hC,GAGXjD,YAAa,SAASjpB,GAClB,QAAS9E,GAAY8Y,EAAQzN,QAAQvG,KAAQgU,EAAQsP,cAActjB,GAAIosB,cAG3EjG,0BAA2B,SAASnmB,EAAIqsB,EAAeC,GACnD,GAAIpH,GAAamH,GAAiBrY,EAAQsP,cAActjB,GAAI9E,SAASgqB,WACjEP,EAAY2H,GAAgBtY,EAAQsP,cAActjB,GAAI9E,SAASypB,SAE/DO,KACAx5B,EAAInG,GAAG8H,OAAO,yDAA0D6J,KAAK2pB,UAAUqE,KACvFA,EAAWqH,UACXhnC,GAAG+C,KAAK48B,EAAY,SAAS38B,EAAKw7B,GAC9BY,EAAUgB,QAAQ5B,KAEtBmB,EAAW55B,OAAS,IAI5BwQ,MAAO,SAASkE,GACZ,GAAIgU,EAAQzN,QAAQvG,GAIhB,MAHAtU,GAAInG,GAAG8H,OAAO,4DAA6D2S,EAAIiG,EAAQjG,KACvFgU,EAAQsP,cAActjB,GAAIwsB,QAAS,EACnCvG,EAAMjmB,IACC,GAIf+kB,mBAAoB,SAAS/kB,GACzB,GAAI9E,GAAY8Y,EAAQzN,QAAQvG,GAAK,CACjC,GACIysB,GACAp2B,EAFA4G,EAAQ+W,EAAQsP,cAActjB,EAQlC,UAJO/C,GAAM/B,SAEb+B,EAAM/B,YACNuxB,EAAczY,EAAQ8Q,gBAAgB9kB,GAClCysB,EAAc,GAAKvxB,EAAS2hB,UAAW,CAKvC,IAJA5f,EAAM/B,SAAS8K,SAAU,EACzB/I,EAAM/B,SAASwxB,MAAQD,EACvBxvB,EAAM/B,SAASypB,aAEVtuB,EAAI,EAAGA,EAAIo2B,EAAap2B,IACzB4G,EAAM/B,SAASypB,UAAUl8B,KAAK4N,EAGlC2d,GAAQyX,eAAezrB,OAGvB/C,GAAM/B,SAAS8K,SAAU,IAKrCqiB,WAAY,SAASroB,EAAI2sB,GACjB3Y,EAAQzN,QAAQvG,KAChBgU,EAAQsP,cAActjB,GAAImF,KAAOwnB,IAIzChF,WAAY,SAAS3nB,GACjB,GAAI4rB,GAAY5X,EAAQsP,cAActjB,GAAIjK,IAE1CxQ,IAAG+C,KAAKsjC,EAAUE,eAAgB,SAASc,SAChChB,GAAUE,eAAec,KAGpCrnC,GAAG+C,KAAKsjC,EAAUC,KAAM,SAASe,SACtBhB,GAAUC,KAAKe,MAW9BC,WAAY,SAAS7sB,EAAI8sB,GACrB,MAAO9Y,GAAQ+Y,aAAa/sB,EAAI8sB,EAAavnC,GAAGiK,sBAGpD67B,kBAAmB,SAASrrB,EAAI8sB,GAC5B,GAAI/I,GAA0B,MAAf+I,GAAsB,EAAKA,CAC1C,OAAO9Y,GAAQsP,cAActjB,GAAIjK,KAAK+1B,eAAe/H,IAGzDC,cAAe,SAAShkB,EAAIgtB,GACxB,GAAI/P,GAAY/hB,EAASkiB,SACrB6P,EAAW9kB,EAAQnI,GACnB1P,EAAa0jB,EAAQ7M,QAAQnH,GAC7BktB,EAAajQ,EAAY+P,EACzBG,EAAWD,EAAajQ,GAAagQ,EAAWA,EAAWC,EAAajQ,EACxEwP,EAAczY,EAAQ8Q,gBAAgB9kB,GACtC2rB,EAAe/lC,KAAK09B,cAActjB,GAAIjK,KAAK41B,aAI3C36B,EAAO26B,EAAaqB,IAAeznC,GAAG8K,UAAUC,EAAY48B,EAAYC,EAI5E,OAFAxB,GAAaqB,GAAch8B,GAGvBo8B,KAAMJ,EACNz8B,MAAO28B,EACP18B,IAAK28B,EACLE,MAAOZ,EACPz7B,KAAMA,EACNoQ,KAAM+rB,EAAWD,IAIzB/I,yBAA0B,SAASjT,GAC/B,OACI6L,UAAW7L,EAAUkc,KACrBE,UAAWpc,EAAU3gB,MAAQ,EAC7Bg9B,QAASrc,EAAU1gB,IACnB2sB,WAAYjM,EAAUmc,QAQ9BG,mBAAoB,SAASxtB,GACzB,GAAIytB,GAAgB,MAChBnmC,EAAO2e,EAAQjG,GACfoB,EAAO+G,EAAQnI,GACfid,EAAY/hB,EAASkiB,SACrBzZ,EAAWgE,EAAY3H,EAE3B,OAAOza,IAAG8H,OAAO,2BAA4B2iB,EAAWyd,EAAenmC,EAAM8Z,EAAM6b,EAAWtZ,IAGlG+pB,aAAc,SAAS1tB,GACnB,MAAOgU,GAAQ7M,QAAQnH,GAAIla,MAG/B6nC,oBAAqB,SAAS3tB,GAC1B,MAAOgU,GAAQsP,cAActjB,GAAI9E,UAOrC4pB,gBAAiB,SAAS9kB,GACtB,GAAI9E,EAAU,CACV,GAAI+xB,GAAW9kB,EAAQnI,GACnBid,EAAY/hB,EAASkiB,QAEzB,OAAOl2B,MAAK0mC,KAAKX,EAAWhQ,KAIpCgP,QAAS,SAASjsB,EAAI8sB,GAClB,GAAI/I,GAA0B,MAAf+I,GAAsB,EAAKA,CAC1C,OAAO9Y,GAAQsP,cAActjB,GAAIjK,KAAK81B,KAAK9H,IAG/C+B,SAAU,SAAS9lB,GACf,MAAOgU,GAAQsP,cAActjB,GAAIjK,KAAK81B,MAK1CM,sBAAuB,SAASn3B,GACxBu2B,GACAhmC,GAAG+C,KAAKqR,aAAc,SAASvE,EAAKvI,GAChC,GAAwD,IAApDuI,EAAIlK,QAAQ3F,GAAG8H,OAAO,aAAc2iB,IAAmB,CACvD,GAAIlK,GAAa5O,KAAKC,MAAMtK,EAC5BmI,GAASI,EAAK0Q,OAM9B2lB,eAAgB,SAASzrB,GACrBgU,EAAQsP,cAActjB,GAAIjK,MACtB+1B,kBACA5H,iBACA2H,QACAF,kBAIRkC,kBAAmB,SAAS7tB,GACxBgU,EAAQsP,cAActjB,GAAIosB,cAAe,GAK7C5H,+BAAgC,SAASxkB,GACrC,GAAI8tB,EAEJ,UAAIvC,GAAiBvX,EAAQiV,YAAYjpB,KACrC8tB,EAAiB9Z,EAAQwZ,mBAAmBxtB,GAExC8tB,GAAkBn0B,aAAatE,QAAQy4B,OACvCn0B,aAAao0B,WAAWD,IACjB,IASnBpC,uBAAwB,SAAS1rB,GAC7B,GACI8tB,GAAgBE,EADhB/wB,EAAQ+W,EAAQsP,cAActjB,EAK9BurB,IAA+B7hC,SAAduT,EAAM7H,MACvB04B,EAAiB9Z,EAAQwZ,mBAAmBxtB,GAC5CguB,EAAgBr0B,aAAatE,QAAQy4B,GAGjCE,IACAA,EAAgB92B,KAAKC,MAAM62B,GAIvBpc,EAAcoc,EAAc7tB,MAC5B6T,EAAQ6Z,kBAAkB7tB,IAG1BtU,EAAInG,GAAG8H,OAAO,0DAA2D2S,EAAIiG,EAAQjG,KAErFyR,EAAczR,EAAIguB,EAAc7tB,MAEhClD,EAAM7H,IAAM44B,EAAc54B,IAC1B6H,EAAM/B,SAAW8yB,EAAc9yB,SAC/B+B,EAAMoT,OAAS2d,EAAc3d,OAC7BpT,EAAMgnB,kBAAmB,EAEzBjQ,EAAQmS,0BAA0BnmB,OAOlDylB,0BAA2B,SAASzlB,GAChC,GACI8tB,GAAgBE,EADhB/wB,EAAQ+W,EAAQsP,cAActjB,EAIlC,IAAIurB,GAAiBvX,EAAQiV,YAAYjpB,GAAK,CAC1C8tB,EAAiB9Z,EAAQwZ,mBAAmBxtB,GAE5CguB,GACI1mC,KAAM2e,EAAQjG,GACdoB,KAAM+G,EAAQnI,GACdG,KAAMuI,EAAQ1I,GACd5K,IAAK6H,EAAM7H,IACX8F,SAAU+B,EAAM/B,SAChBmV,OAAQpT,EAAMoT,OACd4d,YAAapL,KAAK8F,MAGtB,KACIhvB,aAAaC,QAAQk0B,EAAgB52B,KAAK2pB,UAAUmN,IAExD,MAAOp+B,GACHlE,EAAInG,GAAG8H,OAAO,0DAA2D2S,EAAIpQ,EAAM1D,YAAa,WAK5GgiC,yBAA0B,SAASluB,EAAI+jB,EAAU9G,GAC7C,GAAI9jB,GAAM6a,EAAQiY,QAAQjsB,EAAI+jB,GAC1Bz8B,EAAO2e,EAAQjG,GACfmuB,GACI3G,OAAQ,SAASnX,EAAQC,GACrB,GAAI2c,GAAW9kB,EAAQnI,EAEnBqQ,KAAWC,EACXF,EAAWpQ,EAAI1Y,EAAM2lC,EAAUA,GAG/B7c,EAAWpQ,EAAI1Y,EAAO+oB,GAAU4c,EAAWA,EAAW,EAAI5c,EAAS4c,IAI3EnJ,QAAS,SAASzT,EAAQC,GACtB,GAAI4T,GAAgBlQ,EAAQsP,cAActjB,GAAIjK,KAAKmuB,cAC/CkK,EAAiCpa,EAAQsP,cAActjB,GAAIqQ,OAC3Dge,EAAmBhe,EACnBie,EAAkBhe,EAClB4M,EAAgB/U,EAAQnI,GACxBuuB,EAAuBF,GAAoBC,EAAkBrR,GAC7DuR,EAAqBJ,CAEzBlK,GAAcH,GAAYwK,EAE1BhpC,GAAG+C,KAAK47B,EAAe,SAASH,EAAU0K,GACtCD,GAAsBC,IAG1Bre,EAAWpQ,EAAI1Y,EAAMknC,EAAoBtR,IAIrD/jB,GAAI1J,OAAOsvB,WAAa,SAAStqB,GAC7B,GAAIA,EAAEusB,iBAAkB,CAEpB,GAAIl7B,GAAoB,MAAbm3B,EAAoB,SAAW,SAC1CkR,GAAmBroC,GAAM2O,EAAE4b,OAAQ5b,EAAE6b,UAcjDyc,aAAc,SAAS/sB,EAAI8sB,EAAa3zB,EAAKu1B,GACzC,GAAIC,GAAwB,MAAf7B,GAAsB,EAAKA,EACpClB,EAAY5X,EAAQsP,cAActjB,GAAIjK,IAW1C,OATA61B,GAAUC,KAAOD,EAAUC,SAC3BD,EAAUE,eAAiBF,EAAUE,mBAErCF,EAAUC,KAAK8C,GAAUx1B,EAErBu1B,IACA9C,EAAUE,eAAe6C,GAAUD,GAGhCv1B,GAIX8uB,8BAA+B,WAC3B,GAAI2G,GAAiB5yB,EAAOqhB,eAE5BrJ,GAAQmY,sBAAsB,SAAS/2B,EAAK0Q,GACxC,GAAI+oB,GAAiB,GAAIhM,MAAK/c,EAAWmoB,YAGzCY,GAAeC,QAAQD,EAAeE,UAAYH,GAE9CC,EAAe/L,WAAaD,KAAK8F,QACjCj9B,EAAI,2CAA6C0J,GACjDuE,aAAao0B,WAAW34B,OAWpCyzB,qBAAsB,SAAS7oB,GAC3B,GAAI/C,GAAQ+W,EAAQsP,cAActjB,EAMlC,OAJK/C,GAAM/B,UACP8Y,EAAQ+Q,mBAAmB/kB,GAGxB/C,EAAM/B,SAAS8K,YCxflCzgB,GAAG2mB,wBAA0B,SAASxO,GAClC,YAqBA,SAASsxB,KACL,MAAqC,SAAjClxB,EAAQqO,OAAOC,eAEX6iB,QAAS,aAtBrB,GAAIC,GACApxB,GACIqO,OAAQ,SACRG,cAAe,SACfK,iBACAN,eAAgB,EAChBI,cAAe,SAASzM,GAAK,UAC7B0M,eACAE,MACIqI,UAAU,EACVuI,iBAAiB,GAErB9xB,IAAK,SAAS4B,EAAK1B,KACnBihB,SAAU,SAAS7M,KACnB+M,iBAAkB,SAAS/M,EAAIgN,EAAUC,KAGjD1nB,IAAG6B,OAAO0W,EAASJ,GAYnBwxB,EAAY3pC,GAAG6B,OAAOxB,KAAM,GAAIL,IAAGg5B,eAC/B6C,aAAc,mBACdM,cAAe,OAAQ,UACvBvV,OAAQrO,EAAQqO,OAChBQ,cAAe7O,EAAQ6O,cACvBD,YAAa5O,EAAQ4O,YACrBkT,eAAgBoP,IAChB3iB,eAAgBvO,EAAQuO,eACxBI,cAAe,SAASzM,GACpB,MAAOlC,GAAQ2O,cAAc5E,IAAI7H,IAErCtU,IAAKoS,EAAQpS,IACbs0B,OAAQliB,EAAQ+O,SAChB2D,WAAY1S,EAAQiP,iBACpBH,KAAM9O,EAAQ8O,QAGlBrnB,GAAG6B,OAAOxB,MACNgzB,WAAY,SAAS5Y,EAAIG,EAAMqY,GAC3B,GAAIzI,GAAoByI,KAExB1a,GAAQpS,IAAI,sCAAwCsU,GAE7B,WAAnBlC,EAAQqO,OACR+iB,EAAUlN,cAAchiB,GACnBmiB,SAAShiB,GACTkiB,WAAWtS,GACX2Q,QAGL3Q,EAAkBjS,EAAQwO,eAAiBnM,EAC3C+uB,EAAUlN,cAAchiB,GACnBqiB,WAAWtS,GACX2Q,YCpDrB,WAMI,QAASyO,GAAkBC,GACvB,GAGIC,GAHAC,EAAKF,EAAIG,aACTC,EAAKJ,EAAIK,cACT7lC,EAASwF,SAASC,cAAc,SAGpC,OAAIigC,GAAKE,EAAK,UACV5lC,EAAO8lC,MAAQ9lC,EAAOgV,OAAS,EAC/BywB,EAAMzlC,EAAO+lC,WAAW,MACxBN,EAAIO,UAAUR,GAAME,EAAK,EAAG,GAIoB,IAAzCD,EAAIQ,aAAa,EAAG,EAAG,EAAG,GAAGtlC,KAAK,IAUjD,QAASulC,GAAqBV,EAAKE,EAAIE,GACnC,GAIIH,GAAK9kC,EAAMwlC,EAAOC,EAJlBpmC,EAASwF,SAASC,cAAc,UAChC4gC,EAAK,EACLC,EAAKV,EACLW,EAAKX,CAUT,KAPA5lC,EAAO8lC,MAAQ,EACf9lC,EAAOgV,OAAS4wB,EAChBH,EAAMzlC,EAAO+lC,WAAW,MACxBN,EAAIO,UAAUR,EAAK,EAAG,GACtB7kC,EAAO8kC,EAAIQ,aAAa,EAAG,EAAG,EAAGL,GAAIjlC,KAG9B4lC,EAAKF,GACRF,EAAQxlC,EAAgB,GAAV4lC,EAAK,GAAS,GACd,IAAVJ,EACAG,EAAKC,EAELF,EAAKE,EAETA,EAAMD,EAAKD,GAAO,CAItB,OADAD,GAASG,EAAKX,EACI,IAAVQ,EAAe,EAAIA,EAM/B,QAASI,GAAqBhB,EAAKp+B,EAAM8M,EAASuyB,GAC9C,GAAIzmC,GAASwF,SAASC,cAAc,UAChCxF,EAAOiU,EAAQjU,MAAQ,aACvBwH,EAAU,GAAI9L,IAAG+L,OASrB,OAPAg/B,GAAoBlB,EAAKp+B,EAAMpH,EAAQkU,EAASuyB,GAC3C1zB,KAAK,WACFtL,EAAQG,QACJ5H,EAAOI,UAAUH,EAAMiU,EAAQhU,SAAW,OAI/CuH,EAGX,QAASk/B,GAAoCtvB,GACzC,GAAIuvB,GAAY,MAEhB,KAAKjrC,GAAG0O,MACJ,KAAM,IAAI1O,IAAGwB,MAAM,kEAGvB,IAAIka,EAAKwvB,WAAaxvB,EAAKyvB,UAAYF,EACnC,OACIG,UAAWzpC,KAAKC,MAAMD,KAAK0pC,KAAKJ,GAAavvB,EAAKwvB,WAAaxvB,EAAKyvB,aACpEG,SAAU3pC,KAAKC,MAAMD,KAAK0pC,KAAKJ,GAAavvB,EAAKyvB,UAAYzvB,EAAKwvB,eAQ9E,QAASH,GAAoBlB,EAAKp+B,EAAMpH,EAAQkU,EAASuyB,GACrD,GAMIS,GANAxB,EAAKF,EAAIG,aACTC,EAAKJ,EAAIK,cACTC,EAAQ5xB,EAAQ4xB,MAChB9wB,EAASd,EAAQc,OACjBywB,EAAMzlC,EAAO+lC,WAAW,MACxBt+B,EAAU,GAAI9L,IAAG+L,OAKrB,OAFA+9B,GAAI0B,OAEAjzB,EAAQkzB,OACDC,GACHjgC,KAAMA,EACNpH,OAAQA,EACRgxB,MAAOwU,EACP8B,YAAa1B,EACb2B,WAAY7B,EACZ8B,YAAatzB,EAAQszB,YACrBJ,OAAQlzB,EAAQkzB,OAChBK,aAAczyB,EACd0yB,YAAa5B,KAIhBnqC,GAAGkT,kBAAkB0D,2BACtB20B,EAAqBP,GACjBG,UAAWhB,EACXe,WAAY7xB,IAGZkyB,IACAvrC,GAAGmG,IAAInG,GAAG8H,OAAO,iFACbqiC,EAAO9wB,EAAQkyB,EAAmBD,SAAUC,EAAmBH,WAC/D,QAEJjB,EAAQoB,EAAmBD,SAC3BjyB,EAASkyB,EAAmBH,YAIpCY,EAAoB3nC,EAAQ8lC,EAAO9wB,EAAQd,EAAQszB,aAI/C7rC,GAAG0O,OACF,WACOk7B,EAAkBC,KAClBE,GAAM,EACNE,GAAM,EAGV,IAOIgC,GAAQC,EAAIC,EAPZC,EAAI,KACJC,EAAYxiC,SAASC,cAAc,UACnCwiC,EAAkBxB,EAAWP,EAAqBV,EAAKE,EAAIE,GAAM,EACjEsC,EAAK5qC,KAAK0mC,KAAK+D,EAAIjC,EAAQJ,GAC3ByC,EAAK7qC,KAAK0mC,KAAK+D,EAAI/yB,EAAS4wB,EAAKqC,GACjC5B,EAAK,EACL+B,EAAK,CAMT,KAHAJ,EAAUlC,MAAQkC,EAAUhzB,OAAS+yB,EACrCH,EAASI,EAAUjC,WAAW,MAEvBM,EAAKT,GAAI,CAGZ,IAFAiC,EAAK,EACLC,EAAK,EACED,EAAKnC,GACRkC,EAAOS,UAAU,EAAG,EAAGN,EAAGA,GAC1BH,EAAO5B,UAAUR,GAAMqC,GAAKxB,GAC5BZ,EAAIO,UAAUgC,EAAW,EAAG,EAAGD,EAAGA,EAAGD,EAAIM,EAAIF,EAAIC,GACjDN,GAAME,EACND,GAAMI,CAEV7B,IAAM0B,EACNK,GAAMD,EAEV1C,EAAI6C,UACJN,EAAYJ,EAAS,QAIzBnC,EAAIO,UAAUR,EAAK,EAAG,EAAGM,EAAO9wB,GAGpChV,EAAOuoC,iBAAmBvoC,EAAOuoC,kBACjC9gC,EAAQG,UAEDH,GAGX,QAAS4/B,GAAqCmB,GAC1C,GAAIphC,GAAOohC,EAAWphC,KAClB4pB,EAAQwX,EAAWxX,MACnBsW,EAAckB,EAAWlB,YACzBC,EAAaiB,EAAWjB,WACxBC,EAAcgB,EAAWhB,YACzB//B,EAAU,GAAI9L,IAAG+L,QACjB0/B,EAASoB,EAAWpB,OACpBqB,EAAejjC,SAASC,cAAc,UACtCijC,EAAsBD,EAAa1C,WAAW,MAC9C4C,EAAeH,EAAWxoC,OAC1BynC,EAAee,EAAWf,aAC1BC,EAAcc,EAAWd,WAyB7B,OAvBAC,GAAoBc,EAAclB,EAAYD,EAAaE,GAE3DmB,EAAa3zB,OAASyyB,EACtBkB,EAAa7C,MAAQ4B,EAErBgB,EAAoB1C,UAAUhV,EAAO,EAAG,GAExCoW,GACIhgC,KAAMA,EACN4N,OAAQyyB,EACRzW,MAAOA,EACPyX,aAAcA,EACdE,aAAcA,EACd7C,MAAO4B,IAEN30B,KACG,WACI41B,EAAaJ,iBAAmBI,EAAaJ,kBAC7C9gC,EAAQG,WAEZH,EAAQK,SAGTL,EAOX,QAASkgC,GAAoB3nC,EAAQ8lC,EAAO9wB,EAAQwyB,GAChD,OAAQA,GACJ,IAAK,GACL,IAAK,GACL,IAAK,GACL,IAAK,GACDxnC,EAAO8lC,MAAQ9wB,EACfhV,EAAOgV,OAAS8wB,CAChB,MACJ,SACI9lC,EAAO8lC,MAAQA,EACf9lC,EAAOgV,OAASA,EAExB,GAAIywB,GAAMzlC,EAAO+lC,WAAW,KAC5B,QAAQyB,GACJ,IAAK,GAED/B,EAAImD,UAAU9C,EAAO,GACrBL,EAAInoB,OAAM,EAAI,EACd,MACJ,KAAK,GAEDmoB,EAAImD,UAAU9C,EAAO9wB,GACrBywB,EAAIoD,OAAOvrC,KAAKwrC,GAChB,MACJ,KAAK,GAEDrD,EAAImD,UAAU,EAAG5zB,GACjBywB,EAAInoB,MAAM,GAAG,EACb,MACJ,KAAK,GAEDmoB,EAAIoD,OAAO,GAAMvrC,KAAKwrC,IACtBrD,EAAInoB,MAAM,GAAG,EACb,MACJ,KAAK,GAEDmoB,EAAIoD,OAAO,GAAMvrC,KAAKwrC,IACtBrD,EAAImD,UAAU,GAAI5zB,EAClB,MACJ,KAAK,GAEDywB,EAAIoD,OAAO,GAAMvrC,KAAKwrC,IACtBrD,EAAImD,UAAU9C,GAAQ9wB,GACtBywB,EAAInoB,OAAM,EAAI,EACd,MACJ,KAAK,GAEDmoB,EAAIoD,QAAO,GAAOvrC,KAAKwrC,IACvBrD,EAAImD,WAAW9C,EAAO,IAUlC,QAASiD,GAAaC,EAAUC,GAC5B,GAAI70B,GAAOpY,IAEP6E,QAAOO,MAAQ4nC,YAAoB5nC,QAClC,WACG,GAAIokC,GAAM,GAAI0D,OACVC,EAAMtoC,OAAOsoC,KAAOtoC,OAAOsoC,IAAIC,gBAAkBvoC,OAAOsoC,IACpDtoC,OAAOwoC,WAAaxoC,OAAOwoC,UAAUD,gBAAkBvoC,OAAOwoC,UAAY,IAClF,KAAKF,EAAO,KAAMhsC,OAAM,uDACxBqoC,GAAI8D,IAAMH,EAAIC,gBAAgBJ,GAC9B50B,EAAKhN,KAAO4hC,EACZA,EAAWxD,KAGdwD,EAASrD,cAAiBqD,EAASnD,gBACpCmD,EAASrhC,OAAS,WACd,GAAI4hC,GAAYn1B,EAAKo1B,kBACjBD,KACAn1B,EAAKo1B,mBAAqB,KAG1BvkB,WAAW,WACP,IAAK,GAAIxY,GAAI,EAAG5D,EAAM0gC,EAAU7nC,OAAQ+K,EAAI5D,EAAK4D,IAC7C88B,EAAU98B,MAEf,KAGXu8B,EAASnhC,QAAUohC,EACnBjtC,KAAKwtC,uBAETxtC,KAAKgtC,SAAWA,EAMpBD,EAAa1mC,UAAUonC,OAAS,SAASrhC,EAAQ8L,GAC7CA,EAAUA,KAEV,IASIw1B,GATAt1B,EAAOpY,KACP2tC,EAAW3tC,KAAKgtC,SAASrD,aACzBiE,EAAY5tC,KAAKgtC,SAASnD,cAC1BC,EAAQ5xB,EAAQ4xB,MAChB9wB,EAASd,EAAQc,OACjB+c,EAAW7d,EAAQ6d,SACnBD,EAAY5d,EAAQ4d,UACpB2U,GAAYzqC,KAAKoL,MAA2B,eAAnBpL,KAAKoL,KAAKlL,KACnCiJ,EAAUiD,EAAOjD,QAAQF,aAG7B,OAAIjJ,MAAKwtC,uBACLxtC,MAAKwtC,mBAAmB3qC,KAAK,WAAauV,EAAKq1B,OAAOrhC,EAAQ8L,MAI9D4xB,IAAU9wB,EACVA,EAAU40B,EAAY9D,EAAQ6D,GAAa,EACpC30B,IAAW8wB,EAClBA,EAAS6D,EAAW30B,EAAS40B,GAAc,GAE3C9D,EAAQ6D,EACR30B,EAAS40B,GAET7X,GAAY+T,EAAQ/T,IACpB+T,EAAQ/T,EACR/c,EAAU40B,EAAY9D,EAAQ6D,GAAa,GAE3C7X,GAAa9c,EAAS8c,IACtB9c,EAAS8c,EACTgU,EAAS6D,EAAW30B,EAAS40B,GAAc,GAG/CF,GAAQ5D,MAAOA,EAAO9wB,OAAQA,GAC9BrZ,GAAG+C,KAAKwV,EAAS,SAAS21B,EAAYC,GAClCJ,EAAIG,GAAcC,IAGN,QAAZ3kC,GACC,WACG,GAAI4kC,GAAe3hC,EAAOkhC,GAC1B9C,GAAqBpyB,EAAK40B,SAAU50B,EAAKhN,KAAMsiC,EAAKjD,GAC/C1zB,KAAK,SAAS1S,GACX+H,EAAOkhC,IAAMjpC,EACb0pC,IAAiB3hC,EAAOkhC,KAAOlhC,EAAOT,cAG/B,WAAZxC,GACPuhC,EAAoB1qC,KAAKgtC,SAAUhtC,KAAKoL,KAAMgB,EAAQshC,EAAKjD,QAElC,kBAAlBzqC,MAAKguC,UACZhuC,KAAKguC,SAAS5hC,MAItBzM,GAAGotC,aAAeA,KCtYtBptC,GAAG24B,eAAiB,SAASxyB,GACzB,YAEA,SAASmoC,GAAMC,GACX,MAAoC,QAA7BA,EAAG/kC,QAAQF,cAGtB,QAASklC,GAASD,GACd,MAAoC,WAA7BA,EAAG/kC,QAAQF,cAGtB,QAASmlC,KACL,MAAmCtqC,WAA5B,GAAIopC,QAAQmB,YAGvB,QAASC,KACL,GAAItqC,GAASwF,SAASC,cAAc,SAEpC,OAAOzF,GAAO+lC,YAAc/lC,EAAO+lC,WAAW,MAMlD,QAASwE,GAAwBC,GAE7B,GAAIC,GAAeD,EAAanpC,MAAM,KAClC3D,EAAO+sC,EAAaA,EAAa/oC,OAAS,GAAGL,MAAM,KAAK,GACxDspB,EAAYhvB,GAAG8R,aAAa/P,EAIhC,QAFAitB,EAAYA,GAAaA,EAAU1lB,eAG/B,IAAK,OACL,IAAK,MACD,MAAO,YACX,KAAK,MACD,MAAO,WACX,KAAK,MACD,MAAO,WACX,KAAK,MACD,MAAO,WACX,KAAK,OACL,IAAK,MACD,MAAO,cASnB,QAASylC,GAAcxU,GACnB,GACIyU,GAAgBC,EAAgBC,EADhCC,EAAetlC,SAASC,cAAc,IAS1C,OANAqlC,GAAaC,KAAO7U,EAEpByU,EAAiBG,EAAaE,SAC9BH,EAAaC,EAAaG,KAC1BL,EAAiBE,EAAaI,SAE1BP,EAAe1lC,gBAAkBpE,OAAOsqC,SAASH,SAAS/lC,gBAI1D2lC,EAAe3lC,gBAAkBpE,OAAOsqC,SAASD,SAASjmC,eAK1D4lC,IAAehqC,OAAOsqC,SAASF,OAAStvC,GAAGyN,MAOnD,QAASgiC,GAAyB5F,EAAK/9B,GACnC+9B,EAAI79B,OAAS,WACT69B,EAAI79B,OAAS,KACb69B,EAAI39B,QAAU,KACdJ,EAAQG,QAAQ49B,IAGpBA,EAAI39B,QAAU,WACV29B,EAAI79B,OAAS,KACb69B,EAAI39B,QAAU,KACd/F,EAAI,6BAA8B,SAClC2F,EAAQK,QAAQ09B,EAAK,+BAI7B,QAAS6F,GAAgCrrC,EAAQyH,GAO7CzH,EAAOuoC,gBAAkB,WACrB9gC,EAAQG,QAAQ5H,IAQxB,QAASsrC,GAAkCxuB,EAAarV,GACpD,GAAI8jC,GAAatB,EAAMntB,IAAgBqtB,EAASrtB,EAahD,OAXImtB,GAAMntB,GACNsuB,EAAyBtuB,EAAarV,GAEjC0iC,EAASrtB,GACduuB,EAAgCvuB,EAAarV,IAG7CA,EAAQK,QAAQgV,GAChBhb,EAAInG,GAAG8H,OAAO,iDAAkDqZ,EAAY3X,SAAU,UAGnFomC,EAKX,QAASC,GAAK9kC,EAAY8W,EAAWtJ,GACjC,GAAIu3B,GAAc,GAAI9vC,IAAG+L,QACrBqc,EAAa,GAAIpoB,IAAG+vC,SAAShlC,EAAY5E,GACzCib,EAAU7I,EAAQ6I,QAElBiX,EAA2B,MAAlB9f,EAAQ8f,QAAwB9f,EAAQ8f,OACjD2X,EAAsB,WAClBnuB,EAAU3V,QAAU,KACpB2V,EAAU7V,OAAS,KACnB7F,EAAI,mDAAoD,SACxD2pC,EAAY3jC,QAAQ0V,EAAW,gCAiDvC,OA9CAuG,GAAW6nB,gBAAgB74B,KACvB,SAAS9S,GAGL,GAAI4rC,IACIt+B,MAAO,WACH,OAAO,GAAI5R,IAAG+L,SAAUE,YAGhCkkC,EAAO9X,EAAS,GAAIr4B,IAAGowC,KAAKrlC,EAAY5E,GAAO+pC,EAC/CG,EAAQ,GAAIrwC,IAAGotC,aAAariC,EAAYilC,EAExCL,GAAkC9tB,EAAWiuB,IAC7CK,EAAKv+B,QAAQwF,KACT,SAAS+4B,GACL,GAAItE,GAAcsE,GAAQA,EAAKG,WAE/BD,GAAMvC,OAAOjsB,GACTuU,SAAUhV,EACV+U,UAAW/U,EACXyqB,YAAaA,EACbvnC,KAAMA,EACNmnC,OAAQlzB,EAAQ+I,wBAIxB,SAASivB,GACLpqC,EAAInG,GAAG8H,OAAO,iEAAkEyoC,IAEhFF,EAAMvC,OAAOjsB,GACTuU,SAAUhV,EACV+U,UAAW/U,EACX9c,KAAMA,EACNmnC,OAAQlzB,EAAQ+I,0BAOpC,WACInb,EAAI,mBACJ2pC,EAAY3jC,QAAQ0V,EAAW,qBAIhCiuB,EAGX,QAASU,GAAyBjW,EAAKkW,EAAaZ,EAAMzuB,EAASE,GAC/D,GAAIovB,GAAU,GAAInD,OACdoD,EAAgB,GAAI3wC,IAAG+L,OAE3B4jC,GAAkCe,EAASC,GAEvC5B,EAAcxU,KACdmW,EAAQhC,YAAc,aAG1BgC,EAAQ/C,IAAMpT,EAEdoW,EAAcv5B,KACV,WACIu4B,EAAkCc,EAAaZ,EAE/C,IAAIQ,GAAQ,GAAIrwC,IAAGotC,aAAasD,EAChCL,GAAMvC,OAAO2C,GACTra,SAAUhV,EACV+U,UAAW/U,EACX9c,KAAMsqC,EAAwBrU,GAC9BkR,OAAQnqB,KAIhBuuB,EAAK1jC,SAIb,QAASykC,GAA+BrW,EAAKsP,EAAKgG,EAAMzuB,GACpDuuB,EAAkC9F,EAAKgG,GAKvC7vC,GAAG6pC,GAAKvoC,KACJ80B,SAAUhV,EAAU,KACpB+U,UAAW/U,EAAU,OAGzByoB,EAAI8D,IAAMpT,EAWd,QAASsW,GAAYtW,EAAK1Y,EAAWtJ,GACjC,GAAIs3B,GAAO,GAAI7vC,IAAG+L,QACd4V,EAAQpJ,EAAQoJ,MAChBP,EAAUO,EAAQpJ,EAAQ6I,QAAU,IA8BxC,OA3BIO,IAAS2sB,EAAMzsB,GAGX8sB,IAIII,EAAcxU,KAASkU,IACvBmC,EAA+BrW,EAAK1Y,EAAWguB,EAAMzuB,GAGrDovB,EAAyBjW,EAAK1Y,EAAWguB,EAAMzuB,GAInDwvB,EAA+BrW,EAAK1Y,EAAWguB,EAAMzuB,GAIpDotB,EAAS3sB,GACd2uB,EAAyBjW,EAAK1Y,EAAWguB,EAAMzuB,GAG1CuuB,EAAkC9tB,EAAWguB,KAClDhuB,EAAU8rB,IAAMpT,GAGbsV,EAGX7vC,GAAG6B,OAAOxB,MAWNyhB,SAAU,SAASgvB,EAAejvB,EAAWtJ,GACzC,MAAIvY,IAAGwH,SAASspC,IACZ3qC,EAAI,4DACG0qC,EAAYC,EAAejvB,EAAWtJ,SAG7CpS,EAAI,iDACG0pC,EAAKiB,EAAejvB,EAAWtJ,WAMlDlY,KAAK0wC,YACL1wC,KAAK0wC,SAASzC,MAAQA,EACtBjuC,KAAK0wC,SAASvC,SAAWA,EACzBnuC,KAAK0wC,SAAShC,cAAgBA,EAC9B1uC,KAAK0wC,SAASnC,wBAA0BA,GChT5C5uC,GAAGowC,KAAO,SAASrlC,EAAY5E,GAC3B,YAYA,SAAS6qC,GAAkBC,GAIvB,IAHA,GAAItuC,GAAS,EACTuuC,EAAM,EAEHD,EAAIlrC,OAAS,GAChBpD,GAAUk/B,SAASoP,EAAI1oC,UAAU,EAAG,GAAI,IAAM5G,KAAKuvC,IAAI,EAAGA,GAC1DD,EAAMA,EAAI1oC,UAAU,EAAG0oC,EAAIlrC,QAC3BmrC,GAAO,CAGX,OAAOvuC,GAKX,QAASwuC,GAAWC,EAAQtlC,GACxB,GAAIulC,GAAYD,EACZE,EAAaxlC,CAwBjB,OAvBkB3H,UAAdktC,IACAA,EAAY,EACZC,EAAa,GAAItxC,IAAG+L,SAGxB/L,GAAGwL,cAAcT,EAAYsmC,EAAW,GAAGj6B,KAAK,SAAS65B,GACrD,GACIM,GADA99B,EAAQ,cAAcxP,KAAKgtC,EAG3Bx9B,GACiB,MAAbA,EAAM,IACN89B,EAAgB1P,SAASoP,EAAI/oC,MAAM,EAAG,GAAI,IAC1CipC,EAAWE,EAAYE,EAAgB,EAAGD,IAG1CA,EAAWrlC,QAAQolC,GAIvBC,EAAWnlC,QAAQ,iCAIpBmlC,EAIX,QAASE,KACL,GAAI1lC,GAAU,GAAI9L,IAAG+L,OAgBrB,OAdA/L,IAAGwL,cAAcT,EAAY,EAAG,GAAGqM,KAAK,SAAS65B,GACjB,IAAxBA,EAAItrC,QAAQ,QACZmG,EAAQK,QAAQ,qBAGhBglC,IAAa/5B,KAAK,SAASg6B,GACvBtlC,EAAQG,QAAQmlC,IAEpB,SAAS/mC,GACLyB,EAAQK,QAAQ9B,OAKrByB,EAIX,QAAS2lC,GAAeC,GACpB,GAAI5lC,GAAU,GAAI9L,IAAG+L,OAMrB,OAJA/L,IAAGwL,cAAcT,EAAY2mC,EAAY,GAAI,GAAGt6B,KAAK,SAAS65B,GAC1DnlC,EAAQG,QAAgB,SAARglC,KAGbnlC,EAIX,QAAS6lC,GAAiBD,EAAWE,GACjC,GAAI9lC,GAAU,GAAI9L,IAAG+L,OAWrB,OATA/L,IAAGwL,cAAcT,EAAY2mC,EAAY,GAAI,GAAGt6B,KAAK,SAAS65B,GAC1D,MAAIW,GACO9lC,EAAQG,QAAQ+kC,EAAkBC,QAGzCnlC,GAAQG,QAAQ41B,SAASoP,EAAK,OAI/BnlC,EAIX,QAAS+lC,GAAOH,EAAWI,GACvB,GAAIV,GAASM,EAAY,GACrBrmC,EAAqB,GAAbymC,CAEZ,OAAO9xC,IAAGwL,cAAcT,EAAYqmC,EAAQ/lC,GAIhD,QAAS0mC,GAAcC,GAInB,IAHA,GAAIx3B,MACA42B,EAAS,EAENA,EAAS,IAAMY,EAAOjsC,QACzByU,EAAQtX,KAAK8uC,EAAO9pC,MAAMkpC,EAAQA,EAAS,KAC3CA,GAAU,EAGd,OAAO52B,GAIX,QAASy3B,GAAaL,EAAcE,GAChC,GAAII,GAAiB,GACjBC,EAAanyC,GAAG6B,UAAWuwC,GAC3BC,IAsBJ,OApBAryC,IAAG+C,KAAK+uC,EAAY,SAAS9uC,EAAK+1B,GAC9B,GAGIuZ,GAAW9oC,EAAS+oC,EAHpBC,EAAQzZ,EAAM7wB,MAAM,EAAG,GACvBuS,EAAKm3B,EAAeZ,EAAkBwB,GAAS3Q,SAAS2Q,EAAO,IAC/DC,EAAgBN,EAAWxsC,QAAQ8U,EAYvC,IATIg4B,GAAiB,IACjBjpC,EAAUkpC,EAASj4B,GAAI1Y,KACvBwwC,EAAeG,EAASj4B,GAAIpP,MAC5BinC,EAAYvZ,EAAM7wB,MAAMgqC,EAAgBA,EAAiC,EAAfK,GAC1DF,EAAK7oC,GAAWooC,EAAeZ,EAAkBsB,GAAazQ,SAASyQ,EAAW,IAElFH,EAAW51B,OAAOk2B,EAAe,IAGX,IAAtBN,EAAWpsC,OACX,OAAO,IAIRssC,EApJX,GAAID,IAAW,KACXM,GACIC,KACI5wC,KAAM,cACNsJ,MAAO,GAmJnBrL,IAAG6B,OAAOxB,MAONuR,MAAO,WACH,GAAIghC,GAAS,GAAI5yC,IAAG+L,QAChB8mC,EAAiB,SAASzsC,GACtBD,EAAInG,GAAG8H,OAAO,kCAAmC1B,IACjDwsC,EAAOzmC,QAAQ/F,GA0BvB,OAvBAorC,KAAgBp6B,KAAK,SAAS07B,GAC1B3sC,EAAInG,GAAG8H,OAAO,mDAAwE3D,SAApB4G,EAAWhJ,KAAqB,OAASgJ,EAAWhJ,OAEtH0vC,EAAeqB,GAAY17B,KAAK,SAASw6B,GAErCzrC,EAAInG,GAAG8H,OAAO,+BAAgC8pC,EAAe,SAAW,QAExED,EAAiBmB,EAAYlB,GAAcx6B,KAAK,SAAS27B,GAErD5sC,EAAInG,GAAG8H,OAAO,kCAAmCirC,IAEjDlB,EAAOiB,EAAYC,GAAe37B,KAAK,SAAS46B,GAC5C,GAAIF,GAAaC,EAAcC,GAC3BgB,EAAYf,EAAaL,EAAcE,EAE3C3rC,GAAI,sCAEJysC,EAAO3mC,QAAQ+mC,IAChBH,IACJA,IACJA,IACJA,GAEID,KAKfvyC,KAAK0wC,YACL1wC,KAAK0wC,SAASC,kBAAoBA,GC7MtChxC,GAAG+vC,SAAW,SAAShlC,EAAY5E,GAC/B,YAEA,SAAS8sC,GAAeC,EAAYC,GAChC,GAAIC,IAAe,EACfC,KAAuBjjC,OAAO8iC,EASlC,OAPAlzC,IAAG+C,KAAKswC,EAAmB,SAASrwC,EAAKswC,GACrC,GAAwD,IAApDH,EAAkBxtC,QAAQ2tC,GAE1B,MADAF,IAAe,GACR,IAIRA,EAGXpzC,GAAG6B,OAAOxB,MASN4vC,cAAe,WACX,GAAIx3B,GAAOpY,KACP+nB,EAAa,GAAIpoB,IAAG+L,QACpBwnC,GAAc,EACdxxC,EAA2BoC,SAApB4G,EAAWhJ,KAAqB,OAASgJ,EAAWhJ,IAsC/D,OApCAoE,GAAInG,GAAG8H,OAAO,gEAAiE/F,IAE/EoE,EAAI,oDAEA9F,KAAKmzC,qBACLrtC,EAAI,sDAEJnG,GAAGwL,cAAcT,EAAY,EAAG,GAAGqM,KAAK,SAAS65B,GAC7CjxC,GAAG+C,KAAK0V,EAAKg7B,uBAAwB,SAASnvC,EAAM+G,GAChD,GAAI4nC,EAAe5nC,EAAO4lC,GAQtB,OALa,eAAT3sC,GAAyBtE,GAAGkT,kBAAkByD,gBAC9C48B,GAAc,EACdnrB,EAAWnc,QAAQ3H,KAGhB,IAIf6B,EAAInG,GAAG8H,OAAO,iDAAkD/F,EAAMwxC,EAAc,GAAK,QAEpFA,GACDnrB,EAAWjc,WAGnB,WACIhG,EAAI,+BAAiCpE,EAAO,gDAC5CqmB,EAAWjc,aAIfic,EAAWjc,UAGRic,GAWXorB,kBAAmB,WACf,GAAIE,GAAW3oC,EAAWxK,KAEtBozC,EAAoB3zC,GAAG2F,QAAQc,OAAO65B,KAAKjgC,KAAKozC,wBAAyBC,IAAa,EACtFH,GAAc,EACdxxC,EAA2BoC,SAApB4G,EAAWhJ,KAAqB,OAASgJ,EAAWhJ,IAa/D,OAXI4xC,KAEIJ,EADa,eAAbG,GACc1zC,GAAGkT,kBAAkByD,eAO1C48B,GAAeptC,EAAIpE,EAAO,gEAEpBwxC,MAKnBvzC,GAAG+vC,SAASrpC,UAAU+sC,wBAClBG,aAAc,SACdC,YAAa,SACbC,YAAa,SACbC,YAAa,OACbC,cAAe,WAAY,aA3G/Bh0C,GAAG+vC,SAAW,SAAShlC,EAAY5E,GAC/B,YAEA,SAAS8sC,GAAeC,EAAYC,GAChC,GAAIC,IAAe,EACfC,KAAuBjjC,OAAO8iC,EASlC,OAPAlzC,IAAG+C,KAAKswC,EAAmB,SAASrwC,EAAKswC,GACrC,GAAwD,IAApDH,EAAkBxtC,QAAQ2tC,GAE1B,MADAF,IAAe,GACR,IAIRA,EAGXpzC,GAAG6B,OAAOxB,MASN4vC,cAAe,WACX,GAAIx3B,GAAOpY,KACP+nB,EAAa,GAAIpoB,IAAG+L,QACpBwnC,GAAc,EACdxxC,EAA2BoC,SAApB4G,EAAWhJ,KAAqB,OAASgJ,EAAWhJ,IAsC/D,OApCAoE,GAAInG,GAAG8H,OAAO,gEAAiE/F,IAE/EoE,EAAI,oDAEA9F,KAAKmzC,qBACLrtC,EAAI,sDAEJnG,GAAGwL,cAAcT,EAAY,EAAG,GAAGqM,KAAK,SAAS65B,GAC7CjxC,GAAG+C,KAAK0V,EAAKg7B,uBAAwB,SAASnvC,EAAM+G,GAChD,GAAI4nC,EAAe5nC,EAAO4lC,GAQtB,OALa,eAAT3sC,GAAyBtE,GAAGkT,kBAAkByD,gBAC9C48B,GAAc,EACdnrB,EAAWnc,QAAQ3H,KAGhB,IAIf6B,EAAInG,GAAG8H,OAAO,iDAAkD/F,EAAMwxC,EAAc,GAAK,QAEpFA,GACDnrB,EAAWjc,WAGnB,WACIhG,EAAI,+BAAiCpE,EAAO,gDAC5CqmB,EAAWjc,aAIfic,EAAWjc,UAGRic,GAWXorB,kBAAmB,WACf,GAAIE,GAAW3oC,EAAWxK,KAEtBozC,EAAoB3zC,GAAG2F,QAAQc,OAAO65B,KAAKjgC,KAAKozC,wBAAyBC,IAAa,EACtFH,GAAc,EACdxxC,EAA2BoC,SAApB4G,EAAWhJ,KAAqB,OAASgJ,EAAWhJ,IAa/D,OAXI4xC,KAEIJ,EADa,eAAbG,GACc1zC,GAAGkT,kBAAkByD,eAO1C48B,GAAeptC,EAAIpE,EAAO,gEAEpBwxC,MAKnBvzC,GAAG+vC,SAASrpC,UAAU+sC,wBAClBG,aAAc,SACdC,YAAa,SACbC,YAAa,SACbC,YAAa,OACbC,cAAe,WAAY,aCpG/Bh0C,GAAGm1B,gBAAkB,SAAS1pB,EAAMtF,GAChC,YAMA,SAAS8tC,GAAiBC,GACtB,GAAIC,IAAa,CASjB,OAPAn0C,IAAG+C,KAAKmxC,EAAQ,SAASE,EAAOrtC,GAC5B,GAAIA,EAAQ,EAER,MADAotC,IAAa,GACN,IAIRA,EAQX,QAASE,KACL,GAAIC,GAAoB,GAAIt0C,IAAG+L,OA6B/B,OA3BA,IAAI/L,IAAG+vC,SAAStkC,EAAMtF,GAAK8pC,gBAAgB74B,KAAK,WAC5C,GAAIie,GAAQ,GAAIkY,OACZhT,EAAMr1B,OAAOsoC,KAAOtoC,OAAOsoC,IAAIC,gBAAkBvoC,OAAOsoC,IAClDtoC,OAAOwoC,WAAaxoC,OAAOwoC,UAAUD,gBAAkBvoC,OAAOwoC,UAC9D,IAENnT,IACAlF,EAAMnpB,QAAU,WACZ/F,EAAI,4DAA6D,SACjEmuC,EAAkBnoC,WAGtBkpB,EAAMrpB,OAAS,WACXsoC,EAAkBroC,SACdk+B,MAAO9pC,KAAK8pC,MACZ9wB,OAAQhZ,KAAKgZ,UAIrBgc,EAAMsY,IAAMpT,EAAIkT,gBAAgBhiC,KAGhCtF,EAAI,+DAAgE,SACpEmuC,EAAkBnoC,YAEvBmoC,EAAkBnoC,SAEdmoC,EASX,QAASC,GAAgBL,EAAQM,GAC7B,GAAIC,EA0BJ,OAxBAz0C,IAAG+C,KAAKmxC,EAAQ,SAASQ,EAAWC,GAChC,GAAIA,EAAa,EAAG,CAChB,GAAIC,GAAe,0BAA0B3wC,KAAKywC,GAC9CG,EAAoBD,EAAa,GAAG7kC,OAAO,GAAGzG,cAAgBsrC,EAAa,GAAG1sC,MAAM,GACpF4sC,EAAcN,EAAWK,EAG7B,QAAQD,EAAa,IACjB,IAAK,MACD,GAAIE,EAAcH,EAEd,MADAF,GAAeC,GACR,CAEX,MACJ,KAAK,MACD,GAAII,EAAcH,EAEd,MADAF,GAAeC,GACR,MAOpBD,EAWXp0C,KAAK+0B,SAAW,SAAS8e,GACrB,GAAIa,GAAmB,GAAI/0C,IAAG+L,OAoB9B,OAlBA5F,GAAI,iCAEA8tC,EAAiBC,GACjBG,IAAiBj9B,KAAK,SAASo9B,GAC3B,GAAIC,GAAeF,EAAgBL,EAAQM,EAEvCC,GACAM,EAAiB5oC,QAAQsoC,GAGzBM,EAAiB9oC,WAEtB8oC,EAAiB9oC,SAGpB8oC,EAAiB9oC,UAGd8oC,IC7Hf/0C,GAAGq0B,QAAU,SAAS3Y,GAClB,YAaA,SAASs5B,GAAoB5jB,GACzB,QAAIpxB,GAAG8G,QAAQsqB,QAIf7Y,GAAQpS,IAAI,oCAAqC,SAGrD,QAAS8uC,GAAgBC,EAAWjpC,EAASwb,EAAU3b,GACnD,GAAIqpC,IAAmB,CAEvBlpC,GAAUA,GAAW+oC,EAAoBE,GAErCjpC,GACAjM,GAAG+C,KAAKmyC,EAAW,SAASlyC,EAAK46B,GAE7B,GAAqB,MAAjBA,EAAShjB,KACTu6B,GAAmB,EACnB58B,EAAQpS,IAAInG,GAAG8H,OAAO,oEAAqE9E,GAAM,aAEhG,IAAqB,MAAjB46B,EAAS77B,KACdozC,GAAmB,EACnB58B,EAAQpS,IAAInG,GAAG8H,OAAO,oEAAqE9E,GAAM,aAGjG,KAEI,MADAuV,GAAQgc,cAAcqJ,IACf,EAEX,MAAOwX,GACHD,GAAmB,EACnB58B,EAAQpS,IAAIivC,EAAIhvC,QAAS,SAIjC,OAAO,IAIf0F,EAAQG,IAAYkpC,EAAmB,UAAY,WAAWD,EAAWztB,GAlD7E,GAAIlP,IACA6F,SAAU,KACVD,UACA+I,iBACAG,QACAkN,cAAe,SAASxO,KACxB5f,IAAK,SAASC,EAASC,KAG3BrG,IAAG6B,OAAO0W,EAASmD,GAAM,GA8CzBrb,KAAKm0B,QAAU,WAEX,GAAI6gB,GAAgB,GAAIr1C,IAAG+L,QACvBupC,EAA0B,SAASlkB,EAAUnlB,EAASwb,GAClDwtB,EAAgB7jB,EAAUnlB,EAASwb,EAAU4tB,IAEjDE,EAAmBv1C,GAAG6B,UAAW0W,GACjCoxB,EAAY,GAAI3pC,IAAGw1C,qBACfx1C,GAAG6B,OAAO0zC,GAAmBtqB,WAAYqqB,IAKjD,OAFA3L,GAAU8L,cAEHJ,ICrEfr1C,GAAGw1C,qBAAuB,SAAS95B,GAC/B,YAiBA,SAASuP,GAAWxQ,EAAIgN,EAAUC,GAC9B,GAAI0J,GAAW,IAGf,IAA6B,MAAzB3J,EAASiuB,aACT,IACItkB,EAAWpxB,GAAGyR,UAAUgW,EAASiuB,cAErC,MAAON,GACH78B,EAAQpS,IAAI,qCAAuCivC,EAAIhvC,QAAS,SAChEshB,GAAU,EAIlBnP,EAAQ0S,WAAWmG,GAAW1J,EAASD,GA7B3C,GAAIkiB,GACApxB,GACI6F,SAAU,KACV8I,iBACA/I,UACAkJ,MACIqI,UAAU,EACVuI,iBAAiB,GAErBhN,WAAY,SAASmG,EAAUnlB,EAASwb,KACxCthB,IAAK,SAAS4B,EAAK1B,KAG3BrG,IAAG6B,OAAO0W,EAASmD,GAmBnBiuB,EAAY3pC,GAAG6B,OAAOxB,KAAM,GAAIL,IAAGg5B,eAC/B6C,aAAc,mBACdM,cAAe,OACfvV,OAAQ,MACRQ,eACI9E,IAAK,WACD,MAAO/J,GAAQ6F,WAGvB8I,cAAe3O,EAAQ2O,cACvB/gB,IAAKoS,EAAQpS,IACb8kB,WAAYA,EACZ5D,KAAM9O,EAAQ8O,QAGlBrnB,GAAG6B,OAAOxB,MACNo1C,YAAa,WACT,GAAIt3B,GAASne,GAAG6B,UAAW0W,EAAQ4F,OAEnC5F,GAAQpS,IAAI,0BAEZwjC,EAAUlN,cAAc,kBACnBK,WAAW3e,GACXif,kBACAjC,WCzDjBn7B,GAAGukB,OAAS,SAAS7I,EAAMvV,GACvB,YAEA,IACImb,GAAuB5F,EAAKyc,cAC5Bwd,EAAkBj6B,EAAK0c,aACvBC,EAAS3c,EAAK2c,OACdC,EAAc5c,EAAK4c,YACnBC,EAAiB7c,EAAK6c,eAAiB,IACvCqd,EAAoBl6B,EAAK8c,YACzBC,EAAc/c,EAAK+c,YACnBC,EAAQr4B,KAAKw1C,gBAAgBn6B,EAAKgd,MAGtC14B,IAAG6B,OAAOxB,MAGNogB,QAASzgB,GAAGkT,kBAAkBwD,SAAWgiB,EAAM3yB,OAAS,EAExD+vC,eAAgB,SAASC,EAAkBC,EAAkBC,GACzD,GAAIx9B,GAAOpY,KACP61C,KACAC,EAAeF,EAAuBxqC,KAAOwqC,EAAuBxqC,KAAOwqC,EAC3E7tB,EAAa,GAAIpoB,IAAG+vC,SAASoG,EAAchwC,EAiD/C,OA9CIiiB,GAAWorB,qBAEXxzC,GAAG+C,KAAK21B,EAAO,SAAS11B,EAAKozC,GACzB,GAAIC,GAAa59B,EAAK69B,sBAClBhe,YAAaA,EACbie,cAAeH,EAAW71C,KAC1Bi2C,QAASL,EAAa51C,MAG1B21C,GAAQhzC,MACJ0X,KAAM5a,GAAGoN,cACTrL,KAAM0W,EAAKg+B,SAAST,GAChBj0C,KAAMq0C,EAAWr0C,KACjBxB,KAAM81C,EACNG,QAASL,EAAa51C,OAE1BkL,KAAM,GAAIzL,IAAG8X,UAAUq+B,EACvBn2C,GAAGgQ,KAAKyI,EAAKi+B,qBAAsBj+B,GAC/B6I,qBAAsBA,EACtBF,QAASg1B,EAAWh1B,QACpBiX,OAAQA,EACR93B,KAAM81C,EACN9xC,QAASg0B,EACToe,WAAYf,EACZnd,YAAaA,EACbtyB,IAAKA,SAKjB+vC,EAAQhzC,MACJ0X,KAAMm7B,EACNh0C,KAAMi0C,EACNn6B,KAAMs6B,EAAat6B,KACnBpQ,KAAMkqC,EAAkBQ,EAAe,QAI3CD,EAAQhzC,MACJ0X,KAAMm7B,EACNh0C,KAAMi0C,EACNn6B,KAAMs6B,EAAat6B,KACnBpQ,KAAM0qC,IAIPD,GAGXrd,cAAe,SAASjZ,EAAM7d,EAAM6Y,EAAMiB,EAAMiT,EAAUhT,EAASiL,EAAe6vB,GAC9E,GAAIn+B,GAAOpY,KAEPw2C,GADWj3B,EAAKyG,YAAezG,EAAKnU,MAAQmU,EAAKnU,KAAK4a,eAEtDywB,EAAa,KACbloB,EAAmBgoB,EAAIhoB,iBACvBrO,EAAaq2B,EAAIr2B,WACjB4G,EAAcyvB,EAAIzvB,YAClBpL,EAAe/b,GAAGoN,aAEtBpN,IAAG+C,KAAK0V,EAAKq9B,eAAel7B,EAAM7Y,EAAM6d,GAAO,SAAS5c,EAAK+zC,GACzD,GACIt8B,GADAu8B,EAAWD,EAAOl7B,IAGlBk7B,GAAOtrC,eAAgBzL,IAAG8X,YAC1Bk/B,GAAW,GAGfv8B,EAAK8F,EAAW9E,SACZb,KAAMm8B,EAAOn8B,KACb7Y,KAAMg1C,EAAOh1C,KACb8Z,KAAMm7B,EACNl7B,QAASA,EACTC,aAAcA,IAGdg7B,EAAOtrC,eAAgBzL,IAAG8X,UAC1B++B,EAAU3zC,KAAKuX,GAGfq8B,EAAar8B,EAGbs8B,EAAOtrC,MACPmjB,EAAiBnU,EAAIs8B,EAAOtrC,MAC5BqjB,EAAS5rB,MAAMuX,GAAIA,EAAImF,KAAMm3B,EAAOtrC,QAGpC8U,EAAWpE,UAAU1B,EAAIza,GAAG+a,OAAOsC,YAOxB,OAAfy5B,IACA92C,GAAG+C,KAAK8zC,EAAW,SAAS7zC,EAAKi0C,GAC7B,GAAI94B,IACA+4B,aAAc32B,EAAWtE,UAAUxB,GAAIq8B,IAAal8B,KACpDu8B,aAAc52B,EAAWtE,UAAUxB,GAAIq8B,IAAaj7B,KAKxDsC,GAAO4I,GAAiBxG,EAAWtE,UAAUxB,GAAIw8B,IAAWr8B,KAE5D2F,EAAWxD,YAAYk6B,EAAUH,GACjC3vB,EAAY+B,YAAY+tB,EAAU94B,KAKlC04B,EAAU9wC,SACT,WACG,GAAIqxC,KACJA,GAAMrwB,GAAiBxG,EAAWtE,UAAUxB,GAAIq8B,IAAal8B,KAC7DuM,EAAY+B,YAAY4tB,EAAYM,WAQ5Dp3C,GAAG6B,OAAO7B,GAAGukB,OAAO7d,WAChB2d,WAAY,SAAS5J,EAAI6J,EAAOsyB,GAC5B,YAEA,KAAK52C,GAAGkT,kBAAkBwD,QACtB,KAAM,IAAI1W,IAAGwB,MAAM,4CAGvB,IAAI61C,GAAgB,GAAIr3C,IAAG+L,QACvB5F,EAAMywC,EAAIzwC,IACVyZ,EAAOg3B,EAAIh1B,QAAQnH,GACnB8F,EAAaq2B,EAAIr2B,WAAWtE,UAAUxB,GAAIA,IAC1C1Y,EAAOwe,GAAcA,EAAWxe,KAChC6Y,EAAO2F,GAAcA,EAAW3F,KAChC08B,GACInf,cAAe7T,EAAM6T,cACrBC,cAAc,EACdC,OAAQ/T,EAAM+T,OACdC,YAAahU,EAAM/jB,MAAQ,KAC3Bg4B,eAAgBjU,EAAM/f,QACtBqxC,kBAAmB,kBACnBld,QAAS32B,KAAM,GAAIqf,QAASkD,EAAMlD,WAEtCm2B,EAAS,GAAIv3C,IAAGukB,OAAO+yB,EAAgBnxC,EAuB3C,OArBKnG,IAAGukB,QAAWvkB,GAAGkT,kBAAkBkD,eAAkBwJ,EAOrD5f,GAAGgQ,KAAK,WAEL,GAAI+mC,GAASQ,EAAOzB,eAAel7B,EAAM7Y,EAAM6d,GAAM,EAEjDm3B,IAAUA,EAAOtrC,eAAgBzL,IAAG8X,UACpCi/B,EAAOtrC,KAAKwM,SAASb,KAAKigC,EAAcprC,QAASorC,EAAclrC,UAG/DhG,EAAIsU,EAAK,4BAA6B,SACtC48B,EAAclrC,YAEnB9L,SAjBHg3C,EAAclrC,UAEdhG,EAAI,iDAAmDsU,EAAK,uFAC6B,UAiBtF48B,GAKXf,qBAAsB,SAAS56B,GAC3B,YAEA,IAAI66B,GAAgB76B,EAAK66B,cACrBje,EAAc5c,EAAK4c,YACnBkf,EAAgB97B,EAAK86B,OAIzB,OAAKle,IAAgBie,EAQhBA,GAKDv2C,GAAG2F,QAAQc,OAAO65B,KAAKtgC,GAAG+vC,SAASrpC,UAAU+sC,wBAAyB8C,IAAkB,EAClE,eAAlBA,EACOv2C,GAAGkT,kBAAkByD,aAAe4/B,EAAgBje,EAGxDie,EATAje,EARe,eAAlBkf,EACO,YAEJA,GAqBff,SAAU,SAAS76B,EAAc67B,GAC7B,YAEA,IAAIC,GAAa97B,EAAa3J,YAAY,KACtC0lC,EAAcF,EAAwBl3C,MAAQ,YAC9Ci3C,EAAgBC,EAAwBjB,QACxCoB,EAAa,GACbC,EAAY73C,GAAG8R,aAAa8J,GAC5Bk8B,EAAgB,EAmBpB,OAjBIL,GAAwB11C,MAAQ01C,EAAwB11C,KAAK8F,OAAO9B,SACpE+xC,EAAgB,KAAOL,EAAwB11C,KAAO,KAGtD21C,GAAc,GACdE,EAAah8B,EAAa1J,OAAO,EAAGwlC,GAEhCF,IAAkBG,IAClBE,EAAYF,EAAYjyC,MAAM,KAAK,IAGvCkyC,GAAcE,EAAgB,IAAMD,GAGpCD,EAAah8B,EAAek8B,EAGzBF,GAIX/B,gBAAiB,SAASnd,GACtB,YAIA,OAFAA,GAAQ14B,GAAG6B,UAAW62B,GAEfA,EAAMqf,KAAK,SAASC,EAAGC,GAC1B,MAAID,GAAE52B,QAAU62B,EAAE72B,QACP,EAEP42B,EAAE52B,QAAU62B,EAAE72B,SACP,EAEJ,KAIfs1B,qBAAsB,SAASh7B,EAAMw8B,GACjC,YAEA,IAAIz/B,GAAOpY,KACPihB,EAAuB5F,EAAK4F,qBAC5Bnb,EAAMuV,EAAKvV,IACXib,EAAU1F,EAAK0F,QACfiX,EAAS3c,EAAK2c,OACd93B,EAAOmb,EAAKnb,KACZgE,EAAUmX,EAAKnX,QACfoyC,EAAaj7B,EAAKi7B,WAClBle,EAAc/c,EAAK+c,aAAmC,eAApByf,EAAW33C,MAAkC,eAATA,EACtE82C,EAAgB,GAAIr3C,IAAG+L,QACvBosC,EAAiB,GAAIn4C,IAAG24B,eAAexyB,GACvC9B,EAASwF,SAASC,cAAc,SA8BpC,OA5BA3D,GAAI,6CAA+C+xC,EAAWn2C,MAE9Do2C,EAAer2B,SAASo2B,EAAY7zC,GAAS+c,QAASA,EAASiX,OAAQA,EAAQ/W,qBAAsBA,IAAuBlK,KAAK,WAC7H,GAAIghC,GAAqB/zC,EAAOI,UAAUlE,EAAMgE,GAC5C8zC,EAAgB,WACZlyC,EAAI,yCAA2C+xC,EAAWn2C,KAC1D,IAAI0J,GAAOzL,GAAGwE,cAAc4zC,EAC5Bf,GAAcprC,QAAQR,GAG1BgtB,GACAhgB,EAAK6/B,kBAAkBJ,EAAYE,EAAoBjyC,GAAKiR,KAAK,SAASmhC,GACtEH,EAAqBG,EACrBF,KAEJ,WACIlyC,EAAI,wFAAyF,SAC7FkyC,MAIJA,KAEL,WACClyC,EAAI,iDAAmD+xC,EAAWn2C,KAAM,SACxEs1C,EAAclrC,QAAQwqC,KAGnBU,GAIXiB,kBAAmB,SAASE,EAAeJ,EAAoBjyC,GAC3D,YAEA,IAAIsyC,GAAS,GAAI5sC,YACb6sC,EAAkB,GAAI14C,IAAG+L,QACzB4sC,EAAuB,EAc3B,OAZAF,GAAOzsC,OAAS,WACZ2sC,EAAuBF,EAAO91C,OAC9B+1C,EAAgBzsC,QAAQjM,GAAG44C,aAAajM,QAAQgM,EAAsBP,KAG1EK,EAAOvsC,QAAU,WACb/F,EAAI,mBAAqBqyC,EAAcz2C,KAAO,2DAA4D,SAC1G22C,EAAgBvsC,WAGpBssC,EAAOI,cAAcL,GAEdE,GAGXI,eAAgB,SAASp0C,GACrB,YAEA,IAAIE,GAAYE,EAAYH,EAAaE,CAsBzC,OAlBID,GADAF,EAAQgB,MAAM,KAAK,GAAGC,QAAQ,WAAa,EAC9BC,KAAKlB,EAAQgB,MAAM,KAAK,IAGxBG,UAAUnB,EAAQgB,MAAM,KAAK,IAI9CZ,EAAaJ,EAAQgB,MAAM,KAAK,GAC3BA,MAAM,KAAK,GACXA,MAAM,KAAK,GAGhBf,EAAc,GAAImB,aAAYlB,EAAWmB,QACzClB,EAAW,GAAImB,YAAWrB,GAC1B3E,GAAG+C,KAAK6B,EAAY,SAAS5B,EAAKiD,GAC9BpB,EAAS7B,GAAOiD,EAAUC,WAAW,KAGlC7F,KAAK04C,YAAYp0C,EAAaG,IAGzCi0C,YAAa,SAAS/zC,EAAMV,GACxB,YAEA,IAAIW,GAAcC,OAAOD,aACjBC,OAAOC,mBACPD,OAAOE,gBACPF,OAAOG,cACXC,EAAcL,GAAe,GAAIA,EAErC,OAAIK,IACAA,EAAYC,OAAOP,GACZM,EAAYE,QAAQlB,IAGpB,GAAImB,OAAMT,IAAQzE,KAAM+D,OChZ3CtE,GAAG44C,aAAe,WAGjB,GAAIA,KA+KD,OA7KAA,GAAaI,QAAU,oEAMvBJ,EAAaK,SAAW,SAASrvC,GAE7B,GACIsvC,GAAMC,EACNC,EAAMC,EAAMC,EAFZC,EAAS,GACGC,EAAO,GACDC,EAAO,GACzB3oC,EAAI,CAER,GACIooC,GAAOtvC,EAAMkH,KACbqoC,EAAOvvC,EAAMkH,KACb0oC,EAAO5vC,EAAMkH,KAEbsoC,EAAOF,GAAQ,EACfG,GAAgB,EAAPH,IAAa,EAAMC,GAAQ,EACpCG,GAAgB,GAAPH,IAAc,EAAMK,GAAQ,EACrCC,EAAc,GAAPD,EAEHE,MAAMP,GACPG,EAAOG,EAAO,GACNC,MAAMF,KACdC,EAAO,IAGVF,EAASA,EACNl5C,KAAK24C,QAAQjpC,OAAOqpC,GACpB/4C,KAAK24C,QAAQjpC,OAAOspC,GACpBh5C,KAAK24C,QAAQjpC,OAAOupC,GACpBj5C,KAAK24C,QAAQjpC,OAAO0pC,GACvBP,EAAOC,EAAOK,EAAO,GACrBJ,EAAOC,EAAOC,EAAOG,EAAO,SACvB3oC,EAAIlH,EAAM7D,OAEnB,OAAOwzC,IAGXX,EAAajM,QAAU,SAASgN,EAAgBC,GAE5C,GAAIC,GAAuB,yBAE3B,KAAKF,EAAelmC,MAAMomC,GAEzB,MAAOD,EAGR,IAAIE,GAAWz5C,KAAK05C,SAASJ,EAAep3C,QAAQs3C,EAAsB,KACtEG,EAAW35C,KAAK45C,eAAeH,GAE/BzkB,EAAQh1B,KAAK65C,iBAAiBN,EAAmBI,EAErD,OAAOH,GAAuBx5C,KAAK44C,SAAS5jB,IAKhDujB,EAAasB,iBAAmB,SAASN,EAAmBI,GAEpD,GAAIG,GAAY95C,KAAK+5C,aAAaJ,GAC9BK,EAAgBh6C,KAAKi6C,WAAWV,EAAmBO,GACnDI,EAAU,GAAIv0C,YAAWq0C,EAE7B,OAAOE,IAIf3B,EAAawB,aAAe,SAASJ,GAG7B,IAAK,GADDQ,GACKC,EAAI,EAAGA,EAAIT,EAASj0C,OAAQ00C,IAGjC,GADAD,EAAMR,EAASS,GACD,KAAVD,EAAI,GAAsB,KAAVA,EAAI,GAEpB,MAAOA,EAGf,WAIR5B,EAAa0B,WAAa,SAASV,EAAmBO,GAE9C,GAAIO,GAAYd,EAAkBr3C,QAAQ,0BAA2B,IACjEo4C,EAAMt6C,KAAK05C,SAASW,GACpBE,EAAgBD,EAAIh1C,QAAQ,IAAI,GAChCk1C,EAAMF,EAAIzyC,MAAM,EAAG0yC,GACnBE,EAAMH,EAAIzyC,MAAM0yC,GAChBG,EAAQF,CAIb,OAFCE,GAAQA,EAAM3qC,OAAO+pC,GACrBY,EAAQA,EAAM3qC,OAAO0qC,IAM7BlC,EAAaqB,eAAiB,SAASe,GAKnC,IAHA,GAAIC,GAAO,EACPjB,OAGJ,CACI,GAA2B,KAAvBgB,EAAcC,GAA0C,KAA3BD,EAAcC,EAAO,GAAW,KACjE,IAA2B,KAAvBD,EAAcC,GAA0C,KAA3BD,EAAcC,EAAO,GAElDA,GAAQ,MAGZ,CACI,GAAIl1C,GAAmC,IAA1Bi1C,EAAcC,EAAO,GAAWD,EAAcC,EAAO,GAC9DC,EAAWD,EAAOl1C,EAAS,EAC3By0C,EAAMQ,EAAc9yC,MAAM+yC,EAAMC,EACpClB,GAAS92C,KAAKs3C,GACdS,EAAOC,EAEX,GAAID,EAAOD,EAAcj1C,OAAQ,MAGrC,MAAOi0C,IAKXpB,EAAamB,SAAW,SAASnwC,GAE7B,GACIsvC,GAAMC,EACNC,EAAMC,EAAMC,EADAE,EAAO,GACDC,EAAO,GACzB3oC,EAAI,EACJ6pC,KAGAQ,EAAa,qBACjB,IAAIA,EAAWl3C,KAAK2F,GAChB,KAAM,IAAIpI,OAAM,uHAGpBoI,GAAQA,EAAMrH,QAAQ,sBAAuB,GAE7C,GACI62C,GAAO/4C,KAAK24C,QAAQrzC,QAAQiE,EAAMmG,OAAOe,MACzCuoC,EAAOh5C,KAAK24C,QAAQrzC,QAAQiE,EAAMmG,OAAOe,MACzCwoC,EAAOj5C,KAAK24C,QAAQrzC,QAAQiE,EAAMmG,OAAOe,MACzC2oC,EAAOp5C,KAAK24C,QAAQrzC,QAAQiE,EAAMmG,OAAOe,MAEzCooC,EAAQE,GAAQ,EAAMC,GAAQ,EAC9BF,GAAgB,GAAPE,IAAc,EAAMC,GAAQ,EACrCE,GAAgB,EAAPF,IAAa,EAAKG,EAE3BkB,EAAIz3C,KAAKg2C,GAEG,IAARI,GACDqB,EAAIz3C,KAAKi2C,GAEA,IAARM,GACDkB,EAAIz3C,KAAKs2C,GAGZN,EAAOC,EAAOK,EAAO,GACrBJ,EAAOC,EAAOC,EAAOG,EAAO,SAEvB3oC,EAAIlH,EAAM7D,OAEnB,OAAO40C,IAIJ/B,KC7KX54C,GAAG84B,cAAgB,SAASrpB,EAAUmT,GAClC,YAEA,IAAIw4B,MACAC,EAAc,EACdC,EAAY,EAEZC,GAAiB,EACjBC,GAAgB,EAChBC,EAAgB,SAAS3wB,EAAQC,GACzBD,IAAWywB,GAAkBxwB,IAAUywB,GACvC/rC,EAASqb,EAAQC,GAGrBwwB,EAAiBzwB,EACjB0wB,EAAgBzwB,GAQpB2wB,EAAmB,SAAS3pB,EAAQ4pB,GAChC,GAAIC,IAAO,CASX,OAPA57C,IAAG+C,KAAKgvB,EAAQ,SAAS/uB,EAAK64C,GAC1B,GAAI77C,GAAG2F,QAAQg2C,EAAWE,IAAa,EAEnC,MADAD,IAAO,GACA,IAIRA,GAGXzwB,EAAW,SAAS1Q,GAChBqhC,EAAoBrhC,GAAI,GAAI,SACrB2gC,GAAgB3gC,IAG3BuX,EAAgB,SAASF,EAAYC,EAAQ4pB,IACnB,IAAlB5pB,EAAOhsB,QAAgB21C,EAAiB3pB,EAAQ4pB,MAChDF,EAAcH,EAAWA,GACzBj7C,KAAK8Z,UAIb4hC,EAAQ,SAASthC,GACb,GAAIoB,GAAO+G,EAAQnI,EAGfoB,GAAO,IACPigC,EAAoBrhC,EAAI,EAAGoB,GAC3Bu/B,EAAgB3gC,IAAOqQ,OAAQ,EAAGC,MAAOlP,KAYjDigC,EAAsB,SAASrhC,EAAIuhC,EAAWC,GAC1C,GAAIC,GAAYd,EAAgB3gC,GAAM2gC,EAAgB3gC,GAAIqQ,OAAS,EAC/DqxB,EAAWf,EAAgB3gC,GAAM2gC,EAAgB3gC,GAAIsQ,MAAQ,CAE7DixB,MAAc,GAAMC,KAAa,GACjCZ,GAAea,EACfZ,GAAaa,IAGTH,IACAX,GAAeW,EAAYE,GAE3BD,IACAX,GAAaW,EAAWE,IAIhCV,EAAcJ,EAAaC,GAGnCt7C,IAAG6B,OAAOxB,MAEN2xB,cAAeA;AAGfhW,eAAgB,SAASvB,EAAI4B,EAAWD,GAChCA,IAAcpc,GAAG+a,OAAOwC,UAAYnB,IAAcpc,GAAG+a,OAAOsC,SAC5D8N,EAAS1Q,GAEJ2B,IAAcpc,GAAG+a,OAAOY,YAC7BogC,EAAMthC,IAKdiY,qBAAsB,SAASjY,EAAIqQ,EAAQC,GACvC+wB,EAAoBrhC,EAAIqQ,EAAQC,GAChCqwB,EAAgB3gC,IAAOqQ,OAAQA,EAAQC,MAAOA,IAIlD4J,UAAW,SAASla,GAChBshC,EAAMthC,IAGVN,MAAO,WACHihC,KACAC,EAAc,EACdC,EAAY,MCzHxBt7C,GAAG6nB,aAAe,SAAS1P,GACvB,YAYA,SAASikC,GAAQ90C,GACb,MAAOA,GAAK/G,MACwB,IAAhC+G,EAAK/G,KAAKoF,QAAQ,UAG1B,QAAS02C,KACLC,EAAqBt8C,GAAGuY,EAAQuP,eAAexnB,OAAO,QAAS,SAAS6zB,GACpE,GAAIooB,GAAgBpoB,EAAMooB,aAEtBA,IACAv8C,GAAG+C,KAAKw5C,EAAc/xC,MAAO,SAASxH,EAAKsE,GACvC,GAAI80C,EAAQ90C,GAAO,CACf,GAAImE,GAAOnE,EAAKk1C,WAChBjkC,GAAQiO,UAAUwB,cAAcvc,QAOpD,QAASgxC,KACDH,GACAA,IAhCR,GAAI/jC,GAAS+jC,CAEb/jC,IACIuP,cAAe,KACftB,WACIrgB,IAAK,SAASC,EAASC,KACvB2hB,cAAe,SAASvc,OA8BhCzL,GAAG6B,OAAO0W,EAASJ,GACnBkkC,IAEAr8C,GAAG6B,OAAOxB,MACN8Z,MAAO,WACHsiC,QCpCZz8C,GAAGmvB,YAAc,SAAS5W,EAASmkC,EAAav2C,GAC5C,YAgCA,SAASw2C,GAAqBC,GACtBA,EAAO14C,aAAa,YACpBuU,EAAK+W,YAAcotB,EAAO14C,aAAa,WAM/C,QAAS24C,GAAaD,EAAQE,GAC1B,QAAIF,EAAOG,gBAAkBH,EAAOG,mBAChC52C,EAAI,yDAA0D,aAC9D22C,MAQR,QAASE,GAAoBJ,GACzB,GAAIE,GAAeF,EAAOK,MAG1Bj9C,IAAG48C,GAAQt8C,OAAO,SAAU,SAAS6zB,GACjCA,EAAQA,GAASjvB,OAAOivB,MAEpBA,EAAMllB,eACNklB,EAAMllB,iBAGNklB,EAAMhlB,aAAc,EAGxB0tC,EAAaD,EAAQE,IAAiBJ,MAK1CE,EAAOK,OAAS,WACZJ,EAAaD,EAAQE,IAAiBJ,KAM9C,QAASQ,GAAgBN,GAarB,MAZIA,KACI58C,GAAGwH,SAASo1C,KACZA,EAAS/yC,SAAS26B,eAAeoY,IAGjCA,IACAz2C,EAAI,8BACJw2C,EAAqBC,GACrB1kB,GAAmB8kB,EAAoBJ,KAIxCA,EAzFX,GAAInkC,GAAQpY,KACR63B,EAAkB3f,EAAQ2f,gBAC1B0kB,EAASrkC,EAAQtY,QACjBqvB,EAAa/W,EAAQ+W,UAGzBtvB,IAAG6B,OAAOxB,MAINmvB,YAAa,KAIbD,cAAeD,EAGfF,gBAAgB,EAGhBC,sBAAuB,WAEnB,MAAc,OAAVutB,EACO,KAGJnkC,EAAK0kC,UAAUP,MAkE9BA,EAASM,EAAgBN,GACzBv8C,KAAK+uB,iBAAmBwtB,GAG5B58C,GAAG6B,OAAO7B,GAAGmvB,YAAYzoB,WAGrBy2C,UAAW,SAAS7rC,GAChB,YACA,IAAIf,MACA6sC,EAAoB,SAAS78C,GACzB,GAAI88C,IACA,SACA,QACA,QACA,SAGJ,OAAOr9C,IAAG2F,QAAQ03C,EAAiB98C,EAAK+I,eAAiB,GAE7Dg0C,EAAkB,SAAS/8C,GACvB,MAAOP,IAAG2F,SAAS,WAAY,SAAUpF,EAAK+I,gBAAkB,GAEpEi0C,EAAc,SAAShP,GACnB,SAAI+O,EAAgB/O,EAAGhuC,OAAUguC,EAAGiP,UAI7BjP,EAAGj7B,UAAsC,WAA1Bi7B,EAAGhuC,KAAK+I,eAElCm0C,EAAc,SAASC,GACnB,GAAI32C,GAAQ,IASZ,OAPA/G,IAAG+C,KAAK/C,GAAG09C,GAAQt6C,WAAY,SAASJ,EAAKK,GACzC,GAAoC,WAAhCA,EAAMmG,QAAQF,eAA8BjG,EAAMs6C,SAElD,MADA52C,GAAQ1D,EAAM0D,OACP,IAIRA,EAmBf,OAhBA/G,IAAG+C,KAAKuO,EAAKssC,SAAU,SAAS56C,EAAKurC,GACjC,IAAKvuC,GAAGiJ,QAAQslC,GAAI,IAAsC,aAA7BA,EAAG/kC,QAAQF,gBACpC8zC,EAAkB7O,EAAGhuC,OACpBg9C,EAAYhP,IAIZ,GAAiC,WAA7BA,EAAG/kC,QAAQF,gBAA+Bi0C,EAAYhP,GAAK,CAChE,GAAIxnC,GAAQ02C,EAAYlP,EAEV,QAAVxnC,IACAwJ,EAAIg+B,EAAGxsC,MAAQgF,QANnBwJ,GAAIg+B,EAAGxsC,MAAQwsC,EAAGxnC,QAWnBwJ,KCzJfvQ,GAAGwiC,YAAcxiC,GAAGwiC,gBACpBxiC,GAAGwiC,YAAYyB,kBAAoB,SAAS1rB,EAASmlB,GACjD,YAUA,SAASmgB,GAAqBpjC,EAAI8pB,GAG9B,GAAInT,GAAU0sB,EAAKC,CAGnB,KAEID,EAAMvZ,EAAOe,iBAAmBf,EAAOyZ,cAAcn0C,SACrDk0C,EAAYD,EAAIjZ,KAAKt1B,UAErBpJ,EAAI,yCACJA,EAAI,eAAiB43C,GAEjBA,GAAaA,EAAUtqC,MAAM,YAC7BsqC,EAAYD,EAAIjZ,KAAKvhC,WAAWA,WAAW26C,WAG/C7sB,EAAW3C,EAAQwW,mBAAmB8Y,GAE1C,MAAO1zC,GACHlE,EAAI,wDAA0DkE,EAAMjE,QAAU,IAAK,SACnFgrB,GAAYnlB,SAAS,GAGzB,MAAOmlB,GAMX,QAAS8sB,GAAWzjC,EAAI8pB,GACpB,GAAIpmB,GAAS5F,EAAQ4O,YAAY7E,IAAI7H,GACjCmM,EAA0C,QAAjCrO,EAAQqO,OAAOtd,cAA0B,MAAQ,OAC1D8U,EAAW7F,EAAQ6O,cAAc9E,IAAI7H,GACrC1Y,EAAO2e,EAAQjG,EAKnB,OAHA0D,GAAO5F,EAAQ0O,UAAY9D,EAAQ1I,GACnC0D,EAAO5F,EAAQwd,eAAiBh0B,EAEzB0sB,EAAQ+W,oBACX5e,OAAQA,EACRxI,SAAUA,EACVD,OAAQA,EACR8X,aAAc1d,EAAQ0d,aACtBwP,WAAYlB,EAAOxiC,OArD3B,GAAI0sB,GAAUpuB,KACVqgB,EAAUgd,EAAMhd,QAChByC,EAAUua,EAAMva,QAChBhd,EAAMu3B,EAAMv3B,GAsDhB9F,MAAK6hC,WAAa,SAASznB,GACvB,GAGInJ,GAHA1H,EAAQ6kB,EAAQ5U,SAASY,GACzB8pB,EAAS9V,EAAQ8W,cAAc9qB,GAC/B3O,EAAU,GAAI9L,IAAG+L,OA8BrB,OA3BAuF,GAAO4sC,EAAWzjC,EAAI8pB,GACtBjzB,EAAKE,YAAY5H,GAEjB6kB,EAAQ2W,iBAAiBb,EAAQ,SAAS4Z,GACtCh4C,EAAI,gBAEJ,IAAIirB,GAAW+sB,EAAsBA,EAAsBN,EAAqBpjC,EAAI8pB,EAEpF9V,GAAQyW,iBAAiBzqB,GAGpBlC,EAAQ8O,KAAKqI,UACd1vB,GAAGukC,GAAQnjC,SAGXgwB,EAASnlB,QACTH,EAAQG,QAAQmlB,GAGhBtlB,EAAQK,QAAQilB,KAIxBjrB,EAAI,8BAAgCsU,GACpCnJ,EAAK2rC,SACLj9C,GAAGsR,GAAMlQ,SAEF0K,GAGX9L,GAAG6B,OAAOxB,KAAM,GAAIL,IAAGikC,mBACnB1rB,SACI6rB,OAAQ7rB,EAAQ8O,KAAKqI,SACrBvF,UAAW5R,EAAQ4R,WAGvBuT,OACIvS,SAAU5S,EAAQ4S,SAClBzK,QAASA,EACTyC,QAASA,EACThd,IAAKA,OC5GjBnG,GAAGwiC,YAAcxiC,GAAGwiC,gBACpBxiC,GAAGwiC,YAAYmD,iBAAmB,SAASjqB,EAAMgiB,GAC7C,YAEA,IAAIjP,GAAUpuB,KACVqgB,EAAUgd,EAAMhd,QAChBkC,EAAU8a,EAAM9a,QAChBO,EAAUua,EAAMva,QAChBhd,EAAMu3B,EAAMv3B,IACZi4C,EAAY1iC,EAAKsa,gBAAkBta,EAAKua,aAExCooB,EAA4B,SAAS5jC,EAAI0D,EAAQwN,GAC7C,GAAI9P,GAAO+G,EAAQnI,GACf1Y,EAAO2e,EAAQjG,EAEnB0D,GAAOzC,EAAK/F,SAAS4hB,WAAWC,WAAa7L,EAAUkc,KACvD1pB,EAAOzC,EAAK/F,SAAS4hB,WAAWE,gBAAkB9L,EAAU3gB,MAC5DmT,EAAOzC,EAAK/F,SAAS4hB,WAAWG,WAAa/L,EAAU9P,KACvDsC,EAAOzC,EAAK/F,SAAS4hB,WAAWK,YAAcjM,EAAUmc,MACxD3pB,EAAOzC,EAAKwa,mBAAqBra,EAM7BuiC,IACAjgC,EAAOzC,EAAKqa,eAAiBh0B,IAIrCu8C,EAAyB,GAAIt+C,IAAGwiC,YAAY+b,4BACxCl3B,KAAM3L,EAAK2L,KACXjJ,SAAU1C,EAAK/F,SAAS1J,QAAQmS,SAChCjY,IAAKA,IAGTq4C,EAAiC,SAAS/jC,EAAI7G,GAC1C,GAAI9H,GAAU,GAAI9L,IAAG+L,OAerB,OAbA6H,GAAIknB,mBAAqB,WACrB,GAAuB,IAAnBlnB,EAAI4nB,WAAkB,CACtB,GAAI74B,GAAS87C,EAAwBhkC,EAAI7G,EAErCjR,GAAOsJ,QACPH,EAAQG,QAAQtJ,EAAOyuB,SAAUxd,GAGjC9H,EAAQK,QAAQxJ,EAAOyuB,SAAUxd,KAKtC9H,GAGX4yC,EAA0B,SAASjkC,GAC/B,GAAI0D,GAASzC,EAAKyL,YAAY7E,IAAI7H,GAC9B1Y,EAAO2e,EAAQjG,GACfoB,EAAO+G,EAAQnI,EAOnB,OALA0D,GAAOzC,EAAKuL,UAAY9D,EAAQ1I,GAChC0D,EAAOzC,EAAKqa,eAAiBh0B,EAC7Boc,EAAOzC,EAAKwa,mBAAqBra,EACjCsC,EAAOzC,EAAK/F,SAAS4hB,WAAWK,YAAcnJ,EAAQ8Q,gBAAgB9kB,GAE/D0D,GAGXwgC,EAAwB,SAAS/qC,EAAKwd,GAClC,MAAOpxB,IAAG2F,SAAS,IAAK,IAAK,IAAK,IAAK,KAAMiO,EAAImH,QAAU,IACtDqW,EAASnlB,SACVmlB,EAASjX,OAGjBskC,EAA0B,SAAShkC,EAAI7G,GACnC,GAAIwd,EAOJ,OALAjrB,GAAI,sCAAwCsU,GAC5CtU,EAAI,kBAAoByN,EAAI8hC,cAE5BtkB,EAAWwtB,GAAc,EAAMhrC,IAG3B3H,SAAU0yC,EAAsB/qC,EAAKwd,GACrCA,SAAUA,IAKlBwtB,EAAgB,SAAS10C,EAAQ0J,GAC7B,GAAIwd,KAEJ,KACIjrB,EAAInG,GAAG8H,OAAO,4CAA6C8L,EAAImH,OAAQnH,EAAI8hC,eAC3EtkB,EAAWpxB,GAAGyR,UAAUmC,EAAI8hC,cAEhC,MAAOrrC,GACHH,GAAU/D,EAAI,qDAAuDkE,EAAMjE,QAAU,IAAK,SAG9F,MAAOgrB,IAGXytB,EAA4B,SAASpkC,GACjC,GAAI3O,GAAU,GAAI9L,IAAG+L,OAcrB,OAZAuyC,GAAuBQ,SACfrkC,EACAgU,EAAQ6Y,WAAW7sB,GACnBikC,EAAwBjkC,GACxBiB,EAAKwL,cAAc5E,IAAI7H,IAE1BrD,KAAK,SAASxD,GACX9H,EAAQG,QAAQ2yC,GAAc,EAAOhrC,GAAMA,IAC5C,SAASA,GACR9H,EAAQK,QAAQyyC,GAAc,EAAOhrC,GAAMA,KAG5C9H,GAGXizC,EAA8B,SAAS5gC,EAAQvK,EAAK7I,EAAY0P,GAC5D,GAAItJ,GAAW,GAAInH,UACf4c,EAASlL,EAAKkL,OACdxI,EAAW1C,EAAK0L,cAAc9E,IAAI7H,GAClC1Y,EAAO2e,EAAQjG,GACfoB,EAAO+G,EAAQnI,EAuBnB,OArBA0D,GAAOzC,EAAKuL,UAAY9D,EAAQ1I,GAChC0D,EAAOzC,EAAKqa,eAAiBh0B,EAEzBq8C,IACAjgC,EAAOzC,EAAKwa,mBAAqBra,GAIhCH,EAAKua,eACDmoB,IACDjgC,EAAOzC,EAAKyO,WAAapoB,GAE7Bqc,EAAWpe,GAAGsQ,QAAQ6N,EAAQC,IAGlCxK,EAAIqnB,KAAKrU,EAAQxI,GAAU,GAEvB1C,EAAK2L,KAAKqI,UAAYhU,EAAK2L,KAAK4Q,kBAChCrkB,EAAIC,iBAAkB,GAGtBuqC,GACI1iC,EAAKua,cACLj2B,GAAGkR,aAAaiN,EAAQhN,GAG5BA,EAAS5L,OAAOmW,EAAKyO,UAAWpf,GACzBoG,GAGJpG,GAGXi0C,EAAmB,SAASvkC,EAAI7G,GAC5B,GAAIqrC,GAAevjC,EAAKwL,cAAc5E,IAAI7H,GACtC1P,EAAa0jB,EAAQ7M,QAAQnH,EAEjC7G,GAAIkoB,iBAAiB,SAAU,oBAC/BloB,EAAIkoB,iBAAiB,mBAAoB,kBACzCloB,EAAIkoB,iBAAiB,gBAAiB,YAEjCsiB,IACDxqC,EAAIkoB,iBAAiB,eAAgB,4BAErCloB,EAAIkoB,iBAAiB,cAAe/wB,EAAWxK,OAGnDP,GAAG+C,KAAKk8C,EAAc,SAASl9C,EAAMkB,GACjC2Q,EAAIkoB,iBAAiB/5B,EAAMkB,KAIvCjD,IAAG6B,OAAOxB,MACNy/B,YAAa,SAASrlB,EAAI+jB,EAAUzG,GAChC,GAGIjsB,GAASozC,EAAQ/gC,EAHjBwN,EAAY8C,EAAQgQ,cAAchkB,EAAI+jB,GACtC5qB,EAAM6a,EAAQ6Y,WAAW7sB,EAAI+jB,EACtB5b,GAAQnI,EAgBnB,OAbA3O,GAAU0yC,EAA+B/jC,EAAI7G,GAC7C6a,EAAQka,yBAAyBluB,EAAI+jB,EAAU7S,EAAU9P,MACzDsC,EAASzC,EAAKyL,YAAY7E,IAAI7H,GAC9B4jC,EAA0B5jC,EAAI0D,EAAQwN,GAElCoM,IACA5Z,EAAOzC,EAAKjF,OAAO8gB,WAAWQ,WAAY,GAG9CmnB,EAASH,EAA4B5gC,EAAQvK,EAAK+X,EAAUlgB,KAAMgP,GAClEukC,EAAiBvkC,EAAI7G,GACrBA,EAAIunB,KAAK+jB,GAEFpzC,GAGXo2B,WAAY,SAASznB,GACjB,GACI3O,GAAS8H,EAAKuK,EAAQ+gC,EADtBn0C,EAAa0jB,EAAQ7M,QAAQnH,EAWjC,OARA7G,GAAM6a,EAAQ6Y,WAAW7sB,GACzBgU,EAAQka,yBAAyBluB,GACjC3O,EAAU0yC,EAA+B/jC,EAAI7G,GAC7CuK,EAASzC,EAAKyL,YAAY7E,IAAI7H,GAC9BykC,EAASH,EAA4B5gC,EAAQvK,EAAK7I,EAAY0P,GAC9DukC,EAAiBvkC,EAAI7G,GACrBA,EAAIunB,KAAK+jB,GAEFpzC,KAIf9L,GAAG6B,OAAOxB,KAAM,GAAIL,IAAG2lC,kBACnBptB,QAASvY,GAAG6B,QAAQ4oB,UAAW,eAAgB/O,GAC/CgiB,MAAO19B,GAAG6B,QAAQugB,YAAa1G,EAAK0L,cAAc9E,KAAMob,MAG5D19B,GAAGwM,SAASnM,KAAM,SAASsM,GACvB,OACImyB,eAAgB,SAASrkB,GACrB,MAAIiB,GAAK/F,SAAS1J,QAAQmS,SACfygC,EAA0BpkC,GAG1B9N,EAAOmyB,eAAerkB,EAAIza,GAAGgQ,KAAK4uC,EAAev+C,MAAM,SCvOlFL,GAAGwiC,YAAY+b,2BAA6B,SAASpmC,GACjD,YAEA,IAAIwxB,GACA/iB,EAAS,OACTrO,GACI8O,MACIsI,UAAU,EACVD,UAAU,EACVuI,iBAAiB,GAErB7Z,SAAU,KACVjY,IAAK,SAAS4B,EAAK1B,MAEvB84C,KACAC,GACI98B,IAAK,SAAS7H,GACV,MAAOlC,GAAQ6F,UAI3Bpe,IAAG6B,OAAO0W,EAASJ,GAEnBwxB,EAAY3pC,GAAG6B,OAAOxB,KAAM,GAAIL,IAAGg5B,eAC/B6C,aAAc,mBACdM,cAAevV,GACfA,OAAQA,EACRQ,cAAeg4B,EACfrjB,oCAAoC,EACpC1U,KAAM9O,EAAQ8O,KACdlhB,IAAKoS,EAAQpS,IACb8kB,WAAY,SAASxQ,EAAI7G,EAAK8T,GAC1B,GAAI5b,GAAUqzC,EAAS1kC,SAEhB0kC,GAAS1kC,GAEZiN,EACA5b,EAAQK,QAAQyH,GAGhB9H,EAAQG,QAAQ2H,OAK5B5T,GAAG6B,OAAOxB,MACNy+C,SAAU,SAASrkC,EAAI7G,EAAKuK,EAAQsG,GAChC,GAAI3Y,GAAU,GAAI9L,IAAG+L,OAWrB,OATAwM,GAAQpS,IAAI,0CAA4CsU,GAExD0kC,EAAS1kC,GAAM3O,EAEf69B,EAAUlN,cAAchiB,GACnBqiB,WAAW3e,GACX8e,YAAYxY,GACZ0W,KAAKvnB,GAEH9H,OjClEY5G", "file": "fine-uploader.core.min.js", "sourcesContent": [null, "/*globals window, navigator, document, FormData, File, HTMLInputElement, XMLHttpRequest, Blob, Storage, ActiveXObject */\n/* jshint -W079 */\nvar qq = function(element) {\n    \"use strict\";\n\n    return {\n        hide: function() {\n            element.style.display = \"none\";\n            return this;\n        },\n\n        /** Returns the function which detaches attached event */\n        attach: function(type, fn) {\n            if (element.addEventListener) {\n                element.addEventListener(type, fn, false);\n            } else if (element.attachEvent) {\n                element.attachEvent(\"on\" + type, fn);\n            }\n            return function() {\n                qq(element).detach(type, fn);\n            };\n        },\n\n        detach: function(type, fn) {\n            if (element.removeEventListener) {\n                element.removeEventListener(type, fn, false);\n            } else if (element.attachEvent) {\n                element.detachEvent(\"on\" + type, fn);\n            }\n            return this;\n        },\n\n        contains: function(descendant) {\n            // The [W3C spec](http://www.w3.org/TR/domcore/#dom-node-contains)\n            // says a `null` (or ostensibly `undefined`) parameter\n            // passed into `Node.contains` should result in a false return value.\n            // IE7 throws an exception if the parameter is `undefined` though.\n            if (!descendant) {\n                return false;\n            }\n\n            // compareposition returns false in this case\n            if (element === descendant) {\n                return true;\n            }\n\n            if (element.contains) {\n                return element.contains(descendant);\n            } else {\n                /*jslint bitwise: true*/\n                return !!(descendant.compareDocumentPosition(element) & 8);\n            }\n        },\n\n        /**\n         * Insert this element before elementB.\n         */\n        insertBefore: function(elementB) {\n            elementB.parentNode.insertBefore(element, elementB);\n            return this;\n        },\n\n        remove: function() {\n            element.parentNode.removeChild(element);\n            return this;\n        },\n\n        /**\n         * Sets styles for an element.\n         * Fixes opacity in IE6-8.\n         */\n        css: function(styles) {\n            /*jshint eqnull: true*/\n            if (element.style == null) {\n                throw new qq.Error(\"Can't apply style to node as it is not on the HTMLElement prototype chain!\");\n            }\n\n            /*jshint -W116*/\n            if (styles.opacity != null) {\n                if (typeof element.style.opacity !== \"string\" && typeof (element.filters) !== \"undefined\") {\n                    styles.filter = \"alpha(opacity=\" + Math.round(100 * styles.opacity) + \")\";\n                }\n            }\n            qq.extend(element.style, styles);\n\n            return this;\n        },\n\n        hasClass: function(name, considerParent) {\n            var re = new RegExp(\"(^| )\" + name + \"( |$)\");\n            return re.test(element.className) || !!(considerParent && re.test(element.parentNode.className));\n        },\n\n        addClass: function(name) {\n            if (!qq(element).hasClass(name)) {\n                element.className += \" \" + name;\n            }\n            return this;\n        },\n\n        removeClass: function(name) {\n            var re = new RegExp(\"(^| )\" + name + \"( |$)\");\n            element.className = element.className.replace(re, \" \").replace(/^\\s+|\\s+$/g, \"\");\n            return this;\n        },\n\n        getByClass: function(className, first) {\n            var candidates,\n                result = [];\n\n            if (first && element.querySelector) {\n                return element.querySelector(\".\" + className);\n            }\n            else if (element.querySelectorAll) {\n                return element.querySelectorAll(\".\" + className);\n            }\n\n            candidates = element.getElementsByTagName(\"*\");\n\n            qq.each(candidates, function(idx, val) {\n                if (qq(val).hasClass(className)) {\n                    result.push(val);\n                }\n            });\n            return first ? result[0] : result;\n        },\n\n        getFirstByClass: function(className) {\n            return qq(element).getByClass(className, true);\n        },\n\n        children: function() {\n            var children = [],\n                child = element.firstChild;\n\n            while (child) {\n                if (child.nodeType === 1) {\n                    children.push(child);\n                }\n                child = child.nextSibling;\n            }\n\n            return children;\n        },\n\n        setText: function(text) {\n            element.innerText = text;\n            element.textContent = text;\n            return this;\n        },\n\n        clearText: function() {\n            return qq(element).setText(\"\");\n        },\n\n        // Returns true if the attribute exists on the element\n        // AND the value of the attribute is NOT \"false\" (case-insensitive)\n        hasAttribute: function(attrName) {\n            var attrVal;\n\n            if (element.hasAttribute) {\n\n                if (!element.hasAttribute(attrName)) {\n                    return false;\n                }\n\n                /*jshint -W116*/\n                return (/^false$/i).exec(element.getAttribute(attrName)) == null;\n            }\n            else {\n                attrVal = element[attrName];\n\n                if (attrVal === undefined) {\n                    return false;\n                }\n\n                /*jshint -W116*/\n                return (/^false$/i).exec(attrVal) == null;\n            }\n        }\n    };\n};\n\n(function() {\n    \"use strict\";\n\n    qq.canvasToBlob = function(canvas, mime, quality) {\n        return qq.dataUriToBlob(canvas.toDataURL(mime, quality));\n    };\n\n    qq.dataUriToBlob = function(dataUri) {\n        var arrayBuffer, byteString,\n            createBlob = function(data, mime) {\n                var BlobBuilder = window.BlobBuilder ||\n                        window.WebKitBlobBuilder ||\n                        window.MozBlobBuilder ||\n                        window.MSBlobBuilder,\n                    blobBuilder = BlobBuilder && new BlobBuilder();\n\n                if (blobBuilder) {\n                    blobBuilder.append(data);\n                    return blobBuilder.getBlob(mime);\n                }\n                else {\n                    return new Blob([data], {type: mime});\n                }\n            },\n            intArray, mimeString;\n\n        // convert base64 to raw binary data held in a string\n        if (dataUri.split(\",\")[0].indexOf(\"base64\") >= 0) {\n            byteString = atob(dataUri.split(\",\")[1]);\n        }\n        else {\n            byteString = decodeURI(dataUri.split(\",\")[1]);\n        }\n\n        // extract the MIME\n        mimeString = dataUri.split(\",\")[0]\n            .split(\":\")[1]\n            .split(\";\")[0];\n\n        // write the bytes of the binary string to an ArrayBuffer\n        arrayBuffer = new ArrayBuffer(byteString.length);\n        intArray = new Uint8Array(arrayBuffer);\n        qq.each(byteString, function(idx, character) {\n            intArray[idx] = character.charCodeAt(0);\n        });\n\n        return createBlob(arrayBuffer, mimeString);\n    };\n\n    qq.log = function(message, level) {\n        if (window.console) {\n            if (!level || level === \"info\") {\n                window.console.log(message);\n            }\n            else\n            {\n                if (window.console[level]) {\n                    window.console[level](message);\n                }\n                else {\n                    window.console.log(\"<\" + level + \"> \" + message);\n                }\n            }\n        }\n    };\n\n    qq.isObject = function(variable) {\n        return variable && !variable.nodeType && Object.prototype.toString.call(variable) === \"[object Object]\";\n    };\n\n    qq.isFunction = function(variable) {\n        return typeof (variable) === \"function\";\n    };\n\n    /**\n     * Check the type of a value.  Is it an \"array\"?\n     *\n     * @param value value to test.\n     * @returns true if the value is an array or associated with an `ArrayBuffer`\n     */\n    qq.isArray = function(value) {\n        return Object.prototype.toString.call(value) === \"[object Array]\" ||\n            (value && window.ArrayBuffer && value.buffer && value.buffer.constructor === ArrayBuffer);\n    };\n\n    // Looks for an object on a `DataTransfer` object that is associated with drop events when utilizing the Filesystem API.\n    qq.isItemList = function(maybeItemList) {\n        return Object.prototype.toString.call(maybeItemList) === \"[object DataTransferItemList]\";\n    };\n\n    // Looks for an object on a `NodeList` or an `HTMLCollection`|`HTMLFormElement`|`HTMLSelectElement`\n    // object that is associated with collections of Nodes.\n    qq.isNodeList = function(maybeNodeList) {\n        return Object.prototype.toString.call(maybeNodeList) === \"[object NodeList]\" ||\n            // If `HTMLCollection` is the actual type of the object, we must determine this\n            // by checking for expected properties/methods on the object\n            (maybeNodeList.item && maybeNodeList.namedItem);\n    };\n\n    qq.isString = function(maybeString) {\n        return Object.prototype.toString.call(maybeString) === \"[object String]\";\n    };\n\n    qq.trimStr = function(string) {\n        if (String.prototype.trim) {\n            return string.trim();\n        }\n\n        return string.replace(/^\\s+|\\s+$/g, \"\");\n    };\n\n    /**\n     * @param str String to format.\n     * @returns {string} A string, swapping argument values with the associated occurrence of {} in the passed string.\n     */\n    qq.format = function(str) {\n\n        var args =  Array.prototype.slice.call(arguments, 1),\n            newStr = str,\n            nextIdxToReplace = newStr.indexOf(\"{}\");\n\n        qq.each(args, function(idx, val) {\n            var strBefore = newStr.substring(0, nextIdxToReplace),\n                strAfter = newStr.substring(nextIdxToReplace + 2);\n\n            newStr = strBefore + val + strAfter;\n            nextIdxToReplace = newStr.indexOf(\"{}\", nextIdxToReplace + val.length);\n\n            // End the loop if we have run out of tokens (when the arguments exceed the # of tokens)\n            if (nextIdxToReplace < 0) {\n                return false;\n            }\n        });\n\n        return newStr;\n    };\n\n    qq.isFile = function(maybeFile) {\n        return window.File && Object.prototype.toString.call(maybeFile) === \"[object File]\";\n    };\n\n    qq.isFileList = function(maybeFileList) {\n        return window.FileList && Object.prototype.toString.call(maybeFileList) === \"[object FileList]\";\n    };\n\n    qq.isFileOrInput = function(maybeFileOrInput) {\n        return qq.isFile(maybeFileOrInput) || qq.isInput(maybeFileOrInput);\n    };\n\n    qq.isInput = function(maybeInput, notFile) {\n        var evaluateType = function(type) {\n            var normalizedType = type.toLowerCase();\n\n            if (notFile) {\n                return normalizedType !== \"file\";\n            }\n\n            return normalizedType === \"file\";\n        };\n\n        if (window.HTMLInputElement) {\n            if (Object.prototype.toString.call(maybeInput) === \"[object HTMLInputElement]\") {\n                if (maybeInput.type && evaluateType(maybeInput.type)) {\n                    return true;\n                }\n            }\n        }\n        if (maybeInput.tagName) {\n            if (maybeInput.tagName.toLowerCase() === \"input\") {\n                if (maybeInput.type && evaluateType(maybeInput.type)) {\n                    return true;\n                }\n            }\n        }\n\n        return false;\n    };\n\n    qq.isBlob = function(maybeBlob) {\n        if (window.Blob && Object.prototype.toString.call(maybeBlob) === \"[object Blob]\") {\n            return true;\n        }\n    };\n\n    qq.isXhrUploadSupported = function() {\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n\n        return (\n            input.multiple !== undefined &&\n                typeof File !== \"undefined\" &&\n                typeof FormData !== \"undefined\" &&\n                typeof (qq.createXhrInstance()).upload !== \"undefined\");\n    };\n\n    // Fall back to ActiveX is native XHR is disabled (possible in any version of IE).\n    qq.createXhrInstance = function() {\n        if (window.XMLHttpRequest) {\n            return new XMLHttpRequest();\n        }\n\n        try {\n            return new ActiveXObject(\"MSXML2.XMLHTTP.3.0\");\n        }\n        catch (error) {\n            qq.log(\"Neither XHR or ActiveX are supported!\", \"error\");\n            return null;\n        }\n    };\n\n    qq.isFolderDropSupported = function(dataTransfer) {\n        return dataTransfer.items &&\n            dataTransfer.items.length > 0 &&\n            dataTransfer.items[0].webkitGetAsEntry;\n    };\n\n    qq.isFileChunkingSupported = function() {\n        return !qq.androidStock() && //Android's stock browser cannot upload Blobs correctly\n            qq.isXhrUploadSupported() &&\n            (File.prototype.slice !== undefined || File.prototype.webkitSlice !== undefined || File.prototype.mozSlice !== undefined);\n    };\n\n    qq.sliceBlob = function(fileOrBlob, start, end) {\n        var slicer = fileOrBlob.slice || fileOrBlob.mozSlice || fileOrBlob.webkitSlice;\n\n        return slicer.call(fileOrBlob, start, end);\n    };\n\n    qq.arrayBufferToHex = function(buffer) {\n        var bytesAsHex = \"\",\n            bytes = new Uint8Array(buffer);\n\n        qq.each(bytes, function(idx, byt) {\n            var byteAsHexStr = byt.toString(16);\n\n            if (byteAsHexStr.length < 2) {\n                byteAsHexStr = \"0\" + byteAsHexStr;\n            }\n\n            bytesAsHex += byteAsHexStr;\n        });\n\n        return bytesAsHex;\n    };\n\n    qq.readBlobToHex = function(blob, startOffset, length) {\n        var initialBlob = qq.sliceBlob(blob, startOffset, startOffset + length),\n            fileReader = new FileReader(),\n            promise = new qq.Promise();\n\n        fileReader.onload = function() {\n            promise.success(qq.arrayBufferToHex(fileReader.result));\n        };\n\n        fileReader.onerror = promise.failure;\n\n        fileReader.readAsArrayBuffer(initialBlob);\n\n        return promise;\n    };\n\n    qq.extend = function(first, second, extendNested) {\n        qq.each(second, function(prop, val) {\n            if (extendNested && qq.isObject(val)) {\n                if (first[prop] === undefined) {\n                    first[prop] = {};\n                }\n                qq.extend(first[prop], val, true);\n            }\n            else {\n                first[prop] = val;\n            }\n        });\n\n        return first;\n    };\n\n    /**\n     * Allow properties in one object to override properties in another,\n     * keeping track of the original values from the target object.\n     *\n     * Note that the pre-overriden properties to be overriden by the source will be passed into the `sourceFn` when it is invoked.\n     *\n     * @param target Update properties in this object from some source\n     * @param sourceFn A function that, when invoked, will return properties that will replace properties with the same name in the target.\n     * @returns {object} The target object\n     */\n    qq.override = function(target, sourceFn) {\n        var super_ = {},\n            source = sourceFn(super_);\n\n        qq.each(source, function(srcPropName, srcPropVal) {\n            if (target[srcPropName] !== undefined) {\n                super_[srcPropName] = target[srcPropName];\n            }\n\n            target[srcPropName] = srcPropVal;\n        });\n\n        return target;\n    };\n\n    /**\n     * Searches for a given element (elt) in the array, returns -1 if it is not present.\n     */\n    qq.indexOf = function(arr, elt, from) {\n        if (arr.indexOf) {\n            return arr.indexOf(elt, from);\n        }\n\n        from = from || 0;\n        var len = arr.length;\n\n        if (from < 0) {\n            from += len;\n        }\n\n        for (; from < len; from += 1) {\n            if (arr.hasOwnProperty(from) && arr[from] === elt) {\n                return from;\n            }\n        }\n        return -1;\n    };\n\n    //this is a version 4 UUID\n    qq.getUniqueId = function() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            /*jslint eqeq: true, bitwise: true*/\n            var r = Math.random() * 16 | 0, v = c == \"x\" ? r : (r & 0x3 | 0x8);\n            return v.toString(16);\n        });\n    };\n\n    //\n    // Browsers and platforms detection\n    qq.ie = function() {\n        return navigator.userAgent.indexOf(\"MSIE\") !== -1 ||\n            navigator.userAgent.indexOf(\"Trident\") !== -1;\n    };\n\n    qq.ie7 = function() {\n        return navigator.userAgent.indexOf(\"MSIE 7\") !== -1;\n    };\n\n    qq.ie8 = function() {\n        return navigator.userAgent.indexOf(\"MSIE 8\") !== -1;\n    };\n\n    qq.ie10 = function() {\n        return navigator.userAgent.indexOf(\"MSIE 10\") !== -1;\n    };\n\n    qq.ie11 = function() {\n        return qq.ie() && navigator.userAgent.indexOf(\"rv:11\") !== -1;\n    };\n\n    qq.edge = function() {\n        return navigator.userAgent.indexOf(\"Edge\") >= 0;\n    };\n\n    qq.safari = function() {\n        return navigator.vendor !== undefined && navigator.vendor.indexOf(\"Apple\") !== -1;\n    };\n\n    qq.chrome = function() {\n        return navigator.vendor !== undefined && navigator.vendor.indexOf(\"Google\") !== -1;\n    };\n\n    qq.opera = function() {\n        return navigator.vendor !== undefined && navigator.vendor.indexOf(\"Opera\") !== -1;\n    };\n\n    qq.firefox = function() {\n        return (!qq.edge() && !qq.ie11() && navigator.userAgent.indexOf(\"Mozilla\") !== -1 && navigator.vendor !== undefined && navigator.vendor === \"\");\n    };\n\n    qq.windows = function() {\n        return navigator.platform === \"Win32\";\n    };\n\n    qq.android = function() {\n        return navigator.userAgent.toLowerCase().indexOf(\"android\") !== -1;\n    };\n\n    // We need to identify the Android stock browser via the UA string to work around various bugs in this browser,\n    // such as the one that prevents a `Blob` from being uploaded.\n    qq.androidStock = function() {\n        return qq.android() && navigator.userAgent.toLowerCase().indexOf(\"chrome\") < 0;\n    };\n\n    qq.ios6 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 6_\") !== -1;\n    };\n\n    qq.ios7 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 7_\") !== -1;\n    };\n\n    qq.ios8 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 8_\") !== -1;\n    };\n\n    // iOS 8.0.0\n    qq.ios800 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 8_0 \") !== -1;\n    };\n\n    qq.ios = function() {\n        /*jshint -W014 */\n        return navigator.userAgent.indexOf(\"iPad\") !== -1\n            || navigator.userAgent.indexOf(\"iPod\") !== -1\n            || navigator.userAgent.indexOf(\"iPhone\") !== -1;\n    };\n\n    qq.iosChrome = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\"CriOS\") !== -1;\n    };\n\n    qq.iosSafari = function() {\n        return qq.ios() && !qq.iosChrome() && navigator.userAgent.indexOf(\"Safari\") !== -1;\n    };\n\n    qq.iosSafariWebView = function() {\n        return qq.ios() && !qq.iosChrome() && !qq.iosSafari();\n    };\n\n    //\n    // Events\n\n    qq.preventDefault = function(e) {\n        if (e.preventDefault) {\n            e.preventDefault();\n        } else {\n            e.returnValue = false;\n        }\n    };\n\n    /**\n     * Creates and returns element from html string\n     * Uses innerHTML to create an element\n     */\n    qq.toElement = (function() {\n        var div = document.createElement(\"div\");\n        return function(html) {\n            div.innerHTML = html;\n            var element = div.firstChild;\n            div.removeChild(element);\n            return element;\n        };\n    }());\n\n    //key and value are passed to callback for each entry in the iterable item\n    qq.each = function(iterableItem, callback) {\n        var keyOrIndex, retVal;\n\n        if (iterableItem) {\n            // Iterate through [`Storage`](http://www.w3.org/TR/webstorage/#the-storage-interface) items\n            if (window.Storage && iterableItem.constructor === window.Storage) {\n                for (keyOrIndex = 0; keyOrIndex < iterableItem.length; keyOrIndex++) {\n                    retVal = callback(iterableItem.key(keyOrIndex), iterableItem.getItem(iterableItem.key(keyOrIndex)));\n                    if (retVal === false) {\n                        break;\n                    }\n                }\n            }\n            // `DataTransferItemList` & `NodeList` objects are array-like and should be treated as arrays\n            // when iterating over items inside the object.\n            else if (qq.isArray(iterableItem) || qq.isItemList(iterableItem) || qq.isNodeList(iterableItem)) {\n                for (keyOrIndex = 0; keyOrIndex < iterableItem.length; keyOrIndex++) {\n                    retVal = callback(keyOrIndex, iterableItem[keyOrIndex]);\n                    if (retVal === false) {\n                        break;\n                    }\n                }\n            }\n            else if (qq.isString(iterableItem)) {\n                for (keyOrIndex = 0; keyOrIndex < iterableItem.length; keyOrIndex++) {\n                    retVal = callback(keyOrIndex, iterableItem.charAt(keyOrIndex));\n                    if (retVal === false) {\n                        break;\n                    }\n                }\n            }\n            else {\n                for (keyOrIndex in iterableItem) {\n                    if (Object.prototype.hasOwnProperty.call(iterableItem, keyOrIndex)) {\n                        retVal = callback(keyOrIndex, iterableItem[keyOrIndex]);\n                        if (retVal === false) {\n                            break;\n                        }\n                    }\n                }\n            }\n        }\n    };\n\n    //include any args that should be passed to the new function after the context arg\n    qq.bind = function(oldFunc, context) {\n        if (qq.isFunction(oldFunc)) {\n            var args =  Array.prototype.slice.call(arguments, 2);\n\n            return function() {\n                var newArgs = qq.extend([], args);\n                if (arguments.length) {\n                    newArgs = newArgs.concat(Array.prototype.slice.call(arguments));\n                }\n                return oldFunc.apply(context, newArgs);\n            };\n        }\n\n        throw new Error(\"first parameter must be a function!\");\n    };\n\n    /**\n     * obj2url() takes a json-object as argument and generates\n     * a querystring. pretty much like jQuery.param()\n     *\n     * how to use:\n     *\n     *    `qq.obj2url({a:'b',c:'d'},'http://any.url/upload?otherParam=value');`\n     *\n     * will result in:\n     *\n     *    `http://any.url/upload?otherParam=value&a=b&c=d`\n     *\n     * @param  Object JSON-Object\n     * @param  String current querystring-part\n     * @return String encoded querystring\n     */\n    qq.obj2url = function(obj, temp, prefixDone) {\n        /*jshint laxbreak: true*/\n        var uristrings = [],\n            prefix = \"&\",\n            add = function(nextObj, i) {\n                var nextTemp = temp\n                    ? (/\\[\\]$/.test(temp)) // prevent double-encoding\n                    ? temp\n                    : temp + \"[\" + i + \"]\"\n                    : i;\n                if ((nextTemp !== \"undefined\") && (i !== \"undefined\")) {\n                    uristrings.push(\n                        (typeof nextObj === \"object\")\n                            ? qq.obj2url(nextObj, nextTemp, true)\n                            : (Object.prototype.toString.call(nextObj) === \"[object Function]\")\n                            ? encodeURIComponent(nextTemp) + \"=\" + encodeURIComponent(nextObj())\n                            : encodeURIComponent(nextTemp) + \"=\" + encodeURIComponent(nextObj)\n                    );\n                }\n            };\n\n        if (!prefixDone && temp) {\n            prefix = (/\\?/.test(temp)) ? (/\\?$/.test(temp)) ? \"\" : \"&\" : \"?\";\n            uristrings.push(temp);\n            uristrings.push(qq.obj2url(obj));\n        } else if ((Object.prototype.toString.call(obj) === \"[object Array]\") && (typeof obj !== \"undefined\")) {\n            qq.each(obj, function(idx, val) {\n                add(val, idx);\n            });\n        } else if ((typeof obj !== \"undefined\") && (obj !== null) && (typeof obj === \"object\")) {\n            qq.each(obj, function(prop, val) {\n                add(val, prop);\n            });\n        } else {\n            uristrings.push(encodeURIComponent(temp) + \"=\" + encodeURIComponent(obj));\n        }\n\n        if (temp) {\n            return uristrings.join(prefix);\n        } else {\n            return uristrings.join(prefix)\n                .replace(/^&/, \"\")\n                .replace(/%20/g, \"+\");\n        }\n    };\n\n    qq.obj2FormData = function(obj, formData, arrayKeyName) {\n        if (!formData) {\n            formData = new FormData();\n        }\n\n        qq.each(obj, function(key, val) {\n            key = arrayKeyName ? arrayKeyName + \"[\" + key + \"]\" : key;\n\n            if (qq.isObject(val)) {\n                qq.obj2FormData(val, formData, key);\n            }\n            else if (qq.isFunction(val)) {\n                formData.append(key, val());\n            }\n            else {\n                formData.append(key, val);\n            }\n        });\n\n        return formData;\n    };\n\n    qq.obj2Inputs = function(obj, form) {\n        var input;\n\n        if (!form) {\n            form = document.createElement(\"form\");\n        }\n\n        qq.obj2FormData(obj, {\n            append: function(key, val) {\n                input = document.createElement(\"input\");\n                input.setAttribute(\"name\", key);\n                input.setAttribute(\"value\", val);\n                form.appendChild(input);\n            }\n        });\n\n        return form;\n    };\n\n    /**\n     * Not recommended for use outside of Fine Uploader since this falls back to an unchecked eval if JSON.parse is not\n     * implemented.  For a more secure JSON.parse polyfill, use Douglas Crockford's json2.js.\n     */\n    qq.parseJson = function(json) {\n        /*jshint evil: true*/\n        if (window.JSON && qq.isFunction(JSON.parse)) {\n            return JSON.parse(json);\n        } else {\n            return eval(\"(\" + json + \")\");\n        }\n    };\n\n    /**\n     * Retrieve the extension of a file, if it exists.\n     *\n     * @param filename\n     * @returns {string || undefined}\n     */\n    qq.getExtension = function(filename) {\n        var extIdx = filename.lastIndexOf(\".\") + 1;\n\n        if (extIdx > 0) {\n            return filename.substr(extIdx, filename.length - extIdx);\n        }\n    };\n\n    qq.getFilename = function(blobOrFileInput) {\n        /*jslint regexp: true*/\n\n        if (qq.isInput(blobOrFileInput)) {\n            // get input value and remove path to normalize\n            return blobOrFileInput.value.replace(/.*(\\/|\\\\)/, \"\");\n        }\n        else if (qq.isFile(blobOrFileInput)) {\n            if (blobOrFileInput.fileName !== null && blobOrFileInput.fileName !== undefined) {\n                return blobOrFileInput.fileName;\n            }\n        }\n\n        return blobOrFileInput.name;\n    };\n\n    /**\n     * A generic module which supports object disposing in dispose() method.\n     * */\n    qq.DisposeSupport = function() {\n        var disposers = [];\n\n        return {\n            /** Run all registered disposers */\n            dispose: function() {\n                var disposer;\n                do {\n                    disposer = disposers.shift();\n                    if (disposer) {\n                        disposer();\n                    }\n                }\n                while (disposer);\n            },\n\n            /** Attach event handler and register de-attacher as a disposer */\n            attach: function() {\n                var args = arguments;\n                /*jslint undef:true*/\n                this.addDisposer(qq(args[0]).attach.apply(this, Array.prototype.slice.call(arguments, 1)));\n            },\n\n            /** Add disposer to the collection */\n            addDisposer: function(disposeFunction) {\n                disposers.push(disposeFunction);\n            }\n        };\n    };\n}());\n", "/* globals define, module, global, qq */\n(function() {\n    \"use strict\";\n    if (typeof define === \"function\" && define.amd) {\n        define(function() {\n            return qq;\n        });\n    }\n    else if (typeof module !== \"undefined\" && module.exports) {\n        module.exports = qq;\n    }\n    else {\n        global.qq = qq;\n    }\n}());\n", "/* globals qq */\n/**\n * Fine Uploader top-level Error container.  Inherits from `Error`.\n */\n(function() {\n    \"use strict\";\n\n    qq.Error = function(message) {\n        this.message = \"[Fine Uploader \" + qq.version + \"] \" + message;\n    };\n\n    qq.Error.prototype = new Error();\n}());\n", "/*global qq */\nqq.version = \"5.11.8\";\n", "/* globals qq */\nqq.supportedFeatures = (function() {\n    \"use strict\";\n\n    var supportsUploading,\n        supportsUploadingBlobs,\n        supportsFileDrop,\n        supportsAjaxFileUploading,\n        supportsFolderDrop,\n        supportsChunking,\n        supportsResume,\n        supportsUploadViaPaste,\n        supportsUploadCors,\n        supportsDeleteFileXdr,\n        supportsDeleteFileCorsXhr,\n        supportsDeleteFileCors,\n        supportsFolderSelection,\n        supportsImagePreviews,\n        supportsUploadProgress;\n\n    function testSupportsFileInputElement() {\n        var supported = true,\n            tempInput;\n\n        try {\n            tempInput = document.createElement(\"input\");\n            tempInput.type = \"file\";\n            qq(tempInput).hide();\n\n            if (tempInput.disabled) {\n                supported = false;\n            }\n        }\n        catch (ex) {\n            supported = false;\n        }\n\n        return supported;\n    }\n\n    //only way to test for Filesystem API support since webkit does not expose the DataTransfer interface\n    function isChrome21OrHigher() {\n        return (qq.chrome() || qq.opera()) &&\n            navigator.userAgent.match(/Chrome\\/[2][1-9]|Chrome\\/[3-9][0-9]/) !== undefined;\n    }\n\n    //only way to test for complete Clipboard API support at this time\n    function isChrome14OrHigher() {\n        return (qq.chrome() || qq.opera()) &&\n            navigator.userAgent.match(/Chrome\\/[1][4-9]|Chrome\\/[2-9][0-9]/) !== undefined;\n    }\n\n    //Ensure we can send cross-origin `XMLHttpRequest`s\n    function isCrossOriginXhrSupported() {\n        if (window.XMLHttpRequest) {\n            var xhr = qq.createXhrInstance();\n\n            //Commonly accepted test for XHR CORS support.\n            return xhr.withCredentials !== undefined;\n        }\n\n        return false;\n    }\n\n    //Test for (terrible) cross-origin ajax transport fallback for IE9 and IE8\n    function isXdrSupported() {\n        return window.XDomainRequest !== undefined;\n    }\n\n    // CORS Ajax requests are supported if it is either possible to send credentialed `XMLHttpRequest`s,\n    // or if `XDomainRequest` is an available alternative.\n    function isCrossOriginAjaxSupported() {\n        if (isCrossOriginXhrSupported()) {\n            return true;\n        }\n\n        return isXdrSupported();\n    }\n\n    function isFolderSelectionSupported() {\n        // We know that folder selection is only supported in Chrome via this proprietary attribute for now\n        return document.createElement(\"input\").webkitdirectory !== undefined;\n    }\n\n    function isLocalStorageSupported() {\n        try {\n            return !!window.localStorage &&\n                // unpatched versions of IE10/11 have buggy impls of localStorage where setItem is a string\n                qq.isFunction(window.localStorage.setItem);\n        }\n        catch (error) {\n            // probably caught a security exception, so no localStorage for you\n            return false;\n        }\n    }\n\n    function isDragAndDropSupported() {\n        var span = document.createElement(\"span\");\n\n        return (\"draggable\" in span || (\"ondragstart\" in span && \"ondrop\" in span)) &&\n            !qq.android() && !qq.ios();\n    }\n\n    supportsUploading = testSupportsFileInputElement();\n\n    supportsAjaxFileUploading = supportsUploading && qq.isXhrUploadSupported();\n\n    supportsUploadingBlobs = supportsAjaxFileUploading && !qq.androidStock();\n\n    supportsFileDrop = supportsAjaxFileUploading && isDragAndDropSupported();\n\n    supportsFolderDrop = supportsFileDrop && isChrome21OrHigher();\n\n    supportsChunking = supportsAjaxFileUploading && qq.isFileChunkingSupported();\n\n    supportsResume = supportsAjaxFileUploading && supportsChunking && isLocalStorageSupported();\n\n    supportsUploadViaPaste = supportsAjaxFileUploading && isChrome14OrHigher();\n\n    supportsUploadCors = supportsUploading && (window.postMessage !== undefined || supportsAjaxFileUploading);\n\n    supportsDeleteFileCorsXhr = isCrossOriginXhrSupported();\n\n    supportsDeleteFileXdr = isXdrSupported();\n\n    supportsDeleteFileCors = isCrossOriginAjaxSupported();\n\n    supportsFolderSelection = isFolderSelectionSupported();\n\n    supportsImagePreviews = supportsAjaxFileUploading && window.FileReader !== undefined;\n\n    supportsUploadProgress = (function() {\n        if (supportsAjaxFileUploading) {\n            return !qq.androidStock() && !qq.iosChrome();\n        }\n        return false;\n    }());\n\n    return {\n        ajaxUploading: supportsAjaxFileUploading,\n        blobUploading: supportsUploadingBlobs,\n        canDetermineSize: supportsAjaxFileUploading,\n        chunking: supportsChunking,\n        deleteFileCors: supportsDeleteFileCors,\n        deleteFileCorsXdr: supportsDeleteFileXdr, //NOTE: will also return true in IE10, where XDR is also supported\n        deleteFileCorsXhr: supportsDeleteFileCorsXhr,\n        dialogElement: !!window.HTMLDialogElement,\n        fileDrop: supportsFileDrop,\n        folderDrop: supportsFolderDrop,\n        folderSelection: supportsFolderSelection,\n        imagePreviews: supportsImagePreviews,\n        imageValidation: supportsImagePreviews,\n        itemSizeValidation: supportsAjaxFileUploading,\n        pause: supportsChunking,\n        progressBar: supportsUploadProgress,\n        resume: supportsResume,\n        scaling: supportsImagePreviews && supportsUploadingBlobs,\n        tiffPreviews: qq.safari(), // Not the best solution, but simple and probably accurate enough (for now)\n        unlimitedScaledImageSize: !qq.ios(), // false simply indicates that there is some known limit\n        uploading: supportsUploading,\n        uploadCors: supportsUploadCors,\n        uploadCustomHeaders: supportsAjaxFileUploading,\n        uploadNonMultipart: supportsAjaxFileUploading,\n        uploadViaPaste: supportsUploadViaPaste\n    };\n\n}());\n", "/*globals qq*/\n\n// Is the passed object a promise instance?\nqq.isGenericPromise = function(maybePromise) {\n    \"use strict\";\n    return !!(maybePromise && maybePromise.then && qq.isFunction(maybePromise.then));\n};\n\nqq.Promise = function() {\n    \"use strict\";\n\n    var successArgs, failureArgs,\n        successCallbacks = [],\n        failureCallbacks = [],\n        doneCallbacks = [],\n        state = 0;\n\n    qq.extend(this, {\n        then: function(onSuccess, onFailure) {\n            if (state === 0) {\n                if (onSuccess) {\n                    successCallbacks.push(onSuccess);\n                }\n                if (onFailure) {\n                    failureCallbacks.push(onFailure);\n                }\n            }\n            else if (state === -1) {\n                onFailure && onFailure.apply(null, failureArgs);\n            }\n            else if (onSuccess) {\n                onSuccess.apply(null, successArgs);\n            }\n\n            return this;\n        },\n\n        done: function(callback) {\n            if (state === 0) {\n                doneCallbacks.push(callback);\n            }\n            else {\n                callback.apply(null, failureArgs === undefined ? successArgs : failureArgs);\n            }\n\n            return this;\n        },\n\n        success: function() {\n            state = 1;\n            successArgs = arguments;\n\n            if (successCallbacks.length) {\n                qq.each(successCallbacks, function(idx, callback) {\n                    callback.apply(null, successArgs);\n                });\n            }\n\n            if (doneCallbacks.length) {\n                qq.each(doneCallbacks, function(idx, callback) {\n                    callback.apply(null, successArgs);\n                });\n            }\n\n            return this;\n        },\n\n        failure: function() {\n            state = -1;\n            failureArgs = arguments;\n\n            if (failureCallbacks.length) {\n                qq.each(failureCallbacks, function(idx, callback) {\n                    callback.apply(null, failureArgs);\n                });\n            }\n\n            if (doneCallbacks.length) {\n                qq.each(doneCallbacks, function(idx, callback) {\n                    callback.apply(null, failureArgs);\n                });\n            }\n\n            return this;\n        }\n    });\n};\n", "/* globals qq */\n/**\n * Placeholder for a Blob that will be generated on-demand.\n *\n * @param referenceBlob Parent of the generated blob\n * @param onCreate Function to invoke when the blob must be created.  Must be promissory.\n * @constructor\n */\nqq.BlobProxy = function(referenceBlob, onCreate) {\n    \"use strict\";\n\n    qq.extend(this, {\n        referenceBlob: referenceBlob,\n\n        create: function() {\n            return onCreate(referenceBlob);\n        }\n    });\n};\n", "/*globals qq*/\n\n/**\n * This module represents an upload or \"Select File(s)\" button.  It's job is to embed an opaque `<input type=\"file\">`\n * element as a child of a provided \"container\" element.  This \"container\" element (`options.element`) is used to provide\n * a custom style for the `<input type=\"file\">` element.  The ability to change the style of the container element is also\n * provided here by adding CSS classes to the container on hover/focus.\n *\n * TODO Eliminate the mouseover and mouseout event handlers since the :hover CSS pseudo-class should now be\n * available on all supported browsers.\n *\n * @param o Options to override the default values\n */\nqq.UploadButton = function(o) {\n    \"use strict\";\n\n    var self = this,\n\n        disposeSupport = new qq.DisposeSupport(),\n\n        options = {\n            // Corresponds to the `accept` attribute on the associated `<input type=\"file\">`\n            acceptFiles: null,\n\n            // \"Container\" element\n            element: null,\n\n            focusClass: \"qq-upload-button-focus\",\n\n            // A true value allows folders to be selected, if supported by the UA\n            folders: false,\n\n            // **This option will be removed** in the future as the :hover CSS pseudo-class is available on all supported browsers\n            hoverClass: \"qq-upload-button-hover\",\n\n            ios8BrowserCrashWorkaround: false,\n\n            // If true adds `multiple` attribute to `<input type=\"file\">`\n            multiple: false,\n\n            // `name` attribute of `<input type=\"file\">`\n            name: \"qqfile\",\n\n            // Called when the browser invokes the onchange handler on the `<input type=\"file\">`\n            onChange: function(input) {},\n\n            title: null\n        },\n        input, buttonId;\n\n    // Overrides any of the default option values with any option values passed in during construction.\n    qq.extend(options, o);\n\n    buttonId = qq.getUniqueId();\n\n    // Embed an opaque `<input type=\"file\">` element as a child of `options.element`.\n    function createInput() {\n        var input = document.createElement(\"input\");\n\n        input.setAttribute(qq.UploadButton.BUTTON_ID_ATTR_NAME, buttonId);\n        input.setAttribute(\"title\", options.title);\n\n        self.setMultiple(options.multiple, input);\n\n        if (options.folders && qq.supportedFeatures.folderSelection) {\n            // selecting directories is only possible in Chrome now, via a vendor-specific prefixed attribute\n            input.setAttribute(\"webkitdirectory\", \"\");\n        }\n\n        if (options.acceptFiles) {\n            input.setAttribute(\"accept\", options.acceptFiles);\n        }\n\n        input.setAttribute(\"type\", \"file\");\n        input.setAttribute(\"name\", options.name);\n\n        qq(input).css({\n            position: \"absolute\",\n            // in Opera only 'browse' button\n            // is clickable and it is located at\n            // the right side of the input\n            right: 0,\n            top: 0,\n            fontFamily: \"Arial\",\n            // It's especially important to make this an arbitrarily large value\n            // to ensure the rendered input button in IE takes up the entire\n            // space of the container element.  Otherwise, the left side of the\n            // button will require a double-click to invoke the file chooser.\n            // In other browsers, this might cause other issues, so a large font-size\n            // is only used in IE.  There is a bug in IE8 where the opacity style is  ignored\n            // in some cases when the font-size is large.  So, this workaround is not applied\n            // to IE8.\n            fontSize: qq.ie() && !qq.ie8() ? \"3500px\" : \"118px\",\n            margin: 0,\n            padding: 0,\n            cursor: \"pointer\",\n            opacity: 0\n        });\n\n        // Setting the file input's height to 100% in IE7 causes\n        // most of the visible button to be unclickable.\n        !qq.ie7() && qq(input).css({height: \"100%\"});\n\n        options.element.appendChild(input);\n\n        disposeSupport.attach(input, \"change\", function() {\n            options.onChange(input);\n        });\n\n        // **These event handlers will be removed** in the future as the :hover CSS pseudo-class is available on all supported browsers\n        disposeSupport.attach(input, \"mouseover\", function() {\n            qq(options.element).addClass(options.hoverClass);\n        });\n        disposeSupport.attach(input, \"mouseout\", function() {\n            qq(options.element).removeClass(options.hoverClass);\n        });\n\n        disposeSupport.attach(input, \"focus\", function() {\n            qq(options.element).addClass(options.focusClass);\n        });\n        disposeSupport.attach(input, \"blur\", function() {\n            qq(options.element).removeClass(options.focusClass);\n        });\n\n        return input;\n    }\n\n    // Make button suitable container for input\n    qq(options.element).css({\n        position: \"relative\",\n        overflow: \"hidden\",\n        // Make sure browse button is in the right side in Internet Explorer\n        direction: \"ltr\"\n    });\n\n    // Exposed API\n    qq.extend(this, {\n        getInput: function() {\n            return input;\n        },\n\n        getButtonId: function() {\n            return buttonId;\n        },\n\n        setMultiple: function(isMultiple, optInput) {\n            var input = optInput || this.getInput();\n\n            // Temporary workaround for bug in in iOS8 UIWebView that causes the browser to crash\n            // before the file chooser appears if the file input doesn't contain a multiple attribute.\n            // See #1283.\n            if (options.ios8BrowserCrashWorkaround && qq.ios8() && (qq.iosChrome() || qq.iosSafariWebView())) {\n                input.setAttribute(\"multiple\", \"\");\n            }\n\n            else {\n                if (isMultiple) {\n                    input.setAttribute(\"multiple\", \"\");\n                }\n                else {\n                    input.removeAttribute(\"multiple\");\n                }\n            }\n        },\n\n        setAcceptFiles: function(acceptFiles) {\n            if (acceptFiles !== options.acceptFiles) {\n                input.setAttribute(\"accept\", acceptFiles);\n            }\n        },\n\n        reset: function() {\n            if (input.parentNode) {\n                qq(input).remove();\n            }\n\n            qq(options.element).removeClass(options.focusClass);\n            input = null;\n            input = createInput();\n        }\n    });\n\n    input = createInput();\n};\n\nqq.UploadButton.BUTTON_ID_ATTR_NAME = \"qq-button-id\";\n", "/*globals qq */\nqq.UploadData = function(uploaderProxy) {\n    \"use strict\";\n\n    var data = [],\n        byUuid = {},\n        byStatus = {},\n        byProxyGroupId = {},\n        byBatchId = {};\n\n    function getDataByIds(idOrIds) {\n        if (qq.isArray(idOrIds)) {\n            var entries = [];\n\n            qq.each(idOrIds, function(idx, id) {\n                entries.push(data[id]);\n            });\n\n            return entries;\n        }\n\n        return data[idOrIds];\n    }\n\n    function getDataByUuids(uuids) {\n        if (qq.isArray(uuids)) {\n            var entries = [];\n\n            qq.each(uuids, function(idx, uuid) {\n                entries.push(data[byUuid[uuid]]);\n            });\n\n            return entries;\n        }\n\n        return data[byUuid[uuids]];\n    }\n\n    function getDataByStatus(status) {\n        var statusResults = [],\n            statuses = [].concat(status);\n\n        qq.each(statuses, function(index, statusEnum) {\n            var statusResultIndexes = byStatus[statusEnum];\n\n            if (statusResultIndexes !== undefined) {\n                qq.each(statusResultIndexes, function(i, dataIndex) {\n                    statusResults.push(data[dataIndex]);\n                });\n            }\n        });\n\n        return statusResults;\n    }\n\n    qq.extend(this, {\n        /**\n         * Adds a new file to the data cache for tracking purposes.\n         *\n         * @param spec Data that describes this file.  Possible properties are:\n         *\n         * - uuid: Initial UUID for this file.\n         * - name: Initial name of this file.\n         * - size: Size of this file, omit if this cannot be determined\n         * - status: Initial `qq.status` for this file.  Omit for `qq.status.SUBMITTING`.\n         * - batchId: ID of the batch this file belongs to\n         * - proxyGroupId: ID of the proxy group associated with this file\n         *\n         * @returns {number} Internal ID for this file.\n         */\n        addFile: function(spec) {\n            var status = spec.status || qq.status.SUBMITTING,\n                id = data.push({\n                    name: spec.name,\n                    originalName: spec.name,\n                    uuid: spec.uuid,\n                    size: spec.size == null ? -1 : spec.size,\n                    status: status\n                }) - 1;\n\n            if (spec.batchId) {\n                data[id].batchId = spec.batchId;\n\n                if (byBatchId[spec.batchId] === undefined) {\n                    byBatchId[spec.batchId] = [];\n                }\n                byBatchId[spec.batchId].push(id);\n            }\n\n            if (spec.proxyGroupId) {\n                data[id].proxyGroupId = spec.proxyGroupId;\n\n                if (byProxyGroupId[spec.proxyGroupId] === undefined) {\n                    byProxyGroupId[spec.proxyGroupId] = [];\n                }\n                byProxyGroupId[spec.proxyGroupId].push(id);\n            }\n\n            data[id].id = id;\n            byUuid[spec.uuid] = id;\n\n            if (byStatus[status] === undefined) {\n                byStatus[status] = [];\n            }\n            byStatus[status].push(id);\n\n            uploaderProxy.onStatusChange(id, null, status);\n\n            return id;\n        },\n\n        retrieve: function(optionalFilter) {\n            if (qq.isObject(optionalFilter) && data.length)  {\n                if (optionalFilter.id !== undefined) {\n                    return getDataByIds(optionalFilter.id);\n                }\n\n                else if (optionalFilter.uuid !== undefined) {\n                    return getDataByUuids(optionalFilter.uuid);\n                }\n\n                else if (optionalFilter.status) {\n                    return getDataByStatus(optionalFilter.status);\n                }\n            }\n            else {\n                return qq.extend([], data, true);\n            }\n        },\n\n        reset: function() {\n            data = [];\n            byUuid = {};\n            byStatus = {};\n            byBatchId = {};\n        },\n\n        setStatus: function(id, newStatus) {\n            var oldStatus = data[id].status,\n                byStatusOldStatusIndex = qq.indexOf(byStatus[oldStatus], id);\n\n            byStatus[oldStatus].splice(byStatusOldStatusIndex, 1);\n\n            data[id].status = newStatus;\n\n            if (byStatus[newStatus] === undefined) {\n                byStatus[newStatus] = [];\n            }\n            byStatus[newStatus].push(id);\n\n            uploaderProxy.onStatusChange(id, oldStatus, newStatus);\n        },\n\n        uuidChanged: function(id, newUuid) {\n            var oldUuid = data[id].uuid;\n\n            data[id].uuid = newUuid;\n            byUuid[newUuid] = id;\n            delete byUuid[oldUuid];\n        },\n\n        updateName: function(id, newName) {\n            data[id].name = newName;\n        },\n\n        updateSize: function(id, newSize) {\n            data[id].size = newSize;\n        },\n\n        // Only applicable if this file has a parent that we may want to reference later.\n        setParentId: function(targetId, parentId) {\n            data[targetId].parentId = parentId;\n        },\n\n        getIdsInProxyGroup: function(id) {\n            var proxyGroupId = data[id].proxyGroupId;\n\n            if (proxyGroupId) {\n                return byProxyGroupId[proxyGroupId];\n            }\n            return [];\n        },\n\n        getIdsInBatch: function(id) {\n            var batchId = data[id].batchId;\n\n            return byBatchId[batchId];\n        }\n    });\n};\n\nqq.status = {\n    SUBMITTING: \"submitting\",\n    SUBMITTED: \"submitted\",\n    REJECTED: \"rejected\",\n    QUEUED: \"queued\",\n    CANCELED: \"canceled\",\n    PAUSED: \"paused\",\n    UPLOADING: \"uploading\",\n    UPLOAD_RETRYING: \"retrying upload\",\n    UPLOAD_SUCCESSFUL: \"upload successful\",\n    UPLOAD_FAILED: \"upload failed\",\n    DELETE_FAILED: \"delete failed\",\n    DELETING: \"deleting\",\n    DELETED: \"deleted\"\n};\n", "/*globals qq*/\n/**\n * Defines the public API for FineUploaderBasic mode.\n */\n(function() {\n    \"use strict\";\n\n    qq.basePublicApi = {\n        // DEPRECATED - TODO REMOVE IN NEXT MAJOR RELEASE (replaced by addFiles)\n        addBlobs: function(blobDataOrArray, params, endpoint) {\n            this.addFiles(blobDataOrArray, params, endpoint);\n        },\n\n        addInitialFiles: function(cannedFileList) {\n            var self = this;\n\n            qq.each(cannedFileList, function(index, cannedFile) {\n                self._addCannedFile(cannedFile);\n            });\n        },\n\n        addFiles: function(data, params, endpoint) {\n            this._maybeHandleIos8SafariWorkaround();\n\n            var batchId = this._storedIds.length === 0 ? qq.getUniqueId() : this._currentBatchId,\n\n                processBlob = qq.bind(function(blob) {\n                    this._handleNewFile({\n                        blob: blob,\n                        name: this._options.blobs.defaultName\n                    }, batchId, verifiedFiles);\n                }, this),\n\n                processBlobData = qq.bind(function(blobData) {\n                    this._handleNewFile(blobData, batchId, verifiedFiles);\n                }, this),\n\n                processCanvas = qq.bind(function(canvas) {\n                    var blob = qq.canvasToBlob(canvas);\n\n                    this._handleNewFile({\n                        blob: blob,\n                        name: this._options.blobs.defaultName + \".png\"\n                    }, batchId, verifiedFiles);\n                }, this),\n\n                processCanvasData = qq.bind(function(canvasData) {\n                    var normalizedQuality = canvasData.quality && canvasData.quality / 100,\n                        blob = qq.canvasToBlob(canvasData.canvas, canvasData.type, normalizedQuality);\n\n                    this._handleNewFile({\n                        blob: blob,\n                        name: canvasData.name\n                    }, batchId, verifiedFiles);\n                }, this),\n\n                processFileOrInput = qq.bind(function(fileOrInput) {\n                    if (qq.isInput(fileOrInput) && qq.supportedFeatures.ajaxUploading) {\n                        var files = Array.prototype.slice.call(fileOrInput.files),\n                            self = this;\n\n                        qq.each(files, function(idx, file) {\n                            self._handleNewFile(file, batchId, verifiedFiles);\n                        });\n                    }\n                    else {\n                        this._handleNewFile(fileOrInput, batchId, verifiedFiles);\n                    }\n                }, this),\n\n                normalizeData = function() {\n                    if (qq.isFileList(data)) {\n                        data = Array.prototype.slice.call(data);\n                    }\n                    data = [].concat(data);\n                },\n\n                self = this,\n                verifiedFiles = [];\n\n            this._currentBatchId = batchId;\n\n            if (data) {\n                normalizeData();\n\n                qq.each(data, function(idx, fileContainer) {\n                    if (qq.isFileOrInput(fileContainer)) {\n                        processFileOrInput(fileContainer);\n                    }\n                    else if (qq.isBlob(fileContainer)) {\n                        processBlob(fileContainer);\n                    }\n                    else if (qq.isObject(fileContainer)) {\n                        if (fileContainer.blob && fileContainer.name) {\n                            processBlobData(fileContainer);\n                        }\n                        else if (fileContainer.canvas && fileContainer.name) {\n                            processCanvasData(fileContainer);\n                        }\n                    }\n                    else if (fileContainer.tagName && fileContainer.tagName.toLowerCase() === \"canvas\") {\n                        processCanvas(fileContainer);\n                    }\n                    else {\n                        self.log(fileContainer + \" is not a valid file container!  Ignoring!\", \"warn\");\n                    }\n                });\n\n                this.log(\"Received \" + verifiedFiles.length + \" files.\");\n                this._prepareItemsForUpload(verifiedFiles, params, endpoint);\n            }\n        },\n\n        cancel: function(id) {\n            this._handler.cancel(id);\n        },\n\n        cancelAll: function() {\n            var storedIdsCopy = [],\n                self = this;\n\n            qq.extend(storedIdsCopy, this._storedIds);\n            qq.each(storedIdsCopy, function(idx, storedFileId) {\n                self.cancel(storedFileId);\n            });\n\n            this._handler.cancelAll();\n        },\n\n        clearStoredFiles: function() {\n            this._storedIds = [];\n        },\n\n        continueUpload: function(id) {\n            var uploadData = this._uploadData.retrieve({id: id});\n\n            if (!qq.supportedFeatures.pause || !this._options.chunking.enabled) {\n                return false;\n            }\n\n            if (uploadData.status === qq.status.PAUSED) {\n                this.log(qq.format(\"Paused file ID {} ({}) will be continued.  Not paused.\", id, this.getName(id)));\n                this._uploadFile(id);\n                return true;\n            }\n            else {\n                this.log(qq.format(\"Ignoring continue for file ID {} ({}).  Not paused.\", id, this.getName(id)), \"error\");\n            }\n\n            return false;\n        },\n\n        deleteFile: function(id) {\n            return this._onSubmitDelete(id);\n        },\n\n        // TODO document?\n        doesExist: function(fileOrBlobId) {\n            return this._handler.isValid(fileOrBlobId);\n        },\n\n        // Generate a variable size thumbnail on an img or canvas,\n        // returning a promise that is fulfilled when the attempt completes.\n        // Thumbnail can either be based off of a URL for an image returned\n        // by the server in the upload response, or the associated `Blob`.\n        drawThumbnail: function(fileId, imgOrCanvas, maxSize, fromServer, customResizeFunction) {\n            var promiseToReturn = new qq.Promise(),\n                fileOrUrl, options;\n\n            if (this._imageGenerator) {\n                fileOrUrl = this._thumbnailUrls[fileId];\n                options = {\n                    customResizeFunction: customResizeFunction,\n                    maxSize: maxSize > 0 ? maxSize : null,\n                    scale: maxSize > 0\n                };\n\n                // If client-side preview generation is possible\n                // and we are not specifically looking for the image URl returned by the server...\n                if (!fromServer && qq.supportedFeatures.imagePreviews) {\n                    fileOrUrl = this.getFile(fileId);\n                }\n\n                /* jshint eqeqeq:false,eqnull:true */\n                if (fileOrUrl == null) {\n                    promiseToReturn.failure({container: imgOrCanvas, error: \"File or URL not found.\"});\n                }\n                else {\n                    this._imageGenerator.generate(fileOrUrl, imgOrCanvas, options).then(\n                        function success(modifiedContainer) {\n                            promiseToReturn.success(modifiedContainer);\n                        },\n\n                        function failure(container, reason) {\n                            promiseToReturn.failure({container: container, error: reason || \"Problem generating thumbnail\"});\n                        }\n                    );\n                }\n            }\n            else {\n                promiseToReturn.failure({container: imgOrCanvas, error: \"Missing image generator module\"});\n            }\n\n            return promiseToReturn;\n        },\n\n        getButton: function(fileId) {\n            return this._getButton(this._buttonIdsForFileIds[fileId]);\n        },\n\n        getEndpoint: function(fileId) {\n            return this._endpointStore.get(fileId);\n        },\n\n        getFile: function(fileOrBlobId) {\n            return this._handler.getFile(fileOrBlobId) || null;\n        },\n\n        getInProgress: function() {\n            return this._uploadData.retrieve({\n                status: [\n                    qq.status.UPLOADING,\n                    qq.status.UPLOAD_RETRYING,\n                    qq.status.QUEUED\n                ]\n            }).length;\n        },\n\n        getName: function(id) {\n            return this._uploadData.retrieve({id: id}).name;\n        },\n\n        // Parent ID for a specific file, or null if this is the parent, or if it has no parent.\n        getParentId: function(id) {\n            var uploadDataEntry = this.getUploads({id: id}),\n                parentId = null;\n\n            if (uploadDataEntry) {\n                if (uploadDataEntry.parentId !== undefined) {\n                    parentId = uploadDataEntry.parentId;\n                }\n            }\n\n            return parentId;\n        },\n\n        getResumableFilesData: function() {\n            return this._handler.getResumableFilesData();\n        },\n\n        getSize: function(id) {\n            return this._uploadData.retrieve({id: id}).size;\n        },\n\n        getNetUploads: function() {\n            return this._netUploaded;\n        },\n\n        getRemainingAllowedItems: function() {\n            var allowedItems = this._currentItemLimit;\n\n            if (allowedItems > 0) {\n                return allowedItems - this._netUploadedOrQueued;\n            }\n\n            return null;\n        },\n\n        getUploads: function(optionalFilter) {\n            return this._uploadData.retrieve(optionalFilter);\n        },\n\n        getUuid: function(id) {\n            return this._uploadData.retrieve({id: id}).uuid;\n        },\n\n        log: function(str, level) {\n            if (this._options.debug && (!level || level === \"info\")) {\n                qq.log(\"[Fine Uploader \" + qq.version + \"] \" + str);\n            }\n            else if (level && level !== \"info\") {\n                qq.log(\"[Fine Uploader \" + qq.version + \"] \" + str, level);\n\n            }\n        },\n\n        pauseUpload: function(id) {\n            var uploadData = this._uploadData.retrieve({id: id});\n\n            if (!qq.supportedFeatures.pause || !this._options.chunking.enabled) {\n                return false;\n            }\n\n            // Pause only really makes sense if the file is uploading or retrying\n            if (qq.indexOf([qq.status.UPLOADING, qq.status.UPLOAD_RETRYING], uploadData.status) >= 0) {\n                if (this._handler.pause(id)) {\n                    this._uploadData.setStatus(id, qq.status.PAUSED);\n                    return true;\n                }\n                else {\n                    this.log(qq.format(\"Unable to pause file ID {} ({}).\", id, this.getName(id)), \"error\");\n                }\n            }\n            else {\n                this.log(qq.format(\"Ignoring pause for file ID {} ({}).  Not in progress.\", id, this.getName(id)), \"error\");\n            }\n\n            return false;\n        },\n\n        reset: function() {\n            this.log(\"Resetting uploader...\");\n\n            this._handler.reset();\n            this._storedIds = [];\n            this._autoRetries = [];\n            this._retryTimeouts = [];\n            this._preventRetries = [];\n            this._thumbnailUrls = [];\n\n            qq.each(this._buttons, function(idx, button) {\n                button.reset();\n            });\n\n            this._paramsStore.reset();\n            this._endpointStore.reset();\n            this._netUploadedOrQueued = 0;\n            this._netUploaded = 0;\n            this._uploadData.reset();\n            this._buttonIdsForFileIds = [];\n\n            this._pasteHandler && this._pasteHandler.reset();\n            this._options.session.refreshOnReset && this._refreshSessionData();\n\n            this._succeededSinceLastAllComplete = [];\n            this._failedSinceLastAllComplete = [];\n\n            this._totalProgress && this._totalProgress.reset();\n        },\n\n        retry: function(id) {\n            return this._manualRetry(id);\n        },\n\n        scaleImage: function(id, specs) {\n            var self = this;\n\n            return qq.Scaler.prototype.scaleImage(id, specs, {\n                log: qq.bind(self.log, self),\n                getFile: qq.bind(self.getFile, self),\n                uploadData: self._uploadData\n            });\n        },\n\n        setCustomHeaders: function(headers, id) {\n            this._customHeadersStore.set(headers, id);\n        },\n\n        setDeleteFileCustomHeaders: function(headers, id) {\n            this._deleteFileCustomHeadersStore.set(headers, id);\n        },\n\n        setDeleteFileEndpoint: function(endpoint, id) {\n            this._deleteFileEndpointStore.set(endpoint, id);\n        },\n\n        setDeleteFileParams: function(params, id) {\n            this._deleteFileParamsStore.set(params, id);\n        },\n\n        // Re-sets the default endpoint, an endpoint for a specific file, or an endpoint for a specific button\n        setEndpoint: function(endpoint, id) {\n            this._endpointStore.set(endpoint, id);\n        },\n\n        setForm: function(elementOrId) {\n            this._updateFormSupportAndParams(elementOrId);\n        },\n\n        setItemLimit: function(newItemLimit) {\n            this._currentItemLimit = newItemLimit;\n        },\n\n        setName: function(id, newName) {\n            this._uploadData.updateName(id, newName);\n        },\n\n        setParams: function(params, id) {\n            this._paramsStore.set(params, id);\n        },\n\n        setUuid: function(id, newUuid) {\n            return this._uploadData.uuidChanged(id, newUuid);\n        },\n\n        uploadStoredFiles: function() {\n            if (this._storedIds.length === 0) {\n                this._itemError(\"noFilesError\");\n            }\n            else {\n                this._uploadStoredFiles();\n            }\n        }\n    };\n\n    /**\n     * Defines the private (internal) API for FineUploaderBasic mode.\n     */\n    qq.basePrivateApi = {\n        // Updates internal state with a file record (not backed by a live file).  Returns the assigned ID.\n        _addCannedFile: function(sessionData) {\n            var id = this._uploadData.addFile({\n                uuid: sessionData.uuid,\n                name: sessionData.name,\n                size: sessionData.size,\n                status: qq.status.UPLOAD_SUCCESSFUL\n            });\n\n            sessionData.deleteFileEndpoint && this.setDeleteFileEndpoint(sessionData.deleteFileEndpoint, id);\n            sessionData.deleteFileParams && this.setDeleteFileParams(sessionData.deleteFileParams, id);\n\n            if (sessionData.thumbnailUrl) {\n                this._thumbnailUrls[id] = sessionData.thumbnailUrl;\n            }\n\n            this._netUploaded++;\n            this._netUploadedOrQueued++;\n\n            return id;\n        },\n\n        _annotateWithButtonId: function(file, associatedInput) {\n            if (qq.isFile(file)) {\n                file.qqButtonId = this._getButtonId(associatedInput);\n            }\n        },\n\n        _batchError: function(message) {\n            this._options.callbacks.onError(null, null, message, undefined);\n        },\n\n        _createDeleteHandler: function() {\n            var self = this;\n\n            return new qq.DeleteFileAjaxRequester({\n                method: this._options.deleteFile.method.toUpperCase(),\n                maxConnections: this._options.maxConnections,\n                uuidParamName: this._options.request.uuidName,\n                customHeaders: this._deleteFileCustomHeadersStore,\n                paramsStore: this._deleteFileParamsStore,\n                endpointStore: this._deleteFileEndpointStore,\n                cors: this._options.cors,\n                log: qq.bind(self.log, self),\n                onDelete: function(id) {\n                    self._onDelete(id);\n                    self._options.callbacks.onDelete(id);\n                },\n                onDeleteComplete: function(id, xhrOrXdr, isError) {\n                    self._onDeleteComplete(id, xhrOrXdr, isError);\n                    self._options.callbacks.onDeleteComplete(id, xhrOrXdr, isError);\n                }\n\n            });\n        },\n\n        _createPasteHandler: function() {\n            var self = this;\n\n            return new qq.PasteSupport({\n                targetElement: this._options.paste.targetElement,\n                callbacks: {\n                    log: qq.bind(self.log, self),\n                    pasteReceived: function(blob) {\n                        self._handleCheckedCallback({\n                            name: \"onPasteReceived\",\n                            callback: qq.bind(self._options.callbacks.onPasteReceived, self, blob),\n                            onSuccess: qq.bind(self._handlePasteSuccess, self, blob),\n                            identifier: \"pasted image\"\n                        });\n                    }\n                }\n            });\n        },\n\n        _createStore: function(initialValue, _readOnlyValues_) {\n            var store = {},\n                catchall = initialValue,\n                perIdReadOnlyValues = {},\n                readOnlyValues = _readOnlyValues_,\n                copy = function(orig) {\n                    if (qq.isObject(orig)) {\n                        return qq.extend({}, orig);\n                    }\n                    return orig;\n                },\n                getReadOnlyValues = function() {\n                    if (qq.isFunction(readOnlyValues)) {\n                        return readOnlyValues();\n                    }\n                    return readOnlyValues;\n                },\n                includeReadOnlyValues = function(id, existing) {\n                    if (readOnlyValues && qq.isObject(existing)) {\n                        qq.extend(existing, getReadOnlyValues());\n                    }\n\n                    if (perIdReadOnlyValues[id]) {\n                        qq.extend(existing, perIdReadOnlyValues[id]);\n                    }\n                };\n\n            return {\n                set: function(val, id) {\n                    /*jshint eqeqeq: true, eqnull: true*/\n                    if (id == null) {\n                        store = {};\n                        catchall = copy(val);\n                    }\n                    else {\n                        store[id] = copy(val);\n                    }\n                },\n\n                get: function(id) {\n                    var values;\n\n                    /*jshint eqeqeq: true, eqnull: true*/\n                    if (id != null && store[id]) {\n                        values = store[id];\n                    }\n                    else {\n                        values = copy(catchall);\n                    }\n\n                    includeReadOnlyValues(id, values);\n\n                    return copy(values);\n                },\n\n                addReadOnly: function(id, values) {\n                    // Only applicable to Object stores\n                    if (qq.isObject(store)) {\n                        // If null ID, apply readonly values to all files\n                        if (id === null) {\n                            if (qq.isFunction(values)) {\n                                readOnlyValues = values;\n                            }\n                            else {\n                                readOnlyValues = readOnlyValues || {};\n                                qq.extend(readOnlyValues, values);\n                            }\n                        }\n                        else {\n                            perIdReadOnlyValues[id] = perIdReadOnlyValues[id] || {};\n                            qq.extend(perIdReadOnlyValues[id], values);\n                        }\n                    }\n                },\n\n                remove: function(fileId) {\n                    return delete store[fileId];\n                },\n\n                reset: function() {\n                    store = {};\n                    perIdReadOnlyValues = {};\n                    catchall = initialValue;\n                }\n            };\n        },\n\n        _createUploadDataTracker: function() {\n            var self = this;\n\n            return new qq.UploadData({\n                getName: function(id) {\n                    return self.getName(id);\n                },\n                getUuid: function(id) {\n                    return self.getUuid(id);\n                },\n                getSize: function(id) {\n                    return self.getSize(id);\n                },\n                onStatusChange: function(id, oldStatus, newStatus) {\n                    self._onUploadStatusChange(id, oldStatus, newStatus);\n                    self._options.callbacks.onStatusChange(id, oldStatus, newStatus);\n                    self._maybeAllComplete(id, newStatus);\n\n                    if (self._totalProgress) {\n                        setTimeout(function() {\n                            self._totalProgress.onStatusChange(id, oldStatus, newStatus);\n                        }, 0);\n                    }\n                }\n            });\n        },\n\n        /**\n         * Generate a tracked upload button.\n         *\n         * @param spec Object containing a required `element` property\n         * along with optional `multiple`, `accept`, and `folders`.\n         * @returns {qq.UploadButton}\n         * @private\n         */\n        _createUploadButton: function(spec) {\n            var self = this,\n                acceptFiles = spec.accept || this._options.validation.acceptFiles,\n                allowedExtensions = spec.allowedExtensions || this._options.validation.allowedExtensions,\n                button;\n\n            function allowMultiple() {\n                if (qq.supportedFeatures.ajaxUploading) {\n                    // Workaround for bug in iOS7+ (see #1039)\n                    if (self._options.workarounds.iosEmptyVideos &&\n                        qq.ios() &&\n                        !qq.ios6() &&\n                        self._isAllowedExtension(allowedExtensions, \".mov\")) {\n\n                        return false;\n                    }\n\n                    if (spec.multiple === undefined) {\n                        return self._options.multiple;\n                    }\n\n                    return spec.multiple;\n                }\n\n                return false;\n            }\n\n            button = new qq.UploadButton({\n                acceptFiles: acceptFiles,\n                element: spec.element,\n                focusClass: this._options.classes.buttonFocus,\n                folders: spec.folders,\n                hoverClass: this._options.classes.buttonHover,\n                ios8BrowserCrashWorkaround: this._options.workarounds.ios8BrowserCrash,\n                multiple: allowMultiple(),\n                name: this._options.request.inputName,\n                onChange: function(input) {\n                    self._onInputChange(input);\n                },\n                title: spec.title == null ? this._options.text.fileInputTitle : spec.title\n            });\n\n            this._disposeSupport.addDisposer(function() {\n                button.dispose();\n            });\n\n            self._buttons.push(button);\n\n            return button;\n        },\n\n        _createUploadHandler: function(additionalOptions, namespace) {\n            var self = this,\n                lastOnProgress = {},\n                options = {\n                    debug: this._options.debug,\n                    maxConnections: this._options.maxConnections,\n                    cors: this._options.cors,\n                    paramsStore: this._paramsStore,\n                    endpointStore: this._endpointStore,\n                    chunking: this._options.chunking,\n                    resume: this._options.resume,\n                    blobs: this._options.blobs,\n                    log: qq.bind(self.log, self),\n                    preventRetryParam: this._options.retry.preventRetryResponseProperty,\n                    onProgress: function(id, name, loaded, total) {\n                        if (loaded < 0 || total < 0) {\n                            return;\n                        }\n\n                        if (lastOnProgress[id]) {\n                            if (lastOnProgress[id].loaded !== loaded || lastOnProgress[id].total !== total) {\n                                self._onProgress(id, name, loaded, total);\n                                self._options.callbacks.onProgress(id, name, loaded, total);\n                            }\n                        }\n                        else {\n                            self._onProgress(id, name, loaded, total);\n                            self._options.callbacks.onProgress(id, name, loaded, total);\n                        }\n\n                        lastOnProgress[id] = {loaded: loaded, total: total};\n\n                    },\n                    onComplete: function(id, name, result, xhr) {\n                        delete lastOnProgress[id];\n\n                        var status = self.getUploads({id: id}).status,\n                            retVal;\n\n                        // This is to deal with some observed cases where the XHR readyStateChange handler is\n                        // invoked by the browser multiple times for the same XHR instance with the same state\n                        // readyState value.  Higher level: don't invoke complete-related code if we've already\n                        // done this.\n                        if (status === qq.status.UPLOAD_SUCCESSFUL || status === qq.status.UPLOAD_FAILED) {\n                            return;\n                        }\n\n                        retVal = self._onComplete(id, name, result, xhr);\n\n                        // If the internal `_onComplete` handler returns a promise, don't invoke the `onComplete` callback\n                        // until the promise has been fulfilled.\n                        if (retVal instanceof  qq.Promise) {\n                            retVal.done(function() {\n                                self._options.callbacks.onComplete(id, name, result, xhr);\n                            });\n                        }\n                        else {\n                            self._options.callbacks.onComplete(id, name, result, xhr);\n                        }\n                    },\n                    onCancel: function(id, name, cancelFinalizationEffort) {\n                        var promise = new qq.Promise();\n\n                        self._handleCheckedCallback({\n                            name: \"onCancel\",\n                            callback: qq.bind(self._options.callbacks.onCancel, self, id, name),\n                            onFailure: promise.failure,\n                            onSuccess: function() {\n                                cancelFinalizationEffort.then(function() {\n                                    self._onCancel(id, name);\n                                });\n\n                                promise.success();\n                            },\n                            identifier: id\n                        });\n\n                        return promise;\n                    },\n                    onUploadPrep: qq.bind(this._onUploadPrep, this),\n                    onUpload: function(id, name) {\n                        self._onUpload(id, name);\n                        self._options.callbacks.onUpload(id, name);\n                    },\n                    onUploadChunk: function(id, name, chunkData) {\n                        self._onUploadChunk(id, chunkData);\n                        self._options.callbacks.onUploadChunk(id, name, chunkData);\n                    },\n                    onUploadChunkSuccess: function(id, chunkData, result, xhr) {\n                        self._options.callbacks.onUploadChunkSuccess.apply(self, arguments);\n                    },\n                    onResume: function(id, name, chunkData) {\n                        return self._options.callbacks.onResume(id, name, chunkData);\n                    },\n                    onAutoRetry: function(id, name, responseJSON, xhr) {\n                        return self._onAutoRetry.apply(self, arguments);\n                    },\n                    onUuidChanged: function(id, newUuid) {\n                        self.log(\"Server requested UUID change from '\" + self.getUuid(id) + \"' to '\" + newUuid + \"'\");\n                        self.setUuid(id, newUuid);\n                    },\n                    getName: qq.bind(self.getName, self),\n                    getUuid: qq.bind(self.getUuid, self),\n                    getSize: qq.bind(self.getSize, self),\n                    setSize: qq.bind(self._setSize, self),\n                    getDataByUuid: function(uuid) {\n                        return self.getUploads({uuid: uuid});\n                    },\n                    isQueued: function(id) {\n                        var status = self.getUploads({id: id}).status;\n                        return status === qq.status.QUEUED ||\n                            status === qq.status.SUBMITTED ||\n                            status === qq.status.UPLOAD_RETRYING ||\n                            status === qq.status.PAUSED;\n                    },\n                    getIdsInProxyGroup: self._uploadData.getIdsInProxyGroup,\n                    getIdsInBatch: self._uploadData.getIdsInBatch\n                };\n\n            qq.each(this._options.request, function(prop, val) {\n                options[prop] = val;\n            });\n\n            options.customHeaders = this._customHeadersStore;\n\n            if (additionalOptions) {\n                qq.each(additionalOptions, function(key, val) {\n                    options[key] = val;\n                });\n            }\n\n            return new qq.UploadHandlerController(options, namespace);\n        },\n\n        _fileOrBlobRejected: function(id) {\n            this._netUploadedOrQueued--;\n            this._uploadData.setStatus(id, qq.status.REJECTED);\n        },\n\n        _formatSize: function(bytes) {\n            var i = -1;\n            do {\n                bytes = bytes / 1000;\n                i++;\n            } while (bytes > 999);\n\n            return Math.max(bytes, 0.1).toFixed(1) + this._options.text.sizeSymbols[i];\n        },\n\n        // Creates an internal object that tracks various properties of each extra button,\n        // and then actually creates the extra button.\n        _generateExtraButtonSpecs: function() {\n            var self = this;\n\n            this._extraButtonSpecs = {};\n\n            qq.each(this._options.extraButtons, function(idx, extraButtonOptionEntry) {\n                var multiple = extraButtonOptionEntry.multiple,\n                    validation = qq.extend({}, self._options.validation, true),\n                    extraButtonSpec = qq.extend({}, extraButtonOptionEntry);\n\n                if (multiple === undefined) {\n                    multiple = self._options.multiple;\n                }\n\n                if (extraButtonSpec.validation) {\n                    qq.extend(validation, extraButtonOptionEntry.validation, true);\n                }\n\n                qq.extend(extraButtonSpec, {\n                    multiple: multiple,\n                    validation: validation\n                }, true);\n\n                self._initExtraButton(extraButtonSpec);\n            });\n        },\n\n        _getButton: function(buttonId) {\n            var extraButtonsSpec = this._extraButtonSpecs[buttonId];\n\n            if (extraButtonsSpec) {\n                return extraButtonsSpec.element;\n            }\n            else if (buttonId === this._defaultButtonId) {\n                return this._options.button;\n            }\n        },\n\n        /**\n         * Gets the internally used tracking ID for a button.\n         *\n         * @param buttonOrFileInputOrFile `File`, `<input type=\"file\">`, or a button container element\n         * @returns {*} The button's ID, or undefined if no ID is recoverable\n         * @private\n         */\n        _getButtonId: function(buttonOrFileInputOrFile) {\n            var inputs, fileInput,\n                fileBlobOrInput = buttonOrFileInputOrFile;\n\n            // We want the reference file/blob here if this is a proxy (a file that will be generated on-demand later)\n            if (fileBlobOrInput instanceof qq.BlobProxy) {\n                fileBlobOrInput = fileBlobOrInput.referenceBlob;\n            }\n\n            // If the item is a `Blob` it will never be associated with a button or drop zone.\n            if (fileBlobOrInput && !qq.isBlob(fileBlobOrInput)) {\n                if (qq.isFile(fileBlobOrInput)) {\n                    return fileBlobOrInput.qqButtonId;\n                }\n                else if (fileBlobOrInput.tagName.toLowerCase() === \"input\" &&\n                    fileBlobOrInput.type.toLowerCase() === \"file\") {\n\n                    return fileBlobOrInput.getAttribute(qq.UploadButton.BUTTON_ID_ATTR_NAME);\n                }\n\n                inputs = fileBlobOrInput.getElementsByTagName(\"input\");\n\n                qq.each(inputs, function(idx, input) {\n                    if (input.getAttribute(\"type\") === \"file\") {\n                        fileInput = input;\n                        return false;\n                    }\n                });\n\n                if (fileInput) {\n                    return fileInput.getAttribute(qq.UploadButton.BUTTON_ID_ATTR_NAME);\n                }\n            }\n        },\n\n        _getNotFinished: function() {\n            return this._uploadData.retrieve({\n                status: [\n                    qq.status.UPLOADING,\n                    qq.status.UPLOAD_RETRYING,\n                    qq.status.QUEUED,\n                    qq.status.SUBMITTING,\n                    qq.status.SUBMITTED,\n                    qq.status.PAUSED\n                ]\n            }).length;\n        },\n\n        // Get the validation options for this button.  Could be the default validation option\n        // or a specific one assigned to this particular button.\n        _getValidationBase: function(buttonId) {\n            var extraButtonSpec = this._extraButtonSpecs[buttonId];\n\n            return extraButtonSpec ? extraButtonSpec.validation : this._options.validation;\n        },\n\n        _getValidationDescriptor: function(fileWrapper) {\n            if (fileWrapper.file instanceof qq.BlobProxy) {\n                return {\n                    name: qq.getFilename(fileWrapper.file.referenceBlob),\n                    size: fileWrapper.file.referenceBlob.size\n                };\n            }\n\n            return {\n                name: this.getUploads({id: fileWrapper.id}).name,\n                size: this.getUploads({id: fileWrapper.id}).size\n            };\n        },\n\n        _getValidationDescriptors: function(fileWrappers) {\n            var self = this,\n                fileDescriptors = [];\n\n            qq.each(fileWrappers, function(idx, fileWrapper) {\n                fileDescriptors.push(self._getValidationDescriptor(fileWrapper));\n            });\n\n            return fileDescriptors;\n        },\n\n        // Allows camera access on either the default or an extra button for iOS devices.\n        _handleCameraAccess: function() {\n            if (this._options.camera.ios && qq.ios()) {\n                var acceptIosCamera = \"image/*;capture=camera\",\n                    button = this._options.camera.button,\n                    buttonId = button ? this._getButtonId(button) : this._defaultButtonId,\n                    optionRoot = this._options;\n\n                // If we are not targeting the default button, it is an \"extra\" button\n                if (buttonId && buttonId !== this._defaultButtonId) {\n                    optionRoot = this._extraButtonSpecs[buttonId];\n                }\n\n                // Camera access won't work in iOS if the `multiple` attribute is present on the file input\n                optionRoot.multiple = false;\n\n                // update the options\n                if (optionRoot.validation.acceptFiles === null) {\n                    optionRoot.validation.acceptFiles = acceptIosCamera;\n                }\n                else {\n                    optionRoot.validation.acceptFiles += \",\" + acceptIosCamera;\n                }\n\n                // update the already-created button\n                qq.each(this._buttons, function(idx, button) {\n                    if (button.getButtonId() === buttonId) {\n                        button.setMultiple(optionRoot.multiple);\n                        button.setAcceptFiles(optionRoot.acceptFiles);\n\n                        return false;\n                    }\n                });\n            }\n        },\n\n        _handleCheckedCallback: function(details) {\n            var self = this,\n                callbackRetVal = details.callback();\n\n            if (qq.isGenericPromise(callbackRetVal)) {\n                this.log(details.name + \" - waiting for \" + details.name + \" promise to be fulfilled for \" + details.identifier);\n                return callbackRetVal.then(\n                    function(successParam) {\n                        self.log(details.name + \" promise success for \" + details.identifier);\n                        details.onSuccess(successParam);\n                    },\n                    function() {\n                        if (details.onFailure) {\n                            self.log(details.name + \" promise failure for \" + details.identifier);\n                            details.onFailure();\n                        }\n                        else {\n                            self.log(details.name + \" promise failure for \" + details.identifier);\n                        }\n                    });\n            }\n\n            if (callbackRetVal !== false) {\n                details.onSuccess(callbackRetVal);\n            }\n            else {\n                if (details.onFailure) {\n                    this.log(details.name + \" - return value was 'false' for \" + details.identifier + \".  Invoking failure callback.\");\n                    details.onFailure();\n                }\n                else {\n                    this.log(details.name + \" - return value was 'false' for \" + details.identifier + \".  Will not proceed.\");\n                }\n            }\n\n            return callbackRetVal;\n        },\n\n        // Updates internal state when a new file has been received, and adds it along with its ID to a passed array.\n        _handleNewFile: function(file, batchId, newFileWrapperList) {\n            var self = this,\n                uuid = qq.getUniqueId(),\n                size = -1,\n                name = qq.getFilename(file),\n                actualFile = file.blob || file,\n                handler = this._customNewFileHandler ?\n                    this._customNewFileHandler :\n                    qq.bind(self._handleNewFileGeneric, self);\n\n            if (!qq.isInput(actualFile) && actualFile.size >= 0) {\n                size = actualFile.size;\n            }\n\n            handler(actualFile, name, uuid, size, newFileWrapperList, batchId, this._options.request.uuidName, {\n                uploadData: self._uploadData,\n                paramsStore: self._paramsStore,\n                addFileToHandler: function(id, file) {\n                    self._handler.add(id, file);\n                    self._netUploadedOrQueued++;\n                    self._trackButton(id);\n                }\n            });\n        },\n\n        _handleNewFileGeneric: function(file, name, uuid, size, fileList, batchId) {\n            var id = this._uploadData.addFile({uuid: uuid, name: name, size: size, batchId: batchId});\n\n            this._handler.add(id, file);\n            this._trackButton(id);\n\n            this._netUploadedOrQueued++;\n\n            fileList.push({id: id, file: file});\n        },\n\n        _handlePasteSuccess: function(blob, extSuppliedName) {\n            var extension = blob.type.split(\"/\")[1],\n                name = extSuppliedName;\n\n            /*jshint eqeqeq: true, eqnull: true*/\n            if (name == null) {\n                name = this._options.paste.defaultName;\n            }\n\n            name += \".\" + extension;\n\n            this.addFiles({\n                name: name,\n                blob: blob\n            });\n        },\n\n        // Creates an extra button element\n        _initExtraButton: function(spec) {\n            var button = this._createUploadButton({\n                accept: spec.validation.acceptFiles,\n                allowedExtensions: spec.validation.allowedExtensions,\n                element: spec.element,\n                folders: spec.folders,\n                multiple: spec.multiple,\n                title: spec.fileInputTitle\n            });\n\n            this._extraButtonSpecs[button.getButtonId()] = spec;\n        },\n\n        _initFormSupportAndParams: function() {\n            this._formSupport = qq.FormSupport && new qq.FormSupport(\n                this._options.form, qq.bind(this.uploadStoredFiles, this), qq.bind(this.log, this)\n            );\n\n            if (this._formSupport && this._formSupport.attachedToForm) {\n                this._paramsStore = this._createStore(\n                    this._options.request.params,  this._formSupport.getFormInputsAsObject\n                );\n\n                this._options.autoUpload = this._formSupport.newAutoUpload;\n                if (this._formSupport.newEndpoint) {\n                    this._options.request.endpoint = this._formSupport.newEndpoint;\n                }\n            }\n            else {\n                this._paramsStore = this._createStore(this._options.request.params);\n            }\n        },\n\n        _isDeletePossible: function() {\n            if (!qq.DeleteFileAjaxRequester || !this._options.deleteFile.enabled) {\n                return false;\n            }\n\n            if (this._options.cors.expected) {\n                if (qq.supportedFeatures.deleteFileCorsXhr) {\n                    return true;\n                }\n\n                if (qq.supportedFeatures.deleteFileCorsXdr && this._options.cors.allowXdr) {\n                    return true;\n                }\n\n                return false;\n            }\n\n            return true;\n        },\n\n        _isAllowedExtension: function(allowed, fileName) {\n            var valid = false;\n\n            if (!allowed.length) {\n                return true;\n            }\n\n            qq.each(allowed, function(idx, allowedExt) {\n                /**\n                 * If an argument is not a string, ignore it.  Added when a possible issue with MooTools hijacking the\n                 * `allowedExtensions` array was discovered.  See case #735 in the issue tracker for more details.\n                 */\n                if (qq.isString(allowedExt)) {\n                    /*jshint eqeqeq: true, eqnull: true*/\n                    var extRegex = new RegExp(\"\\\\.\" + allowedExt + \"$\", \"i\");\n\n                    if (fileName.match(extRegex) != null) {\n                        valid = true;\n                        return false;\n                    }\n                }\n            });\n\n            return valid;\n        },\n\n        /**\n         * Constructs and returns a message that describes an item/file error.  Also calls `onError` callback.\n         *\n         * @param code REQUIRED - a code that corresponds to a stock message describing this type of error\n         * @param maybeNameOrNames names of the items that have failed, if applicable\n         * @param item `File`, `Blob`, or `<input type=\"file\">`\n         * @private\n         */\n        _itemError: function(code, maybeNameOrNames, item) {\n            var message = this._options.messages[code],\n                allowedExtensions = [],\n                names = [].concat(maybeNameOrNames),\n                name = names[0],\n                buttonId = this._getButtonId(item),\n                validationBase = this._getValidationBase(buttonId),\n                extensionsForMessage, placeholderMatch;\n\n            function r(name, replacement) { message = message.replace(name, replacement); }\n\n            qq.each(validationBase.allowedExtensions, function(idx, allowedExtension) {\n                /**\n                 * If an argument is not a string, ignore it.  Added when a possible issue with MooTools hijacking the\n                 * `allowedExtensions` array was discovered.  See case #735 in the issue tracker for more details.\n                 */\n                if (qq.isString(allowedExtension)) {\n                    allowedExtensions.push(allowedExtension);\n                }\n            });\n\n            extensionsForMessage = allowedExtensions.join(\", \").toLowerCase();\n\n            r(\"{file}\", this._options.formatFileName(name));\n            r(\"{extensions}\", extensionsForMessage);\n            r(\"{sizeLimit}\", this._formatSize(validationBase.sizeLimit));\n            r(\"{minSizeLimit}\", this._formatSize(validationBase.minSizeLimit));\n\n            placeholderMatch = message.match(/(\\{\\w+\\})/g);\n            if (placeholderMatch !== null) {\n                qq.each(placeholderMatch, function(idx, placeholder) {\n                    r(placeholder, names[idx]);\n                });\n            }\n\n            this._options.callbacks.onError(null, name, message, undefined);\n\n            return message;\n        },\n\n        /**\n         * Conditionally orders a manual retry of a failed upload.\n         *\n         * @param id File ID of the failed upload\n         * @param callback Optional callback to invoke if a retry is prudent.\n         * In lieu of asking the upload handler to retry.\n         * @returns {boolean} true if a manual retry will occur\n         * @private\n         */\n        _manualRetry: function(id, callback) {\n            if (this._onBeforeManualRetry(id)) {\n                this._netUploadedOrQueued++;\n                this._uploadData.setStatus(id, qq.status.UPLOAD_RETRYING);\n\n                if (callback) {\n                    callback(id);\n                }\n                else {\n                    this._handler.retry(id);\n                }\n\n                return true;\n            }\n        },\n\n        _maybeAllComplete: function(id, status) {\n            var self = this,\n                notFinished = this._getNotFinished();\n\n            if (status === qq.status.UPLOAD_SUCCESSFUL) {\n                this._succeededSinceLastAllComplete.push(id);\n            }\n            else if (status === qq.status.UPLOAD_FAILED) {\n                this._failedSinceLastAllComplete.push(id);\n            }\n\n            if (notFinished === 0 &&\n                (this._succeededSinceLastAllComplete.length || this._failedSinceLastAllComplete.length)) {\n                // Attempt to ensure onAllComplete is not invoked before other callbacks, such as onCancel & onComplete\n                setTimeout(function() {\n                    self._onAllComplete(self._succeededSinceLastAllComplete, self._failedSinceLastAllComplete);\n                }, 0);\n            }\n        },\n\n        _maybeHandleIos8SafariWorkaround: function() {\n            var self = this;\n\n            if (this._options.workarounds.ios8SafariUploads && qq.ios800() && qq.iosSafari()) {\n                setTimeout(function() {\n                    window.alert(self._options.messages.unsupportedBrowserIos8Safari);\n                }, 0);\n                throw new qq.Error(this._options.messages.unsupportedBrowserIos8Safari);\n            }\n        },\n\n        _maybeParseAndSendUploadError: function(id, name, response, xhr) {\n            // Assuming no one will actually set the response code to something other than 200\n            // and still set 'success' to true...\n            if (!response.success) {\n                if (xhr && xhr.status !== 200 && !response.error) {\n                    this._options.callbacks.onError(id, name, \"XHR returned response code \" + xhr.status, xhr);\n                }\n                else {\n                    var errorReason = response.error ? response.error : this._options.text.defaultResponseError;\n                    this._options.callbacks.onError(id, name, errorReason, xhr);\n                }\n            }\n        },\n\n        _maybeProcessNextItemAfterOnValidateCallback: function(validItem, items, index, params, endpoint) {\n            var self = this;\n\n            if (items.length > index) {\n                if (validItem || !this._options.validation.stopOnFirstInvalidFile) {\n                    //use setTimeout to prevent a stack overflow with a large number of files in the batch & non-promissory callbacks\n                    setTimeout(function() {\n                        var validationDescriptor = self._getValidationDescriptor(items[index]),\n                            buttonId = self._getButtonId(items[index].file),\n                            button = self._getButton(buttonId);\n\n                        self._handleCheckedCallback({\n                            name: \"onValidate\",\n                            callback: qq.bind(self._options.callbacks.onValidate, self, validationDescriptor, button),\n                            onSuccess: qq.bind(self._onValidateCallbackSuccess, self, items, index, params, endpoint),\n                            onFailure: qq.bind(self._onValidateCallbackFailure, self, items, index, params, endpoint),\n                            identifier: \"Item '\" + validationDescriptor.name + \"', size: \" + validationDescriptor.size\n                        });\n                    }, 0);\n                }\n                else if (!validItem) {\n                    for (; index < items.length; index++) {\n                        self._fileOrBlobRejected(items[index].id);\n                    }\n                }\n            }\n        },\n\n        _onAllComplete: function(successful, failed) {\n            this._totalProgress && this._totalProgress.onAllComplete(successful, failed, this._preventRetries);\n\n            this._options.callbacks.onAllComplete(qq.extend([], successful), qq.extend([], failed));\n\n            this._succeededSinceLastAllComplete = [];\n            this._failedSinceLastAllComplete = [];\n        },\n\n        /**\n         * Attempt to automatically retry a failed upload.\n         *\n         * @param id The file ID of the failed upload\n         * @param name The name of the file associated with the failed upload\n         * @param responseJSON Response from the server, parsed into a javascript object\n         * @param xhr Ajax transport used to send the failed request\n         * @param callback Optional callback to be invoked if a retry is prudent.\n         * Invoked in lieu of asking the upload handler to retry.\n         * @returns {boolean} true if an auto-retry will occur\n         * @private\n         */\n        _onAutoRetry: function(id, name, responseJSON, xhr, callback) {\n            var self = this;\n\n            self._preventRetries[id] = responseJSON[self._options.retry.preventRetryResponseProperty];\n\n            if (self._shouldAutoRetry(id, name, responseJSON)) {\n                self._maybeParseAndSendUploadError.apply(self, arguments);\n                self._options.callbacks.onAutoRetry(id, name, self._autoRetries[id]);\n                self._onBeforeAutoRetry(id, name);\n\n                self._retryTimeouts[id] = setTimeout(function() {\n                    self.log(\"Retrying \" + name + \"...\");\n                    self._uploadData.setStatus(id, qq.status.UPLOAD_RETRYING);\n\n                    if (callback) {\n                        callback(id);\n                    }\n                    else {\n                        self._handler.retry(id);\n                    }\n                }, self._options.retry.autoAttemptDelay * 1000);\n\n                return true;\n            }\n        },\n\n        _onBeforeAutoRetry: function(id, name) {\n            this.log(\"Waiting \" + this._options.retry.autoAttemptDelay + \" seconds before retrying \" + name + \"...\");\n        },\n\n        //return false if we should not attempt the requested retry\n        _onBeforeManualRetry: function(id) {\n            var itemLimit = this._currentItemLimit,\n                fileName;\n\n            if (this._preventRetries[id]) {\n                this.log(\"Retries are forbidden for id \" + id, \"warn\");\n                return false;\n            }\n            else if (this._handler.isValid(id)) {\n                fileName = this.getName(id);\n\n                if (this._options.callbacks.onManualRetry(id, fileName) === false) {\n                    return false;\n                }\n\n                if (itemLimit > 0 && this._netUploadedOrQueued + 1 > itemLimit) {\n                    this._itemError(\"retryFailTooManyItems\");\n                    return false;\n                }\n\n                this.log(\"Retrying upload for '\" + fileName + \"' (id: \" + id + \")...\");\n                return true;\n            }\n            else {\n                this.log(\"'\" + id + \"' is not a valid file ID\", \"error\");\n                return false;\n            }\n        },\n\n        _onCancel: function(id, name) {\n            this._netUploadedOrQueued--;\n\n            clearTimeout(this._retryTimeouts[id]);\n\n            var storedItemIndex = qq.indexOf(this._storedIds, id);\n            if (!this._options.autoUpload && storedItemIndex >= 0) {\n                this._storedIds.splice(storedItemIndex, 1);\n            }\n\n            this._uploadData.setStatus(id, qq.status.CANCELED);\n        },\n\n        _onComplete: function(id, name, result, xhr) {\n            if (!result.success) {\n                this._netUploadedOrQueued--;\n                this._uploadData.setStatus(id, qq.status.UPLOAD_FAILED);\n\n                if (result[this._options.retry.preventRetryResponseProperty] === true) {\n                    this._preventRetries[id] = true;\n                }\n            }\n            else {\n                if (result.thumbnailUrl) {\n                    this._thumbnailUrls[id] = result.thumbnailUrl;\n                }\n\n                this._netUploaded++;\n                this._uploadData.setStatus(id, qq.status.UPLOAD_SUCCESSFUL);\n            }\n\n            this._maybeParseAndSendUploadError(id, name, result, xhr);\n\n            return result.success ? true : false;\n        },\n\n        _onDelete: function(id) {\n            this._uploadData.setStatus(id, qq.status.DELETING);\n        },\n\n        _onDeleteComplete: function(id, xhrOrXdr, isError) {\n            var name = this.getName(id);\n\n            if (isError) {\n                this._uploadData.setStatus(id, qq.status.DELETE_FAILED);\n                this.log(\"Delete request for '\" + name + \"' has failed.\", \"error\");\n\n                // For error reporting, we only have access to the response status if this is not\n                // an `XDomainRequest`.\n                if (xhrOrXdr.withCredentials === undefined) {\n                    this._options.callbacks.onError(id, name, \"Delete request failed\", xhrOrXdr);\n                }\n                else {\n                    this._options.callbacks.onError(id, name, \"Delete request failed with response code \" + xhrOrXdr.status, xhrOrXdr);\n                }\n            }\n            else {\n                this._netUploadedOrQueued--;\n                this._netUploaded--;\n                this._handler.expunge(id);\n                this._uploadData.setStatus(id, qq.status.DELETED);\n                this.log(\"Delete request for '\" + name + \"' has succeeded.\");\n            }\n        },\n\n        _onInputChange: function(input) {\n            var fileIndex;\n\n            if (qq.supportedFeatures.ajaxUploading) {\n                for (fileIndex = 0; fileIndex < input.files.length; fileIndex++) {\n                    this._annotateWithButtonId(input.files[fileIndex], input);\n                }\n\n                this.addFiles(input.files);\n            }\n            // Android 2.3.x will fire `onchange` even if no file has been selected\n            else if (input.value.length > 0) {\n                this.addFiles(input);\n            }\n\n            qq.each(this._buttons, function(idx, button) {\n                button.reset();\n            });\n        },\n\n        _onProgress: function(id, name, loaded, total) {\n            this._totalProgress && this._totalProgress.onIndividualProgress(id, loaded, total);\n        },\n\n        _onSubmit: function(id, name) {\n            //nothing to do yet in core uploader\n        },\n\n        _onSubmitCallbackSuccess: function(id, name) {\n            this._onSubmit.apply(this, arguments);\n            this._uploadData.setStatus(id, qq.status.SUBMITTED);\n            this._onSubmitted.apply(this, arguments);\n\n            if (this._options.autoUpload) {\n                this._options.callbacks.onSubmitted.apply(this, arguments);\n                this._uploadFile(id);\n            }\n            else {\n                this._storeForLater(id);\n                this._options.callbacks.onSubmitted.apply(this, arguments);\n            }\n        },\n\n        _onSubmitDelete: function(id, onSuccessCallback, additionalMandatedParams) {\n            var uuid = this.getUuid(id),\n                adjustedOnSuccessCallback;\n\n            if (onSuccessCallback) {\n                adjustedOnSuccessCallback = qq.bind(onSuccessCallback, this, id, uuid, additionalMandatedParams);\n            }\n\n            if (this._isDeletePossible()) {\n                this._handleCheckedCallback({\n                    name: \"onSubmitDelete\",\n                    callback: qq.bind(this._options.callbacks.onSubmitDelete, this, id),\n                    onSuccess: adjustedOnSuccessCallback ||\n                        qq.bind(this._deleteHandler.sendDelete, this, id, uuid, additionalMandatedParams),\n                    identifier: id\n                });\n                return true;\n            }\n            else {\n                this.log(\"Delete request ignored for ID \" + id + \", delete feature is disabled or request not possible \" +\n                    \"due to CORS on a user agent that does not support pre-flighting.\", \"warn\");\n                return false;\n            }\n        },\n\n        _onSubmitted: function(id) {\n            //nothing to do in the base uploader\n        },\n\n        _onTotalProgress: function(loaded, total) {\n            this._options.callbacks.onTotalProgress(loaded, total);\n        },\n\n        _onUploadPrep: function(id) {\n            // nothing to do in the core uploader for now\n        },\n\n        _onUpload: function(id, name) {\n            this._uploadData.setStatus(id, qq.status.UPLOADING);\n        },\n\n        _onUploadChunk: function(id, chunkData) {\n            //nothing to do in the base uploader\n        },\n\n        _onUploadStatusChange: function(id, oldStatus, newStatus) {\n            // Make sure a \"queued\" retry attempt is canceled if the upload has been paused\n            if (newStatus === qq.status.PAUSED) {\n                clearTimeout(this._retryTimeouts[id]);\n            }\n        },\n\n        _onValidateBatchCallbackFailure: function(fileWrappers) {\n            var self = this;\n\n            qq.each(fileWrappers, function(idx, fileWrapper) {\n                self._fileOrBlobRejected(fileWrapper.id);\n            });\n        },\n\n        _onValidateBatchCallbackSuccess: function(validationDescriptors, items, params, endpoint, button) {\n            var errorMessage,\n                itemLimit = this._currentItemLimit,\n                proposedNetFilesUploadedOrQueued = this._netUploadedOrQueued;\n\n            if (itemLimit === 0 || proposedNetFilesUploadedOrQueued <= itemLimit) {\n                if (items.length > 0) {\n                    this._handleCheckedCallback({\n                        name: \"onValidate\",\n                        callback: qq.bind(this._options.callbacks.onValidate, this, validationDescriptors[0], button),\n                        onSuccess: qq.bind(this._onValidateCallbackSuccess, this, items, 0, params, endpoint),\n                        onFailure: qq.bind(this._onValidateCallbackFailure, this, items, 0, params, endpoint),\n                        identifier: \"Item '\" + items[0].file.name + \"', size: \" + items[0].file.size\n                    });\n                }\n                else {\n                    this._itemError(\"noFilesError\");\n                }\n            }\n            else {\n                this._onValidateBatchCallbackFailure(items);\n                errorMessage = this._options.messages.tooManyItemsError\n                    .replace(/\\{netItems\\}/g, proposedNetFilesUploadedOrQueued)\n                    .replace(/\\{itemLimit\\}/g, itemLimit);\n                this._batchError(errorMessage);\n            }\n        },\n\n        _onValidateCallbackFailure: function(items, index, params, endpoint) {\n            var nextIndex = index + 1;\n\n            this._fileOrBlobRejected(items[index].id, items[index].file.name);\n\n            this._maybeProcessNextItemAfterOnValidateCallback(false, items, nextIndex, params, endpoint);\n        },\n\n        _onValidateCallbackSuccess: function(items, index, params, endpoint) {\n            var self = this,\n                nextIndex = index + 1,\n                validationDescriptor = this._getValidationDescriptor(items[index]);\n\n            this._validateFileOrBlobData(items[index], validationDescriptor)\n                .then(\n                function() {\n                    self._upload(items[index].id, params, endpoint);\n                    self._maybeProcessNextItemAfterOnValidateCallback(true, items, nextIndex, params, endpoint);\n                },\n                function() {\n                    self._maybeProcessNextItemAfterOnValidateCallback(false, items, nextIndex, params, endpoint);\n                }\n            );\n        },\n\n        _prepareItemsForUpload: function(items, params, endpoint) {\n            if (items.length === 0) {\n                this._itemError(\"noFilesError\");\n                return;\n            }\n\n            var validationDescriptors = this._getValidationDescriptors(items),\n                buttonId = this._getButtonId(items[0].file),\n                button = this._getButton(buttonId);\n\n            this._handleCheckedCallback({\n                name: \"onValidateBatch\",\n                callback: qq.bind(this._options.callbacks.onValidateBatch, this, validationDescriptors, button),\n                onSuccess: qq.bind(this._onValidateBatchCallbackSuccess, this, validationDescriptors, items, params, endpoint, button),\n                onFailure: qq.bind(this._onValidateBatchCallbackFailure, this, items),\n                identifier: \"batch validation\"\n            });\n        },\n\n        _preventLeaveInProgress: function() {\n            var self = this;\n\n            this._disposeSupport.attach(window, \"beforeunload\", function(e) {\n                if (self.getInProgress()) {\n                    e = e || window.event;\n                    // for ie, ff\n                    e.returnValue = self._options.messages.onLeave;\n                    // for webkit\n                    return self._options.messages.onLeave;\n                }\n            });\n        },\n\n        // Attempts to refresh session data only if the `qq.Session` module exists\n        // and a session endpoint has been specified.  The `onSessionRequestComplete`\n        // callback will be invoked once the refresh is complete.\n        _refreshSessionData: function() {\n            var self = this,\n                options = this._options.session;\n\n            /* jshint eqnull:true */\n            if (qq.Session && this._options.session.endpoint != null) {\n                if (!this._session) {\n                    qq.extend(options, {cors: this._options.cors});\n\n                    options.log = qq.bind(this.log, this);\n                    options.addFileRecord = qq.bind(this._addCannedFile, this);\n\n                    this._session = new qq.Session(options);\n                }\n\n                setTimeout(function() {\n                    self._session.refresh().then(function(response, xhrOrXdr) {\n                        self._sessionRequestComplete();\n                        self._options.callbacks.onSessionRequestComplete(response, true, xhrOrXdr);\n\n                    }, function(response, xhrOrXdr) {\n\n                        self._options.callbacks.onSessionRequestComplete(response, false, xhrOrXdr);\n                    });\n                }, 0);\n            }\n        },\n\n        _sessionRequestComplete: function() {},\n\n        _setSize: function(id, newSize) {\n            this._uploadData.updateSize(id, newSize);\n            this._totalProgress && this._totalProgress.onNewSize(id);\n        },\n\n        _shouldAutoRetry: function(id, name, responseJSON) {\n            var uploadData = this._uploadData.retrieve({id: id});\n\n            /*jshint laxbreak: true */\n            if (!this._preventRetries[id]\n                && this._options.retry.enableAuto\n                && uploadData.status !== qq.status.PAUSED) {\n\n                if (this._autoRetries[id] === undefined) {\n                    this._autoRetries[id] = 0;\n                }\n\n                if (this._autoRetries[id] < this._options.retry.maxAutoAttempts) {\n                    this._autoRetries[id] += 1;\n                    return true;\n                }\n            }\n\n            return false;\n        },\n\n        _storeForLater: function(id) {\n            this._storedIds.push(id);\n        },\n\n        // Maps a file with the button that was used to select it.\n        _trackButton: function(id) {\n            var buttonId;\n\n            if (qq.supportedFeatures.ajaxUploading) {\n                buttonId = this._handler.getFile(id).qqButtonId;\n            }\n            else {\n                buttonId = this._getButtonId(this._handler.getInput(id));\n            }\n\n            if (buttonId) {\n                this._buttonIdsForFileIds[id] = buttonId;\n            }\n        },\n\n        _updateFormSupportAndParams: function(formElementOrId) {\n            this._options.form.element = formElementOrId;\n\n            this._formSupport = qq.FormSupport && new qq.FormSupport(\n                    this._options.form, qq.bind(this.uploadStoredFiles, this), qq.bind(this.log, this)\n                );\n\n            if (this._formSupport && this._formSupport.attachedToForm) {\n                this._paramsStore.addReadOnly(null, this._formSupport.getFormInputsAsObject);\n\n                this._options.autoUpload = this._formSupport.newAutoUpload;\n                if (this._formSupport.newEndpoint) {\n                    this.setEndpoint(this._formSupport.newEndpoint);\n                }\n            }\n        },\n\n        _upload: function(id, params, endpoint) {\n            var name = this.getName(id);\n\n            if (params) {\n                this.setParams(params, id);\n            }\n\n            if (endpoint) {\n                this.setEndpoint(endpoint, id);\n            }\n\n            this._handleCheckedCallback({\n                name: \"onSubmit\",\n                callback: qq.bind(this._options.callbacks.onSubmit, this, id, name),\n                onSuccess: qq.bind(this._onSubmitCallbackSuccess, this, id, name),\n                onFailure: qq.bind(this._fileOrBlobRejected, this, id, name),\n                identifier: id\n            });\n        },\n\n        _uploadFile: function(id) {\n            if (!this._handler.upload(id)) {\n                this._uploadData.setStatus(id, qq.status.QUEUED);\n            }\n        },\n\n        _uploadStoredFiles: function() {\n            var idToUpload, stillSubmitting,\n                self = this;\n\n            while (this._storedIds.length) {\n                idToUpload = this._storedIds.shift();\n                this._uploadFile(idToUpload);\n            }\n\n            // If we are still waiting for some files to clear validation, attempt to upload these again in a bit\n            stillSubmitting = this.getUploads({status: qq.status.SUBMITTING}).length;\n            if (stillSubmitting) {\n                qq.log(\"Still waiting for \" + stillSubmitting + \" files to clear submit queue. Will re-parse stored IDs array shortly.\");\n                setTimeout(function() {\n                    self._uploadStoredFiles();\n                }, 1000);\n            }\n        },\n\n        /**\n         * Performs some internal validation checks on an item, defined in the `validation` option.\n         *\n         * @param fileWrapper Wrapper containing a `file` along with an `id`\n         * @param validationDescriptor Normalized information about the item (`size`, `name`).\n         * @returns qq.Promise with appropriate callbacks invoked depending on the validity of the file\n         * @private\n         */\n        _validateFileOrBlobData: function(fileWrapper, validationDescriptor) {\n            var self = this,\n                file = (function() {\n                    if (fileWrapper.file instanceof qq.BlobProxy) {\n                        return fileWrapper.file.referenceBlob;\n                    }\n                    return fileWrapper.file;\n                }()),\n                name = validationDescriptor.name,\n                size = validationDescriptor.size,\n                buttonId = this._getButtonId(fileWrapper.file),\n                validationBase = this._getValidationBase(buttonId),\n                validityChecker = new qq.Promise();\n\n            validityChecker.then(\n                function() {},\n                function() {\n                    self._fileOrBlobRejected(fileWrapper.id, name);\n                });\n\n            if (qq.isFileOrInput(file) && !this._isAllowedExtension(validationBase.allowedExtensions, name)) {\n                this._itemError(\"typeError\", name, file);\n                return validityChecker.failure();\n            }\n\n            if (size === 0) {\n                this._itemError(\"emptyError\", name, file);\n                return validityChecker.failure();\n            }\n\n            if (size > 0 && validationBase.sizeLimit && size > validationBase.sizeLimit) {\n                this._itemError(\"sizeError\", name, file);\n                return validityChecker.failure();\n            }\n\n            if (size > 0 && size < validationBase.minSizeLimit) {\n                this._itemError(\"minSizeError\", name, file);\n                return validityChecker.failure();\n            }\n\n            if (qq.ImageValidation && qq.supportedFeatures.imagePreviews && qq.isFile(file)) {\n                new qq.ImageValidation(file, qq.bind(self.log, self)).validate(validationBase.image).then(\n                    validityChecker.success,\n                    function(errorCode) {\n                        self._itemError(errorCode + \"ImageError\", name, file);\n                        validityChecker.failure();\n                    }\n                );\n            }\n            else {\n                validityChecker.success();\n            }\n\n            return validityChecker;\n        },\n\n        _wrapCallbacks: function() {\n            var self, safeCallback, prop;\n\n            self = this;\n\n            safeCallback = function(name, callback, args) {\n                var errorMsg;\n\n                try {\n                    return callback.apply(self, args);\n                }\n                catch (exception) {\n                    errorMsg = exception.message || exception.toString();\n                    self.log(\"Caught exception in '\" + name + \"' callback - \" + errorMsg, \"error\");\n                }\n            };\n\n            /* jshint forin: false, loopfunc: true */\n            for (prop in this._options.callbacks) {\n                (function() {\n                    var callbackName, callbackFunc;\n                    callbackName = prop;\n                    callbackFunc = self._options.callbacks[callbackName];\n                    self._options.callbacks[callbackName] = function() {\n                        return safeCallback(callbackName, callbackFunc, arguments);\n                    };\n                }());\n            }\n        }\n    };\n}());\n", "/*globals qq*/\n(function() {\n    \"use strict\";\n\n    qq.FineUploaderBasic = function(o) {\n        var self = this;\n\n        // These options define FineUploaderBasic mode.\n        this._options = {\n            debug: false,\n            button: null,\n            multiple: true,\n            maxConnections: 3,\n            disableCancelForFormUploads: false,\n            autoUpload: true,\n\n            request: {\n                customHeaders: {},\n                endpoint: \"/server/upload\",\n                filenameParam: \"qqfilename\",\n                forceMultipart: true,\n                inputName: \"qqfile\",\n                method: \"POST\",\n                params: {},\n                paramsInBody: true,\n                totalFileSizeName: \"qqtotalfilesize\",\n                uuidName: \"qquuid\"\n            },\n\n            validation: {\n                allowedExtensions: [],\n                sizeLimit: 0,\n                minSizeLimit: 0,\n                itemLimit: 0,\n                stopOnFirstInvalidFile: true,\n                acceptFiles: null,\n                image: {\n                    maxHeight: 0,\n                    maxWidth: 0,\n                    minHeight: 0,\n                    minWidth: 0\n                }\n            },\n\n            callbacks: {\n                onSubmit: function(id, name) {},\n                onSubmitted: function(id, name) {},\n                onComplete: function(id, name, responseJSON, maybeXhr) {},\n                onAllComplete: function(successful, failed) {},\n                onCancel: function(id, name) {},\n                onUpload: function(id, name) {},\n                onUploadChunk: function(id, name, chunkData) {},\n                onUploadChunkSuccess: function(id, chunkData, responseJSON, xhr) {},\n                onResume: function(id, fileName, chunkData) {},\n                onProgress: function(id, name, loaded, total) {},\n                onTotalProgress: function(loaded, total) {},\n                onError: function(id, name, reason, maybeXhrOrXdr) {},\n                onAutoRetry: function(id, name, attemptNumber) {},\n                onManualRetry: function(id, name) {},\n                onValidateBatch: function(fileOrBlobData) {},\n                onValidate: function(fileOrBlobData) {},\n                onSubmitDelete: function(id) {},\n                onDelete: function(id) {},\n                onDeleteComplete: function(id, xhrOrXdr, isError) {},\n                onPasteReceived: function(blob) {},\n                onStatusChange: function(id, oldStatus, newStatus) {},\n                onSessionRequestComplete: function(response, success, xhrOrXdr) {}\n            },\n\n            messages: {\n                typeError: \"{file} has an invalid extension. Valid extension(s): {extensions}.\",\n                sizeError: \"{file} is too large, maximum file size is {sizeLimit}.\",\n                minSizeError: \"{file} is too small, minimum file size is {minSizeLimit}.\",\n                emptyError: \"{file} is empty, please select files again without it.\",\n                noFilesError: \"No files to upload.\",\n                tooManyItemsError: \"Too many items ({netItems}) would be uploaded.  Item limit is {itemLimit}.\",\n                maxHeightImageError: \"Image is too tall.\",\n                maxWidthImageError: \"Image is too wide.\",\n                minHeightImageError: \"Image is not tall enough.\",\n                minWidthImageError: \"Image is not wide enough.\",\n                retryFailTooManyItems: \"Retry failed - you have reached your file limit.\",\n                onLeave: \"The files are being uploaded, if you leave now the upload will be canceled.\",\n                unsupportedBrowserIos8Safari: \"Unrecoverable error - this browser does not permit file uploading of any kind due to serious bugs in iOS8 Safari.  Please use iOS8 Chrome until Apple fixes these issues.\"\n            },\n\n            retry: {\n                enableAuto: false,\n                maxAutoAttempts: 3,\n                autoAttemptDelay: 5,\n                preventRetryResponseProperty: \"preventRetry\"\n            },\n\n            classes: {\n                buttonHover: \"qq-upload-button-hover\",\n                buttonFocus: \"qq-upload-button-focus\"\n            },\n\n            chunking: {\n                enabled: false,\n                concurrent: {\n                    enabled: false\n                },\n                mandatory: false,\n                paramNames: {\n                    partIndex: \"qqpartindex\",\n                    partByteOffset: \"qqpartbyteoffset\",\n                    chunkSize: \"qqchunksize\",\n                    totalFileSize: \"qqtotalfilesize\",\n                    totalParts: \"qqtotalparts\"\n                },\n                partSize: 2000000,\n                // only relevant for traditional endpoints, only required when concurrent.enabled === true\n                success: {\n                    endpoint: null\n                }\n            },\n\n            resume: {\n                enabled: false,\n                recordsExpireIn: 7, //days\n                paramNames: {\n                    resuming: \"qqresume\"\n                }\n            },\n\n            formatFileName: function(fileOrBlobName) {\n                return fileOrBlobName;\n            },\n\n            text: {\n                defaultResponseError: \"Upload failure reason unknown\",\n                fileInputTitle: \"file input\",\n                sizeSymbols: [\"kB\", \"MB\", \"GB\", \"TB\", \"PB\", \"EB\"]\n            },\n\n            deleteFile: {\n                enabled: false,\n                method: \"DELETE\",\n                endpoint: \"/server/upload\",\n                customHeaders: {},\n                params: {}\n            },\n\n            cors: {\n                expected: false,\n                sendCredentials: false,\n                allowXdr: false\n            },\n\n            blobs: {\n                defaultName: \"misc_data\"\n            },\n\n            paste: {\n                targetElement: null,\n                defaultName: \"pasted_image\"\n            },\n\n            camera: {\n                ios: false,\n\n                // if ios is true: button is null means target the default button, otherwise target the button specified\n                button: null\n            },\n\n            // This refers to additional upload buttons to be handled by Fine Uploader.\n            // Each element is an object, containing `element` as the only required\n            // property.  The `element` must be a container that will ultimately\n            // contain an invisible `<input type=\"file\">` created by Fine Uploader.\n            // Optional properties of each object include `multiple`, `validation`,\n            // and `folders`.\n            extraButtons: [],\n\n            // Depends on the session module.  Used to query the server for an initial file list\n            // during initialization and optionally after a `reset`.\n            session: {\n                endpoint: null,\n                params: {},\n                customHeaders: {},\n                refreshOnReset: true\n            },\n\n            // Send parameters associated with an existing form along with the files\n            form: {\n                // Element ID, HTMLElement, or null\n                element: \"qq-form\",\n\n                // Overrides the base `autoUpload`, unless `element` is null.\n                autoUpload: false,\n\n                // true = upload files on form submission (and squelch submit event)\n                interceptSubmit: true\n            },\n\n            // scale images client side, upload a new file for each scaled version\n            scaling: {\n                customResizer: null,\n\n                // send the original file as well\n                sendOriginal: true,\n\n                // fox orientation for scaled images\n                orient: true,\n\n                // If null, scaled image type will match reference image type.  This value will be referred to\n                // for any size record that does not specific a type.\n                defaultType: null,\n\n                defaultQuality: 80,\n\n                failureText: \"Failed to scale\",\n\n                includeExif: false,\n\n                // metadata about each requested scaled version\n                sizes: []\n            },\n\n            workarounds: {\n                iosEmptyVideos: true,\n                ios8SafariUploads: true,\n                ios8BrowserCrash: false\n            }\n        };\n\n        // Replace any default options with user defined ones\n        qq.extend(this._options, o, true);\n\n        this._buttons = [];\n        this._extraButtonSpecs = {};\n        this._buttonIdsForFileIds = [];\n\n        this._wrapCallbacks();\n        this._disposeSupport =  new qq.DisposeSupport();\n\n        this._storedIds = [];\n        this._autoRetries = [];\n        this._retryTimeouts = [];\n        this._preventRetries = [];\n        this._thumbnailUrls = [];\n\n        this._netUploadedOrQueued = 0;\n        this._netUploaded = 0;\n        this._uploadData = this._createUploadDataTracker();\n\n        this._initFormSupportAndParams();\n\n        this._customHeadersStore = this._createStore(this._options.request.customHeaders);\n        this._deleteFileCustomHeadersStore = this._createStore(this._options.deleteFile.customHeaders);\n\n        this._deleteFileParamsStore = this._createStore(this._options.deleteFile.params);\n\n        this._endpointStore = this._createStore(this._options.request.endpoint);\n        this._deleteFileEndpointStore = this._createStore(this._options.deleteFile.endpoint);\n\n        this._handler = this._createUploadHandler();\n\n        this._deleteHandler = qq.DeleteFileAjaxRequester && this._createDeleteHandler();\n\n        if (this._options.button) {\n            this._defaultButtonId = this._createUploadButton({\n                element: this._options.button,\n                title: this._options.text.fileInputTitle\n            }).getButtonId();\n        }\n\n        this._generateExtraButtonSpecs();\n\n        this._handleCameraAccess();\n\n        if (this._options.paste.targetElement) {\n            if (qq.PasteSupport) {\n                this._pasteHandler = this._createPasteHandler();\n            }\n            else {\n                this.log(\"Paste support module not found\", \"error\");\n            }\n        }\n\n        this._preventLeaveInProgress();\n\n        this._imageGenerator = qq.ImageGenerator && new qq.ImageGenerator(qq.bind(this.log, this));\n        this._refreshSessionData();\n\n        this._succeededSinceLastAllComplete = [];\n        this._failedSinceLastAllComplete = [];\n\n        this._scaler = (qq.Scaler && new qq.Scaler(this._options.scaling, qq.bind(this.log, this))) || {};\n        if (this._scaler.enabled) {\n            this._customNewFileHandler = qq.bind(this._scaler.handleNewFile, this._scaler);\n        }\n\n        if (qq.TotalProgress && qq.supportedFeatures.progressBar) {\n            this._totalProgress = new qq.TotalProgress(\n                qq.bind(this._onTotalProgress, this),\n\n                function(id) {\n                    var entry = self._uploadData.retrieve({id: id});\n                    return (entry && entry.size) || 0;\n                }\n            );\n        }\n\n        this._currentItemLimit = this._options.validation.itemLimit;\n    };\n\n    // Define the private & public API methods.\n    qq.FineUploaderBasic.prototype = qq.basePublicApi;\n    qq.extend(qq.FineUploaderBasic.prototype, qq.basePrivateApi);\n}());\n", "/*globals qq, XDomainRequest*/\n/** Generic class for sending non-upload ajax requests and handling the associated responses **/\nqq.AjaxRequester = function(o) {\n    \"use strict\";\n\n    var log, shouldParamsBeInQueryString,\n        queue = [],\n        requestData = {},\n        options = {\n            acceptHeader: null,\n            validMethods: [\"PATCH\", \"POST\", \"PUT\"],\n            method: \"POST\",\n            contentType: \"application/x-www-form-urlencoded\",\n            maxConnections: 3,\n            customHeaders: {},\n            endpointStore: {},\n            paramsStore: {},\n            mandatedParams: {},\n            allowXRequestedWithAndCacheControl: true,\n            successfulResponseCodes: {\n                DELETE: [200, 202, 204],\n                PATCH: [200, 201, 202, 203, 204],\n                POST: [200, 201, 202, 203, 204],\n                PUT: [200, 201, 202, 203, 204],\n                GET: [200]\n            },\n            cors: {\n                expected: false,\n                sendCredentials: false\n            },\n            log: function(str, level) {},\n            onSend: function(id) {},\n            onComplete: function(id, xhrOrXdr, isError) {},\n            onProgress: null\n        };\n\n    qq.extend(options, o);\n    log = options.log;\n\n    if (qq.indexOf(options.validMethods, options.method) < 0) {\n        throw new Error(\"'\" + options.method + \"' is not a supported method for this type of request!\");\n    }\n\n    // [Simple methods](http://www.w3.org/TR/cors/#simple-method)\n    // are defined by the W3C in the CORS spec as a list of methods that, in part,\n    // make a CORS request eligible to be exempt from preflighting.\n    function isSimpleMethod() {\n        return qq.indexOf([\"GET\", \"POST\", \"HEAD\"], options.method) >= 0;\n    }\n\n    // [Simple headers](http://www.w3.org/TR/cors/#simple-header)\n    // are defined by the W3C in the CORS spec as a list of headers that, in part,\n    // make a CORS request eligible to be exempt from preflighting.\n    function containsNonSimpleHeaders(headers) {\n        var containsNonSimple = false;\n\n        qq.each(containsNonSimple, function(idx, header) {\n            if (qq.indexOf([\"Accept\", \"Accept-Language\", \"Content-Language\", \"Content-Type\"], header) < 0) {\n                containsNonSimple = true;\n                return false;\n            }\n        });\n\n        return containsNonSimple;\n    }\n\n    function isXdr(xhr) {\n        //The `withCredentials` test is a commonly accepted way to determine if XHR supports CORS.\n        return options.cors.expected && xhr.withCredentials === undefined;\n    }\n\n    // Returns either a new `XMLHttpRequest` or `XDomainRequest` instance.\n    function getCorsAjaxTransport() {\n        var xhrOrXdr;\n\n        if (window.XMLHttpRequest || window.ActiveXObject) {\n            xhrOrXdr = qq.createXhrInstance();\n\n            if (xhrOrXdr.withCredentials === undefined) {\n                xhrOrXdr = new XDomainRequest();\n                // Workaround for XDR bug in IE9 - https://social.msdn.microsoft.com/Forums/ie/en-US/30ef3add-767c-4436-b8a9-f1ca19b4812e/ie9-rtm-xdomainrequest-issued-requests-may-abort-if-all-event-handlers-not-specified?forum=iewebdevelopment\n                xhrOrXdr.onload = function() {};\n                xhrOrXdr.onerror = function() {};\n                xhrOrXdr.ontimeout = function() {};\n                xhrOrXdr.onprogress = function() {};\n            }\n        }\n\n        return xhrOrXdr;\n    }\n\n    // Returns either a new XHR/XDR instance, or an existing one for the associated `File` or `Blob`.\n    function getXhrOrXdr(id, suppliedXhr) {\n        var xhrOrXdr = requestData[id].xhr;\n\n        if (!xhrOrXdr) {\n            if (suppliedXhr) {\n                xhrOrXdr = suppliedXhr;\n            }\n            else {\n                if (options.cors.expected) {\n                    xhrOrXdr = getCorsAjaxTransport();\n                }\n                else {\n                    xhrOrXdr = qq.createXhrInstance();\n                }\n            }\n\n            requestData[id].xhr = xhrOrXdr;\n        }\n\n        return xhrOrXdr;\n    }\n\n    // Removes element from queue, sends next request\n    function dequeue(id) {\n        var i = qq.indexOf(queue, id),\n            max = options.maxConnections,\n            nextId;\n\n        delete requestData[id];\n        queue.splice(i, 1);\n\n        if (queue.length >= max && i < max) {\n            nextId = queue[max - 1];\n            sendRequest(nextId);\n        }\n    }\n\n    function onComplete(id, xdrError) {\n        var xhr = getXhrOrXdr(id),\n            method = options.method,\n            isError = xdrError === true;\n\n        dequeue(id);\n\n        if (isError) {\n            log(method + \" request for \" + id + \" has failed\", \"error\");\n        }\n        else if (!isXdr(xhr) && !isResponseSuccessful(xhr.status)) {\n            isError = true;\n            log(method + \" request for \" + id + \" has failed - response code \" + xhr.status, \"error\");\n        }\n\n        options.onComplete(id, xhr, isError);\n    }\n\n    function getParams(id) {\n        var onDemandParams = requestData[id].additionalParams,\n            mandatedParams = options.mandatedParams,\n            params;\n\n        if (options.paramsStore.get) {\n            params = options.paramsStore.get(id);\n        }\n\n        if (onDemandParams) {\n            qq.each(onDemandParams, function(name, val) {\n                params = params || {};\n                params[name] = val;\n            });\n        }\n\n        if (mandatedParams) {\n            qq.each(mandatedParams, function(name, val) {\n                params = params || {};\n                params[name] = val;\n            });\n        }\n\n        return params;\n    }\n\n    function sendRequest(id, optXhr) {\n        var xhr = getXhrOrXdr(id, optXhr),\n            method = options.method,\n            params = getParams(id),\n            payload = requestData[id].payload,\n            url;\n\n        options.onSend(id);\n\n        url = createUrl(id, params, requestData[id].additionalQueryParams);\n\n        // XDR and XHR status detection APIs differ a bit.\n        if (isXdr(xhr)) {\n            xhr.onload = getXdrLoadHandler(id);\n            xhr.onerror = getXdrErrorHandler(id);\n        }\n        else {\n            xhr.onreadystatechange = getXhrReadyStateChangeHandler(id);\n        }\n\n        registerForUploadProgress(id);\n\n        // The last parameter is assumed to be ignored if we are actually using `XDomainRequest`.\n        xhr.open(method, url, true);\n\n        // Instruct the transport to send cookies along with the CORS request,\n        // unless we are using `XDomainRequest`, which is not capable of this.\n        if (options.cors.expected && options.cors.sendCredentials && !isXdr(xhr)) {\n            xhr.withCredentials = true;\n        }\n\n        setHeaders(id);\n\n        log(\"Sending \" + method + \" request for \" + id);\n\n        if (payload) {\n            xhr.send(payload);\n        }\n        else if (shouldParamsBeInQueryString || !params) {\n            xhr.send();\n        }\n        else if (params && options.contentType && options.contentType.toLowerCase().indexOf(\"application/x-www-form-urlencoded\") >= 0) {\n            xhr.send(qq.obj2url(params, \"\"));\n        }\n        else if (params && options.contentType && options.contentType.toLowerCase().indexOf(\"application/json\") >= 0) {\n            xhr.send(JSON.stringify(params));\n        }\n        else {\n            xhr.send(params);\n        }\n\n        return xhr;\n    }\n\n    function createUrl(id, params, additionalQueryParams) {\n        var endpoint = options.endpointStore.get(id),\n            addToPath = requestData[id].addToPath;\n\n        /*jshint -W116,-W041 */\n        if (addToPath != undefined) {\n            endpoint += \"/\" + addToPath;\n        }\n\n        if (shouldParamsBeInQueryString && params) {\n            endpoint = qq.obj2url(params, endpoint);\n        }\n\n        if (additionalQueryParams) {\n            endpoint = qq.obj2url(additionalQueryParams, endpoint);\n        }\n\n        return endpoint;\n    }\n\n    // Invoked by the UA to indicate a number of possible states that describe\n    // a live `XMLHttpRequest` transport.\n    function getXhrReadyStateChangeHandler(id) {\n        return function() {\n            if (getXhrOrXdr(id).readyState === 4) {\n                onComplete(id);\n            }\n        };\n    }\n\n    function registerForUploadProgress(id) {\n        var onProgress = options.onProgress;\n\n        if (onProgress) {\n            getXhrOrXdr(id).upload.onprogress = function(e) {\n                if (e.lengthComputable) {\n                    onProgress(id, e.loaded, e.total);\n                }\n            };\n        }\n    }\n\n    // This will be called by IE to indicate **success** for an associated\n    // `XDomainRequest` transported request.\n    function getXdrLoadHandler(id) {\n        return function() {\n            onComplete(id);\n        };\n    }\n\n    // This will be called by IE to indicate **failure** for an associated\n    // `XDomainRequest` transported request.\n    function getXdrErrorHandler(id) {\n        return function() {\n            onComplete(id, true);\n        };\n    }\n\n    function setHeaders(id) {\n        var xhr = getXhrOrXdr(id),\n            customHeaders = options.customHeaders,\n            onDemandHeaders = requestData[id].additionalHeaders || {},\n            method = options.method,\n            allHeaders = {};\n\n        // If XDomainRequest is being used, we can't set headers, so just ignore this block.\n        if (!isXdr(xhr)) {\n            options.acceptHeader && xhr.setRequestHeader(\"Accept\", options.acceptHeader);\n\n            // Only attempt to add X-Requested-With & Cache-Control if permitted\n            if (options.allowXRequestedWithAndCacheControl) {\n                // Do not add X-Requested-With & Cache-Control if this is a cross-origin request\n                // OR the cross-origin request contains a non-simple method or header.\n                // This is done to ensure a preflight is not triggered exclusively based on the\n                // addition of these 2 non-simple headers.\n                if (!options.cors.expected || (!isSimpleMethod() || containsNonSimpleHeaders(customHeaders))) {\n                    xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\n                    xhr.setRequestHeader(\"Cache-Control\", \"no-cache\");\n                }\n            }\n\n            if (options.contentType && (method === \"POST\" || method === \"PUT\")) {\n                xhr.setRequestHeader(\"Content-Type\", options.contentType);\n            }\n\n            qq.extend(allHeaders, qq.isFunction(customHeaders) ? customHeaders(id) : customHeaders);\n            qq.extend(allHeaders, onDemandHeaders);\n\n            qq.each(allHeaders, function(name, val) {\n                xhr.setRequestHeader(name, val);\n            });\n        }\n    }\n\n    function isResponseSuccessful(responseCode) {\n        return qq.indexOf(options.successfulResponseCodes[options.method], responseCode) >= 0;\n    }\n\n    function prepareToSend(id, optXhr, addToPath, additionalParams, additionalQueryParams, additionalHeaders, payload) {\n        requestData[id] = {\n            addToPath: addToPath,\n            additionalParams: additionalParams,\n            additionalQueryParams: additionalQueryParams,\n            additionalHeaders: additionalHeaders,\n            payload: payload\n        };\n\n        var len = queue.push(id);\n\n        // if too many active connections, wait...\n        if (len <= options.maxConnections) {\n            return sendRequest(id, optXhr);\n        }\n    }\n\n    shouldParamsBeInQueryString = options.method === \"GET\" || options.method === \"DELETE\";\n\n    qq.extend(this, {\n        // Start the process of sending the request.  The ID refers to the file associated with the request.\n        initTransport: function(id) {\n            var path, params, headers, payload, cacheBuster, additionalQueryParams;\n\n            return {\n                // Optionally specify the end of the endpoint path for the request.\n                withPath: function(appendToPath) {\n                    path = appendToPath;\n                    return this;\n                },\n\n                // Optionally specify additional parameters to send along with the request.\n                // These will be added to the query string for GET/DELETE requests or the payload\n                // for POST/PUT requests.  The Content-Type of the request will be used to determine\n                // how these parameters should be formatted as well.\n                withParams: function(additionalParams) {\n                    params = additionalParams;\n                    return this;\n                },\n\n                withQueryParams: function(_additionalQueryParams_) {\n                    additionalQueryParams = _additionalQueryParams_;\n                    return this;\n                },\n\n                // Optionally specify additional headers to send along with the request.\n                withHeaders: function(additionalHeaders) {\n                    headers = additionalHeaders;\n                    return this;\n                },\n\n                // Optionally specify a payload/body for the request.\n                withPayload: function(thePayload) {\n                    payload = thePayload;\n                    return this;\n                },\n\n                // Appends a cache buster (timestamp) to the request URL as a query parameter (only if GET or DELETE)\n                withCacheBuster: function() {\n                    cacheBuster = true;\n                    return this;\n                },\n\n                // Send the constructed request.\n                send: function(optXhr) {\n                    if (cacheBuster && qq.indexOf([\"GET\", \"DELETE\"], options.method) >= 0) {\n                        params.qqtimestamp = new Date().getTime();\n                    }\n\n                    return prepareToSend(id, optXhr, path, params, additionalQueryParams, headers, payload);\n                }\n            };\n        },\n\n        canceled: function(id) {\n            dequeue(id);\n        }\n    });\n};\n", "/* globals qq */\n/**\n * Common upload handler functions.\n *\n * @constructor\n */\nqq.UploadHandler = function(spec) {\n    \"use strict\";\n\n    var proxy = spec.proxy,\n        fileState = {},\n        onCancel = proxy.onCancel,\n        getName = proxy.getName;\n\n    qq.extend(this, {\n        add: function(id, fileItem) {\n            fileState[id] = fileItem;\n            fileState[id].temp = {};\n        },\n\n        cancel: function(id) {\n            var self = this,\n                cancelFinalizationEffort = new qq.Promise(),\n                onCancelRetVal = onCancel(id, getName(id), cancelFinalizationEffort);\n\n            onCancelRetVal.then(function() {\n                if (self.isValid(id)) {\n                    fileState[id].canceled = true;\n                    self.expunge(id);\n                }\n                cancelFinalizationEffort.success();\n            });\n        },\n\n        expunge: function(id) {\n            delete fileState[id];\n        },\n\n        getThirdPartyFileId: function(id) {\n            return fileState[id].key;\n        },\n\n        isValid: function(id) {\n            return fileState[id] !== undefined;\n        },\n\n        reset: function() {\n            fileState = {};\n        },\n\n        _getFileState: function(id) {\n            return fileState[id];\n        },\n\n        _setThirdPartyFileId: function(id, thirdPartyFileId) {\n            fileState[id].key = thirdPartyFileId;\n        },\n\n        _wasCanceled: function(id) {\n            return !!fileState[id].canceled;\n        }\n    });\n};\n", "/*globals qq*/\n/**\n * Base upload handler module.  Controls more specific handlers.\n *\n * @param o Options.  Passed along to the specific handler submodule as well.\n * @param namespace [optional] Namespace for the specific handler.\n */\nqq.UploadHandlerController = function(o, namespace) {\n    \"use strict\";\n\n    var controller = this,\n        chunkingPossible = false,\n        concurrentChunkingPossible = false,\n        chunking, preventRetryResponse, log, handler,\n\n    options = {\n        paramsStore: {},\n        maxConnections: 3, // maximum number of concurrent uploads\n        chunking: {\n            enabled: false,\n            multiple: {\n                enabled: false\n            }\n        },\n        log: function(str, level) {},\n        onProgress: function(id, fileName, loaded, total) {},\n        onComplete: function(id, fileName, response, xhr) {},\n        onCancel: function(id, fileName) {},\n        onUploadPrep: function(id) {}, // Called if non-trivial operations will be performed before onUpload\n        onUpload: function(id, fileName) {},\n        onUploadChunk: function(id, fileName, chunkData) {},\n        onUploadChunkSuccess: function(id, chunkData, response, xhr) {},\n        onAutoRetry: function(id, fileName, response, xhr) {},\n        onResume: function(id, fileName, chunkData) {},\n        onUuidChanged: function(id, newUuid) {},\n        getName: function(id) {},\n        setSize: function(id, newSize) {},\n        isQueued: function(id) {},\n        getIdsInProxyGroup: function(id) {},\n        getIdsInBatch: function(id) {}\n    },\n\n    chunked = {\n        // Called when each chunk has uploaded successfully\n        done: function(id, chunkIdx, response, xhr) {\n            var chunkData = handler._getChunkData(id, chunkIdx);\n\n            handler._getFileState(id).attemptingResume = false;\n\n            delete handler._getFileState(id).temp.chunkProgress[chunkIdx];\n            handler._getFileState(id).loaded += chunkData.size;\n\n            options.onUploadChunkSuccess(id, handler._getChunkDataForCallback(chunkData), response, xhr);\n        },\n\n        // Called when all chunks have been successfully uploaded and we want to ask the handler to perform any\n        // logic associated with closing out the file, such as combining the chunks.\n        finalize: function(id) {\n            var size = options.getSize(id),\n                name = options.getName(id);\n\n            log(\"All chunks have been uploaded for \" + id + \" - finalizing....\");\n            handler.finalizeChunks(id).then(\n                function(response, xhr) {\n                    log(\"Finalize successful for \" + id);\n\n                    var normaizedResponse = upload.normalizeResponse(response, true);\n\n                    options.onProgress(id, name, size, size);\n                    handler._maybeDeletePersistedChunkData(id);\n                    upload.cleanup(id, normaizedResponse, xhr);\n                },\n                function(response, xhr) {\n                    var normaizedResponse = upload.normalizeResponse(response, false);\n\n                    log(\"Problem finalizing chunks for file ID \" + id + \" - \" + normaizedResponse.error, \"error\");\n\n                    if (normaizedResponse.reset) {\n                        chunked.reset(id);\n                    }\n\n                    if (!options.onAutoRetry(id, name, normaizedResponse, xhr)) {\n                        upload.cleanup(id, normaizedResponse, xhr);\n                    }\n                }\n            );\n        },\n\n        hasMoreParts: function(id) {\n            return !!handler._getFileState(id).chunking.remaining.length;\n        },\n\n        nextPart: function(id) {\n            var nextIdx = handler._getFileState(id).chunking.remaining.shift();\n\n            if (nextIdx >= handler._getTotalChunks(id)) {\n                nextIdx = null;\n            }\n\n            return nextIdx;\n        },\n\n        reset: function(id) {\n            log(\"Server or callback has ordered chunking effort to be restarted on next attempt for item ID \" + id, \"error\");\n\n            handler._maybeDeletePersistedChunkData(id);\n            handler.reevaluateChunking(id);\n            handler._getFileState(id).loaded = 0;\n        },\n\n        sendNext: function(id) {\n            var size = options.getSize(id),\n                name = options.getName(id),\n                chunkIdx = chunked.nextPart(id),\n                chunkData = handler._getChunkData(id, chunkIdx),\n                resuming = handler._getFileState(id).attemptingResume,\n                inProgressChunks = handler._getFileState(id).chunking.inProgress || [];\n\n            if (handler._getFileState(id).loaded == null) {\n                handler._getFileState(id).loaded = 0;\n            }\n\n            // Don't follow-through with the resume attempt if the integrator returns false from onResume\n            if (resuming && options.onResume(id, name, chunkData) === false) {\n                chunked.reset(id);\n                chunkIdx = chunked.nextPart(id);\n                chunkData = handler._getChunkData(id, chunkIdx);\n                resuming = false;\n            }\n\n            // If all chunks have already uploaded successfully, we must be re-attempting the finalize step.\n            if (chunkIdx == null && inProgressChunks.length === 0) {\n                chunked.finalize(id);\n            }\n\n            // Send the next chunk\n            else {\n                log(qq.format(\"Sending chunked upload request for item {}.{}, bytes {}-{} of {}.\", id, chunkIdx, chunkData.start + 1, chunkData.end, size));\n                options.onUploadChunk(id, name, handler._getChunkDataForCallback(chunkData));\n                inProgressChunks.push(chunkIdx);\n                handler._getFileState(id).chunking.inProgress = inProgressChunks;\n\n                if (concurrentChunkingPossible) {\n                    connectionManager.open(id, chunkIdx);\n                }\n\n                if (concurrentChunkingPossible && connectionManager.available() && handler._getFileState(id).chunking.remaining.length) {\n                    chunked.sendNext(id);\n                }\n\n                handler.uploadChunk(id, chunkIdx, resuming).then(\n                    // upload chunk success\n                    function success(response, xhr) {\n                        log(\"Chunked upload request succeeded for \" + id + \", chunk \" + chunkIdx);\n\n                        handler.clearCachedChunk(id, chunkIdx);\n\n                        var inProgressChunks = handler._getFileState(id).chunking.inProgress || [],\n                            responseToReport = upload.normalizeResponse(response, true),\n                            inProgressChunkIdx = qq.indexOf(inProgressChunks, chunkIdx);\n\n                        log(qq.format(\"Chunk {} for file {} uploaded successfully.\", chunkIdx, id));\n\n                        chunked.done(id, chunkIdx, responseToReport, xhr);\n\n                        if (inProgressChunkIdx >= 0) {\n                            inProgressChunks.splice(inProgressChunkIdx, 1);\n                        }\n\n                        handler._maybePersistChunkedState(id);\n\n                        if (!chunked.hasMoreParts(id) && inProgressChunks.length === 0) {\n                            chunked.finalize(id);\n                        }\n                        else if (chunked.hasMoreParts(id)) {\n                            chunked.sendNext(id);\n                        }\n                        else {\n                            log(qq.format(\"File ID {} has no more chunks to send and these chunk indexes are still marked as in-progress: {}\", id, JSON.stringify(inProgressChunks)));\n                        }\n                    },\n\n                    // upload chunk failure\n                    function failure(response, xhr) {\n                        log(\"Chunked upload request failed for \" + id + \", chunk \" + chunkIdx);\n\n                        handler.clearCachedChunk(id, chunkIdx);\n\n                        var responseToReport = upload.normalizeResponse(response, false),\n                            inProgressIdx;\n\n                        if (responseToReport.reset) {\n                            chunked.reset(id);\n                        }\n                        else {\n                            inProgressIdx = qq.indexOf(handler._getFileState(id).chunking.inProgress, chunkIdx);\n                            if (inProgressIdx >= 0) {\n                                handler._getFileState(id).chunking.inProgress.splice(inProgressIdx, 1);\n                                handler._getFileState(id).chunking.remaining.unshift(chunkIdx);\n                            }\n                        }\n\n                        // We may have aborted all other in-progress chunks for this file due to a failure.\n                        // If so, ignore the failures associated with those aborts.\n                        if (!handler._getFileState(id).temp.ignoreFailure) {\n                            // If this chunk has failed, we want to ignore all other failures of currently in-progress\n                            // chunks since they will be explicitly aborted\n                            if (concurrentChunkingPossible) {\n                                handler._getFileState(id).temp.ignoreFailure = true;\n\n                                log(qq.format(\"Going to attempt to abort these chunks: {}. These are currently in-progress: {}.\", JSON.stringify(Object.keys(handler._getXhrs(id))), JSON.stringify(handler._getFileState(id).chunking.inProgress)));\n                                qq.each(handler._getXhrs(id), function(ckid, ckXhr) {\n                                    log(qq.format(\"Attempting to abort file {}.{}. XHR readyState {}. \", id, ckid, ckXhr.readyState));\n                                    ckXhr.abort();\n                                    // Flag the transport, in case we are waiting for some other async operation\n                                    // to complete before attempting to upload the chunk\n                                    ckXhr._cancelled = true;\n                                });\n\n                                // We must indicate that all aborted chunks are no longer in progress\n                                handler.moveInProgressToRemaining(id);\n\n                                // Free up any connections used by these chunks, but don't allow any\n                                // other files to take up the connections (until we have exhausted all auto-retries)\n                                connectionManager.free(id, true);\n                            }\n\n                            if (!options.onAutoRetry(id, name, responseToReport, xhr)) {\n                                // If one chunk fails, abort all of the others to avoid odd race conditions that occur\n                                // if a chunk succeeds immediately after one fails before we have determined if the upload\n                                // is a failure or not.\n                                upload.cleanup(id, responseToReport, xhr);\n                            }\n                        }\n                    }\n                )\n                    .done(function() {\n                        handler.clearXhr(id, chunkIdx);\n                    });\n            }\n        }\n    },\n\n    connectionManager = {\n        _open: [],\n        _openChunks: {},\n        _waiting: [],\n\n        available: function() {\n            var max = options.maxConnections,\n                openChunkEntriesCount = 0,\n                openChunksCount = 0;\n\n            qq.each(connectionManager._openChunks, function(fileId, openChunkIndexes) {\n                openChunkEntriesCount++;\n                openChunksCount += openChunkIndexes.length;\n            });\n\n            return max - (connectionManager._open.length - openChunkEntriesCount + openChunksCount);\n        },\n\n        /**\n         * Removes element from queue, starts upload of next\n         */\n        free: function(id, dontAllowNext) {\n            var allowNext = !dontAllowNext,\n                waitingIndex = qq.indexOf(connectionManager._waiting, id),\n                connectionsIndex = qq.indexOf(connectionManager._open, id),\n                nextId;\n\n            delete connectionManager._openChunks[id];\n\n            if (upload.getProxyOrBlob(id) instanceof qq.BlobProxy) {\n                log(\"Generated blob upload has ended for \" + id + \", disposing generated blob.\");\n                delete handler._getFileState(id).file;\n            }\n\n            // If this file was not consuming a connection, it was just waiting, so remove it from the waiting array\n            if (waitingIndex >= 0) {\n                connectionManager._waiting.splice(waitingIndex, 1);\n            }\n            // If this file was consuming a connection, allow the next file to be uploaded\n            else if (allowNext && connectionsIndex >= 0) {\n                connectionManager._open.splice(connectionsIndex, 1);\n\n                nextId = connectionManager._waiting.shift();\n                if (nextId >= 0) {\n                    connectionManager._open.push(nextId);\n                    upload.start(nextId);\n                }\n            }\n        },\n\n        getWaitingOrConnected: function() {\n            var waitingOrConnected = [];\n\n            // Chunked files may have multiple connections open per chunk (if concurrent chunking is enabled)\n            // We need to grab the file ID of any file that has at least one chunk consuming a connection.\n            qq.each(connectionManager._openChunks, function(fileId, chunks) {\n                if (chunks && chunks.length) {\n                    waitingOrConnected.push(parseInt(fileId));\n                }\n            });\n\n            // For non-chunked files, only one connection will be consumed per file.\n            // This is where we aggregate those file IDs.\n            qq.each(connectionManager._open, function(idx, fileId) {\n                if (!connectionManager._openChunks[fileId]) {\n                    waitingOrConnected.push(parseInt(fileId));\n                }\n            });\n\n            // There may be files waiting for a connection.\n            waitingOrConnected = waitingOrConnected.concat(connectionManager._waiting);\n\n            return waitingOrConnected;\n        },\n\n        isUsingConnection: function(id) {\n            return qq.indexOf(connectionManager._open, id) >= 0;\n        },\n\n        open: function(id, chunkIdx) {\n            if (chunkIdx == null) {\n                connectionManager._waiting.push(id);\n            }\n\n            if (connectionManager.available()) {\n                if (chunkIdx == null) {\n                    connectionManager._waiting.pop();\n                    connectionManager._open.push(id);\n                }\n                else {\n                    (function() {\n                        var openChunksEntry = connectionManager._openChunks[id] || [];\n                        openChunksEntry.push(chunkIdx);\n                        connectionManager._openChunks[id] = openChunksEntry;\n                    }());\n                }\n\n                return true;\n            }\n\n            return false;\n        },\n\n        reset: function() {\n            connectionManager._waiting = [];\n            connectionManager._open = [];\n        }\n    },\n\n    simple = {\n        send: function(id, name) {\n            handler._getFileState(id).loaded = 0;\n\n            log(\"Sending simple upload request for \" + id);\n            handler.uploadFile(id).then(\n                function(response, optXhr) {\n                    log(\"Simple upload request succeeded for \" + id);\n\n                    var responseToReport = upload.normalizeResponse(response, true),\n                        size = options.getSize(id);\n\n                    options.onProgress(id, name, size, size);\n                    upload.maybeNewUuid(id, responseToReport);\n                    upload.cleanup(id, responseToReport, optXhr);\n                },\n\n                function(response, optXhr) {\n                    log(\"Simple upload request failed for \" + id);\n\n                    var responseToReport = upload.normalizeResponse(response, false);\n\n                    if (!options.onAutoRetry(id, name, responseToReport, optXhr)) {\n                        upload.cleanup(id, responseToReport, optXhr);\n                    }\n                }\n            );\n        }\n    },\n\n    upload = {\n        cancel: function(id) {\n            log(\"Cancelling \" + id);\n            options.paramsStore.remove(id);\n            connectionManager.free(id);\n        },\n\n        cleanup: function(id, response, optXhr) {\n            var name = options.getName(id);\n\n            options.onComplete(id, name, response, optXhr);\n\n            if (handler._getFileState(id)) {\n                handler._clearXhrs && handler._clearXhrs(id);\n            }\n\n            connectionManager.free(id);\n        },\n\n        // Returns a qq.BlobProxy, or an actual File/Blob if no proxy is involved, or undefined\n        // if none of these are available for the ID\n        getProxyOrBlob: function(id) {\n            return (handler.getProxy && handler.getProxy(id)) ||\n                (handler.getFile && handler.getFile(id));\n        },\n\n        initHandler: function() {\n            var handlerType = namespace ? qq[namespace] : qq.traditional,\n                handlerModuleSubtype = qq.supportedFeatures.ajaxUploading ? \"Xhr\" : \"Form\";\n\n            handler = new handlerType[handlerModuleSubtype + \"UploadHandler\"](\n                options,\n                {\n                    getDataByUuid: options.getDataByUuid,\n                    getName: options.getName,\n                    getSize: options.getSize,\n                    getUuid: options.getUuid,\n                    log: log,\n                    onCancel: options.onCancel,\n                    onProgress: options.onProgress,\n                    onUuidChanged: options.onUuidChanged\n                }\n            );\n\n            if (handler._removeExpiredChunkingRecords) {\n                handler._removeExpiredChunkingRecords();\n            }\n        },\n\n        isDeferredEligibleForUpload: function(id) {\n            return options.isQueued(id);\n        },\n\n        // For Blobs that are part of a group of generated images, along with a reference image,\n        // this will ensure the blobs in the group are uploaded in the order they were triggered,\n        // even if some async processing must be completed on one or more Blobs first.\n        maybeDefer: function(id, blob) {\n            // If we don't have a file/blob yet & no file/blob exists for this item, request it,\n            // and then submit the upload to the specific handler once the blob is available.\n            // ASSUMPTION: This condition will only ever be true if XHR uploading is supported.\n            if (blob && !handler.getFile(id) && blob instanceof qq.BlobProxy) {\n\n                // Blob creation may take some time, so the caller may want to update the\n                // UI to indicate that an operation is in progress, even before the actual\n                // upload begins and an onUpload callback is invoked.\n                options.onUploadPrep(id);\n\n                log(\"Attempting to generate a blob on-demand for \" + id);\n                blob.create().then(function(generatedBlob) {\n                    log(\"Generated an on-demand blob for \" + id);\n\n                    // Update record associated with this file by providing the generated Blob\n                    handler.updateBlob(id, generatedBlob);\n\n                    // Propagate the size for this generated Blob\n                    options.setSize(id, generatedBlob.size);\n\n                    // Order handler to recalculate chunking possibility, if applicable\n                    handler.reevaluateChunking(id);\n\n                    upload.maybeSendDeferredFiles(id);\n                },\n\n                // Blob could not be generated.  Fail the upload & attempt to prevent retries.  Also bubble error message.\n                function(errorMessage) {\n                    var errorResponse = {};\n\n                    if (errorMessage) {\n                        errorResponse.error = errorMessage;\n                    }\n\n                    log(qq.format(\"Failed to generate blob for ID {}.  Error message: {}.\", id, errorMessage), \"error\");\n\n                    options.onComplete(id, options.getName(id), qq.extend(errorResponse, preventRetryResponse), null);\n                    upload.maybeSendDeferredFiles(id);\n                    connectionManager.free(id);\n                });\n            }\n            else {\n                return upload.maybeSendDeferredFiles(id);\n            }\n\n            return false;\n        },\n\n        // Upload any grouped blobs, in the proper order, that are ready to be uploaded\n        maybeSendDeferredFiles: function(id) {\n            var idsInGroup = options.getIdsInProxyGroup(id),\n                uploadedThisId = false;\n\n            if (idsInGroup && idsInGroup.length) {\n                log(\"Maybe ready to upload proxy group file \" + id);\n\n                qq.each(idsInGroup, function(idx, idInGroup) {\n                    if (upload.isDeferredEligibleForUpload(idInGroup) && !!handler.getFile(idInGroup)) {\n                        uploadedThisId = idInGroup === id;\n                        upload.now(idInGroup);\n                    }\n                    else if (upload.isDeferredEligibleForUpload(idInGroup)) {\n                        return false;\n                    }\n                });\n            }\n            else {\n                uploadedThisId = true;\n                upload.now(id);\n            }\n\n            return uploadedThisId;\n        },\n\n        maybeNewUuid: function(id, response) {\n            if (response.newUuid !== undefined) {\n                options.onUuidChanged(id, response.newUuid);\n            }\n        },\n\n        // The response coming from handler implementations may be in various formats.\n        // Instead of hoping a promise nested 5 levels deep will always return an object\n        // as its first param, let's just normalize the response here.\n        normalizeResponse: function(originalResponse, successful) {\n            var response = originalResponse;\n\n            // The passed \"response\" param may not be a response at all.\n            // It could be a string, detailing the error, for example.\n            if (!qq.isObject(originalResponse)) {\n                response = {};\n\n                if (qq.isString(originalResponse) && !successful) {\n                    response.error = originalResponse;\n                }\n            }\n\n            response.success = successful;\n\n            return response;\n        },\n\n        now: function(id) {\n            var name = options.getName(id);\n\n            if (!controller.isValid(id)) {\n                throw new qq.Error(id + \" is not a valid file ID to upload!\");\n            }\n\n            options.onUpload(id, name);\n\n            if (chunkingPossible && handler._shouldChunkThisFile(id)) {\n                chunked.sendNext(id);\n            }\n            else {\n                simple.send(id, name);\n            }\n        },\n\n        start: function(id) {\n            var blobToUpload = upload.getProxyOrBlob(id);\n\n            if (blobToUpload) {\n                return upload.maybeDefer(id, blobToUpload);\n            }\n            else {\n                upload.now(id);\n                return true;\n            }\n        }\n    };\n\n    qq.extend(this, {\n        /**\n         * Adds file or file input to the queue\n         **/\n        add: function(id, file) {\n            handler.add.apply(this, arguments);\n        },\n\n        /**\n         * Sends the file identified by id\n         */\n        upload: function(id) {\n            if (connectionManager.open(id)) {\n                return upload.start(id);\n            }\n            return false;\n        },\n\n        retry: function(id) {\n            // On retry, if concurrent chunking has been enabled, we may have aborted all other in-progress chunks\n            // for a file when encountering a failed chunk upload.  We then signaled the controller to ignore\n            // all failures associated with these aborts.  We are now retrying, so we don't want to ignore\n            // any more failures at this point.\n            if (concurrentChunkingPossible) {\n                handler._getFileState(id).temp.ignoreFailure = false;\n            }\n\n            // If we are attempting to retry a file that is already consuming a connection, this is likely an auto-retry.\n            // Just go ahead and ask the handler to upload again.\n            if (connectionManager.isUsingConnection(id)) {\n                return upload.start(id);\n            }\n\n            // If we are attempting to retry a file that is not currently consuming a connection,\n            // this is likely a manual retry attempt.  We will need to ensure a connection is available\n            // before the retry commences.\n            else {\n                return controller.upload(id);\n            }\n        },\n\n        /**\n         * Cancels file upload by id\n         */\n        cancel: function(id) {\n            var cancelRetVal = handler.cancel(id);\n\n            if (qq.isGenericPromise(cancelRetVal)) {\n                cancelRetVal.then(function() {\n                    upload.cancel(id);\n                });\n            }\n            else if (cancelRetVal !== false) {\n                upload.cancel(id);\n            }\n        },\n\n        /**\n         * Cancels all queued or in-progress uploads\n         */\n        cancelAll: function() {\n            var waitingOrConnected = connectionManager.getWaitingOrConnected(),\n                i;\n\n            // ensure files are cancelled in reverse order which they were added\n            // to avoid a flash of time where a queued file begins to upload before it is canceled\n            if (waitingOrConnected.length) {\n                for (i = waitingOrConnected.length - 1; i >= 0; i--) {\n                    controller.cancel(waitingOrConnected[i]);\n                }\n            }\n\n            connectionManager.reset();\n        },\n\n        // Returns a File, Blob, or the Blob/File for the reference/parent file if the targeted blob is a proxy.\n        // Undefined if no file record is available.\n        getFile: function(id) {\n            if (handler.getProxy && handler.getProxy(id)) {\n                return handler.getProxy(id).referenceBlob;\n            }\n\n            return handler.getFile && handler.getFile(id);\n        },\n\n        // Returns true if the Blob associated with the ID is related to a proxy s\n        isProxied: function(id) {\n            return !!(handler.getProxy && handler.getProxy(id));\n        },\n\n        getInput: function(id) {\n            if (handler.getInput) {\n                return handler.getInput(id);\n            }\n        },\n\n        reset: function() {\n            log(\"Resetting upload handler\");\n            controller.cancelAll();\n            connectionManager.reset();\n            handler.reset();\n        },\n\n        expunge: function(id) {\n            if (controller.isValid(id)) {\n                return handler.expunge(id);\n            }\n        },\n\n        /**\n         * Determine if the file exists.\n         */\n        isValid: function(id) {\n            return handler.isValid(id);\n        },\n\n        getResumableFilesData: function() {\n            if (handler.getResumableFilesData) {\n                return handler.getResumableFilesData();\n            }\n            return [];\n        },\n\n        /**\n         * This may or may not be implemented, depending on the handler.  For handlers where a third-party ID is\n         * available (such as the \"key\" for Amazon S3), this will return that value.  Otherwise, the return value\n         * will be undefined.\n         *\n         * @param id Internal file ID\n         * @returns {*} Some identifier used by a 3rd-party service involved in the upload process\n         */\n        getThirdPartyFileId: function(id) {\n            if (controller.isValid(id)) {\n                return handler.getThirdPartyFileId(id);\n            }\n        },\n\n        /**\n         * Attempts to pause the associated upload if the specific handler supports this and the file is \"valid\".\n         * @param id ID of the upload/file to pause\n         * @returns {boolean} true if the upload was paused\n         */\n        pause: function(id) {\n            if (controller.isResumable(id) && handler.pause && controller.isValid(id) && handler.pause(id)) {\n                connectionManager.free(id);\n                handler.moveInProgressToRemaining(id);\n                return true;\n            }\n            return false;\n        },\n\n        // True if the file is eligible for pause/resume.\n        isResumable: function(id) {\n            return !!handler.isResumable && handler.isResumable(id);\n        }\n    });\n\n    qq.extend(options, o);\n    log = options.log;\n    chunkingPossible = options.chunking.enabled && qq.supportedFeatures.chunking;\n    concurrentChunkingPossible = chunkingPossible && options.chunking.concurrent.enabled;\n\n    preventRetryResponse = (function() {\n        var response = {};\n\n        response[options.preventRetryParam] = true;\n\n        return response;\n    }());\n\n    upload.initHandler();\n};\n", "/*globals qq */\n/*jshint -W117 */\nqq.WindowReceiveMessage = function(o) {\n    \"use strict\";\n\n    var options = {\n            log: function(message, level) {}\n        },\n        callbackWrapperDetachers = {};\n\n    qq.extend(options, o);\n\n    qq.extend(this, {\n        receiveMessage: function(id, callback) {\n            var onMessageCallbackWrapper = function(event) {\n                    callback(event.data);\n                };\n\n            if (window.postMessage) {\n                callbackWrapperDetachers[id] = qq(window).attach(\"message\", onMessageCallbackWrapper);\n            }\n            else {\n                log(\"iframe message passing not supported in this browser!\", \"error\");\n            }\n        },\n\n        stopReceivingMessages: function(id) {\n            if (window.postMessage) {\n                var detacher = callbackWrapperDetachers[id];\n                if (detacher) {\n                    detacher();\n                }\n            }\n        }\n    });\n};\n", "/* globals qq */\n/**\n * Common APIs exposed to creators of upload via form/iframe handlers.  This is reused and possibly overridden\n * in some cases by specific form upload handlers.\n *\n * @constructor\n */\nqq.FormUploadHandler = function(spec) {\n    \"use strict\";\n\n    var options = spec.options,\n        handler = this,\n        proxy = spec.proxy,\n        formHandlerInstanceId = qq.getUniqueId(),\n        onloadCallbacks = {},\n        detachLoadEvents = {},\n        postMessageCallbackTimers = {},\n        isCors = options.isCors,\n        inputName = options.inputName,\n        getUuid = proxy.getUuid,\n        log = proxy.log,\n        corsMessageReceiver = new qq.WindowReceiveMessage({log: log});\n\n    /**\n     * Remove any trace of the file from the handler.\n     *\n     * @param id ID of the associated file\n     */\n    function expungeFile(id) {\n        delete detachLoadEvents[id];\n\n        // If we are dealing with CORS, we might still be waiting for a response from a loaded iframe.\n        // In that case, terminate the timer waiting for a message from the loaded iframe\n        // and stop listening for any more messages coming from this iframe.\n        if (isCors) {\n            clearTimeout(postMessageCallbackTimers[id]);\n            delete postMessageCallbackTimers[id];\n            corsMessageReceiver.stopReceivingMessages(id);\n        }\n\n        var iframe = document.getElementById(handler._getIframeName(id));\n        if (iframe) {\n            // To cancel request set src to something else.  We use src=\"javascript:false;\"\n            // because it doesn't trigger ie6 prompt on https\n            /* jshint scripturl:true */\n            iframe.setAttribute(\"src\", \"javascript:false;\");\n\n            qq(iframe).remove();\n        }\n    }\n\n    /**\n     * @param iframeName `document`-unique Name of the associated iframe\n     * @returns {*} ID of the associated file\n     */\n    function getFileIdForIframeName(iframeName) {\n        return iframeName.split(\"_\")[0];\n    }\n\n    /**\n     * Generates an iframe to be used as a target for upload-related form submits.  This also adds the iframe\n     * to the current `document`.  Note that the iframe is hidden from view.\n     *\n     * @param name Name of the iframe.\n     * @returns {HTMLIFrameElement} The created iframe\n     */\n    function initIframeForUpload(name) {\n        var iframe = qq.toElement(\"<iframe src='javascript:false;' name='\" + name + \"' />\");\n\n        iframe.setAttribute(\"id\", name);\n\n        iframe.style.display = \"none\";\n        document.body.appendChild(iframe);\n\n        return iframe;\n    }\n\n    /**\n     * If we are in CORS mode, we must listen for messages (containing the server response) from the associated\n     * iframe, since we cannot directly parse the content of the iframe due to cross-origin restrictions.\n     *\n     * @param iframe Listen for messages on this iframe.\n     * @param callback Invoke this callback with the message from the iframe.\n     */\n    function registerPostMessageCallback(iframe, callback) {\n        var iframeName = iframe.id,\n            fileId = getFileIdForIframeName(iframeName),\n            uuid = getUuid(fileId);\n\n        onloadCallbacks[uuid] = callback;\n\n        // When the iframe has loaded (after the server responds to an upload request)\n        // declare the attempt a failure if we don't receive a valid message shortly after the response comes in.\n        detachLoadEvents[fileId] = qq(iframe).attach(\"load\", function() {\n            if (handler.getInput(fileId)) {\n                log(\"Received iframe load event for CORS upload request (iframe name \" + iframeName + \")\");\n\n                postMessageCallbackTimers[iframeName] = setTimeout(function() {\n                    var errorMessage = \"No valid message received from loaded iframe for iframe name \" + iframeName;\n                    log(errorMessage, \"error\");\n                    callback({\n                        error: errorMessage\n                    });\n                }, 1000);\n            }\n        });\n\n        // Listen for messages coming from this iframe.  When a message has been received, cancel the timer\n        // that declares the upload a failure if a message is not received within a reasonable amount of time.\n        corsMessageReceiver.receiveMessage(iframeName, function(message) {\n            log(\"Received the following window message: '\" + message + \"'\");\n            var fileId = getFileIdForIframeName(iframeName),\n                response = handler._parseJsonResponse(message),\n                uuid = response.uuid,\n                onloadCallback;\n\n            if (uuid && onloadCallbacks[uuid]) {\n                log(\"Handling response for iframe name \" + iframeName);\n                clearTimeout(postMessageCallbackTimers[iframeName]);\n                delete postMessageCallbackTimers[iframeName];\n\n                handler._detachLoadEvent(iframeName);\n\n                onloadCallback = onloadCallbacks[uuid];\n\n                delete onloadCallbacks[uuid];\n                corsMessageReceiver.stopReceivingMessages(iframeName);\n                onloadCallback(response);\n            }\n            else if (!uuid) {\n                log(\"'\" + message + \"' does not contain a UUID - ignoring.\");\n            }\n        });\n    }\n\n    qq.extend(this, new qq.UploadHandler(spec));\n\n    qq.override(this, function(super_) {\n        return {\n            /**\n             * Adds File or Blob to the queue\n             **/\n            add: function(id, fileInput) {\n                super_.add(id, {input: fileInput});\n\n                fileInput.setAttribute(\"name\", inputName);\n\n                // remove file input from DOM\n                if (fileInput.parentNode) {\n                    qq(fileInput).remove();\n                }\n            },\n\n            expunge: function(id) {\n                expungeFile(id);\n                super_.expunge(id);\n            },\n\n            isValid: function(id) {\n                return super_.isValid(id) &&\n                    handler._getFileState(id).input !== undefined;\n            }\n        };\n    });\n\n    qq.extend(this, {\n        getInput: function(id) {\n            return handler._getFileState(id).input;\n        },\n\n        /**\n         * This function either delegates to a more specific message handler if CORS is involved,\n         * or simply registers a callback when the iframe has been loaded that invokes the passed callback\n         * after determining if the content of the iframe is accessible.\n         *\n         * @param iframe Associated iframe\n         * @param callback Callback to invoke after we have determined if the iframe content is accessible.\n         */\n        _attachLoadEvent: function(iframe, callback) {\n            /*jslint eqeq: true*/\n            var responseDescriptor;\n\n            if (isCors) {\n                registerPostMessageCallback(iframe, callback);\n            }\n            else {\n                detachLoadEvents[iframe.id] = qq(iframe).attach(\"load\", function() {\n                    log(\"Received response for \" + iframe.id);\n\n                    // when we remove iframe from dom\n                    // the request stops, but in IE load\n                    // event fires\n                    if (!iframe.parentNode) {\n                        return;\n                    }\n\n                    try {\n                        // fixing Opera 10.53\n                        if (iframe.contentDocument &&\n                            iframe.contentDocument.body &&\n                            iframe.contentDocument.body.innerHTML == \"false\") {\n                            // In Opera event is fired second time\n                            // when body.innerHTML changed from false\n                            // to server response approx. after 1 sec\n                            // when we upload file with iframe\n                            return;\n                        }\n                    }\n                    catch (error) {\n                        //IE may throw an \"access is denied\" error when attempting to access contentDocument on the iframe in some cases\n                        log(\"Error when attempting to access iframe during handling of upload response (\" + error.message + \")\", \"error\");\n                        responseDescriptor = {success: false};\n                    }\n\n                    callback(responseDescriptor);\n                });\n            }\n        },\n\n        /**\n         * Creates an iframe with a specific document-unique name.\n         *\n         * @param id ID of the associated file\n         * @returns {HTMLIFrameElement}\n         */\n        _createIframe: function(id) {\n            var iframeName = handler._getIframeName(id);\n\n            return initIframeForUpload(iframeName);\n        },\n\n        /**\n         * Called when we are no longer interested in being notified when an iframe has loaded.\n         *\n         * @param id Associated file ID\n         */\n        _detachLoadEvent: function(id) {\n            if (detachLoadEvents[id] !== undefined) {\n                detachLoadEvents[id]();\n                delete detachLoadEvents[id];\n            }\n        },\n\n        /**\n         * @param fileId ID of the associated file\n         * @returns {string} The `document`-unique name of the iframe\n         */\n        _getIframeName: function(fileId) {\n            return fileId + \"_\" + formHandlerInstanceId;\n        },\n\n        /**\n         * Generates a form element and appends it to the `document`.  When the form is submitted, a specific iframe is targeted.\n         * The name of the iframe is passed in as a property of the spec parameter, and must be unique in the `document`.  Note\n         * that the form is hidden from view.\n         *\n         * @param spec An object containing various properties to be used when constructing the form.  Required properties are\n         * currently: `method`, `endpoint`, `params`, `paramsInBody`, and `targetName`.\n         * @returns {HTMLFormElement} The created form\n         */\n        _initFormForUpload: function(spec) {\n            var method = spec.method,\n                endpoint = spec.endpoint,\n                params = spec.params,\n                paramsInBody = spec.paramsInBody,\n                targetName = spec.targetName,\n                form = qq.toElement(\"<form method='\" + method + \"' enctype='multipart/form-data'></form>\"),\n                url = endpoint;\n\n            if (paramsInBody) {\n                qq.obj2Inputs(params, form);\n            }\n            else {\n                url = qq.obj2url(params, endpoint);\n            }\n\n            form.setAttribute(\"action\", url);\n            form.setAttribute(\"target\", targetName);\n            form.style.display = \"none\";\n            document.body.appendChild(form);\n\n            return form;\n        },\n\n        /**\n         * @param innerHtmlOrMessage JSON message\n         * @returns {*} The parsed response, or an empty object if the response could not be parsed\n         */\n        _parseJsonResponse: function(innerHtmlOrMessage) {\n            var response = {};\n\n            try {\n                response = qq.parseJson(innerHtmlOrMessage);\n            }\n            catch (error) {\n                log(\"Error when attempting to parse iframe upload response (\" + error.message + \")\", \"error\");\n            }\n\n            return response;\n        }\n    });\n};\n", "/* globals qq */\n/**\n * Common API exposed to creators of XHR handlers.  This is reused and possibly overriding in some cases by specific\n * XHR upload handlers.\n *\n * @constructor\n */\nqq.XhrUploadHandler = function(spec) {\n    \"use strict\";\n\n    var handler = this,\n        namespace = spec.options.namespace,\n        proxy = spec.proxy,\n        chunking = spec.options.chunking,\n        resume = spec.options.resume,\n        chunkFiles = chunking && spec.options.chunking.enabled && qq.supportedFeatures.chunking,\n        resumeEnabled = resume && spec.options.resume.enabled && chunkFiles && qq.supportedFeatures.resume,\n        getName = proxy.getName,\n        getSize = proxy.getSize,\n        getUuid = proxy.getUuid,\n        getEndpoint = proxy.getEndpoint,\n        getDataByUuid = proxy.getDataByUuid,\n        onUuidChanged = proxy.onUuidChanged,\n        onProgress = proxy.onProgress,\n        log = proxy.log;\n\n    function abort(id) {\n        qq.each(handler._getXhrs(id), function(xhrId, xhr) {\n            var ajaxRequester = handler._getAjaxRequester(id, xhrId);\n\n            xhr.onreadystatechange = null;\n            xhr.upload.onprogress = null;\n            xhr.abort();\n            ajaxRequester && ajaxRequester.canceled && ajaxRequester.canceled(id);\n        });\n    }\n\n    qq.extend(this, new qq.UploadHandler(spec));\n\n    qq.override(this, function(super_) {\n        return {\n            /**\n             * Adds File or Blob to the queue\n             **/\n            add: function(id, blobOrProxy) {\n                if (qq.isFile(blobOrProxy) || qq.isBlob(blobOrProxy)) {\n                    super_.add(id, {file: blobOrProxy});\n                }\n                else if (blobOrProxy instanceof qq.BlobProxy) {\n                    super_.add(id, {proxy: blobOrProxy});\n                }\n                else {\n                    throw new Error(\"Passed obj is not a File, Blob, or proxy\");\n                }\n\n                handler._initTempState(id);\n                resumeEnabled && handler._maybePrepareForResume(id);\n            },\n\n            expunge: function(id) {\n                abort(id);\n                handler._maybeDeletePersistedChunkData(id);\n                handler._clearXhrs(id);\n                super_.expunge(id);\n            }\n        };\n    });\n\n    qq.extend(this, {\n        // Clear the cached chunk `Blob` after we are done with it, just in case the `Blob` bytes are stored in memory.\n        clearCachedChunk: function(id, chunkIdx) {\n            delete handler._getFileState(id).temp.cachedChunks[chunkIdx];\n        },\n\n        clearXhr: function(id, chunkIdx) {\n            var tempState = handler._getFileState(id).temp;\n\n            if (tempState.xhrs) {\n                delete tempState.xhrs[chunkIdx];\n            }\n            if (tempState.ajaxRequesters) {\n                delete tempState.ajaxRequesters[chunkIdx];\n            }\n        },\n\n        // Called when all chunks have been successfully uploaded.  Expected promissory return type.\n        // This defines the default behavior if nothing further is required when all chunks have been uploaded.\n        finalizeChunks: function(id, responseParser) {\n            var lastChunkIdx = handler._getTotalChunks(id) - 1,\n                xhr = handler._getXhr(id, lastChunkIdx);\n\n            if (responseParser) {\n                return new qq.Promise().success(responseParser(xhr), xhr);\n            }\n\n            return new qq.Promise().success({}, xhr);\n        },\n\n        getFile: function(id) {\n            return handler.isValid(id) && handler._getFileState(id).file;\n        },\n\n        getProxy: function(id) {\n            return handler.isValid(id) && handler._getFileState(id).proxy;\n        },\n\n        /**\n         * @returns {Array} Array of objects containing properties useful to integrators\n         * when it is important to determine which files are potentially resumable.\n         */\n        getResumableFilesData: function() {\n            var resumableFilesData = [];\n\n            handler._iterateResumeRecords(function(key, uploadData) {\n                handler.moveInProgressToRemaining(null, uploadData.chunking.inProgress,  uploadData.chunking.remaining);\n\n                var data = {\n                    name: uploadData.name,\n                    remaining: uploadData.chunking.remaining,\n                    size: uploadData.size,\n                    uuid: uploadData.uuid\n                };\n\n                if (uploadData.key) {\n                    data.key = uploadData.key;\n                }\n\n                resumableFilesData.push(data);\n            });\n\n            return resumableFilesData;\n        },\n\n        isResumable: function(id) {\n            return !!chunking && handler.isValid(id) && !handler._getFileState(id).notResumable;\n        },\n\n        moveInProgressToRemaining: function(id, optInProgress, optRemaining) {\n            var inProgress = optInProgress || handler._getFileState(id).chunking.inProgress,\n                remaining = optRemaining || handler._getFileState(id).chunking.remaining;\n\n            if (inProgress) {\n                log(qq.format(\"Moving these chunks from in-progress {}, to remaining.\", JSON.stringify(inProgress)));\n                inProgress.reverse();\n                qq.each(inProgress, function(idx, chunkIdx) {\n                    remaining.unshift(chunkIdx);\n                });\n                inProgress.length = 0;\n            }\n        },\n\n        pause: function(id) {\n            if (handler.isValid(id)) {\n                log(qq.format(\"Aborting XHR upload for {} '{}' due to pause instruction.\", id, getName(id)));\n                handler._getFileState(id).paused = true;\n                abort(id);\n                return true;\n            }\n        },\n\n        reevaluateChunking: function(id) {\n            if (chunking && handler.isValid(id)) {\n                var state = handler._getFileState(id),\n                    totalChunks,\n                    i;\n\n                delete state.chunking;\n\n                state.chunking = {};\n                totalChunks = handler._getTotalChunks(id);\n                if (totalChunks > 1 || chunking.mandatory) {\n                    state.chunking.enabled = true;\n                    state.chunking.parts = totalChunks;\n                    state.chunking.remaining = [];\n\n                    for (i = 0; i < totalChunks; i++) {\n                        state.chunking.remaining.push(i);\n                    }\n\n                    handler._initTempState(id);\n                }\n                else {\n                    state.chunking.enabled = false;\n                }\n            }\n        },\n\n        updateBlob: function(id, newBlob) {\n            if (handler.isValid(id)) {\n                handler._getFileState(id).file = newBlob;\n            }\n        },\n\n        _clearXhrs: function(id) {\n            var tempState = handler._getFileState(id).temp;\n\n            qq.each(tempState.ajaxRequesters, function(chunkId) {\n                delete tempState.ajaxRequesters[chunkId];\n            });\n\n            qq.each(tempState.xhrs, function(chunkId) {\n                delete tempState.xhrs[chunkId];\n            });\n        },\n\n        /**\n         * Creates an XHR instance for this file and stores it in the fileState.\n         *\n         * @param id File ID\n         * @param optChunkIdx The chunk index associated with this XHR, if applicable\n         * @returns {XMLHttpRequest}\n         */\n        _createXhr: function(id, optChunkIdx) {\n            return handler._registerXhr(id, optChunkIdx, qq.createXhrInstance());\n        },\n\n        _getAjaxRequester: function(id, optChunkIdx) {\n            var chunkIdx = optChunkIdx == null ? -1 : optChunkIdx;\n            return handler._getFileState(id).temp.ajaxRequesters[chunkIdx];\n        },\n\n        _getChunkData: function(id, chunkIndex) {\n            var chunkSize = chunking.partSize,\n                fileSize = getSize(id),\n                fileOrBlob = handler.getFile(id),\n                startBytes = chunkSize * chunkIndex,\n                endBytes = startBytes + chunkSize >= fileSize ? fileSize : startBytes + chunkSize,\n                totalChunks = handler._getTotalChunks(id),\n                cachedChunks = this._getFileState(id).temp.cachedChunks,\n\n            // To work around a Webkit GC bug, we must keep each chunk `Blob` in scope until we are done with it.\n            // See https://github.com/Widen/fine-uploader/issues/937#issuecomment-41418760\n                blob = cachedChunks[chunkIndex] || qq.sliceBlob(fileOrBlob, startBytes, endBytes);\n\n            cachedChunks[chunkIndex] = blob;\n\n            return {\n                part: chunkIndex,\n                start: startBytes,\n                end: endBytes,\n                count: totalChunks,\n                blob: blob,\n                size: endBytes - startBytes\n            };\n        },\n\n        _getChunkDataForCallback: function(chunkData) {\n            return {\n                partIndex: chunkData.part,\n                startByte: chunkData.start + 1,\n                endByte: chunkData.end,\n                totalParts: chunkData.count\n            };\n        },\n\n        /**\n         * @param id File ID\n         * @returns {string} Identifier for this item that may appear in the browser's local storage\n         */\n        _getLocalStorageId: function(id) {\n            var formatVersion = \"5.0\",\n                name = getName(id),\n                size = getSize(id),\n                chunkSize = chunking.partSize,\n                endpoint = getEndpoint(id);\n\n            return qq.format(\"qq{}resume{}-{}-{}-{}-{}\", namespace, formatVersion, name, size, chunkSize, endpoint);\n        },\n\n        _getMimeType: function(id) {\n            return handler.getFile(id).type;\n        },\n\n        _getPersistableData: function(id) {\n            return handler._getFileState(id).chunking;\n        },\n\n        /**\n         * @param id ID of the associated file\n         * @returns {number} Number of parts this file can be divided into, or undefined if chunking is not supported in this UA\n         */\n        _getTotalChunks: function(id) {\n            if (chunking) {\n                var fileSize = getSize(id),\n                    chunkSize = chunking.partSize;\n\n                return Math.ceil(fileSize / chunkSize);\n            }\n        },\n\n        _getXhr: function(id, optChunkIdx) {\n            var chunkIdx = optChunkIdx == null ? -1 : optChunkIdx;\n            return handler._getFileState(id).temp.xhrs[chunkIdx];\n        },\n\n        _getXhrs: function(id) {\n            return handler._getFileState(id).temp.xhrs;\n        },\n\n        // Iterates through all XHR handler-created resume records (in local storage),\n        // invoking the passed callback and passing in the key and value of each local storage record.\n        _iterateResumeRecords: function(callback) {\n            if (resumeEnabled) {\n                qq.each(localStorage, function(key, item) {\n                    if (key.indexOf(qq.format(\"qq{}resume\", namespace)) === 0) {\n                        var uploadData = JSON.parse(item);\n                        callback(key, uploadData);\n                    }\n                });\n            }\n        },\n\n        _initTempState: function(id) {\n            handler._getFileState(id).temp = {\n                ajaxRequesters: {},\n                chunkProgress: {},\n                xhrs: {},\n                cachedChunks: {}\n            };\n        },\n\n        _markNotResumable: function(id) {\n            handler._getFileState(id).notResumable = true;\n        },\n\n        // Removes a chunked upload record from local storage, if possible.\n        // Returns true if the item was removed, false otherwise.\n        _maybeDeletePersistedChunkData: function(id) {\n            var localStorageId;\n\n            if (resumeEnabled && handler.isResumable(id)) {\n                localStorageId = handler._getLocalStorageId(id);\n\n                if (localStorageId && localStorage.getItem(localStorageId)) {\n                    localStorage.removeItem(localStorageId);\n                    return true;\n                }\n            }\n\n            return false;\n        },\n\n        // If this is a resumable upload, grab the relevant data from storage and items in memory that track this upload\n        // so we can pick up from where we left off.\n        _maybePrepareForResume: function(id) {\n            var state = handler._getFileState(id),\n                localStorageId, persistedData;\n\n            // Resume is enabled and possible and this is the first time we've tried to upload this file in this session,\n            // so prepare for a resume attempt.\n            if (resumeEnabled && state.key === undefined) {\n                localStorageId = handler._getLocalStorageId(id);\n                persistedData = localStorage.getItem(localStorageId);\n\n                // If we found this item in local storage, maybe we should resume it.\n                if (persistedData) {\n                    persistedData = JSON.parse(persistedData);\n\n                    // If we found a resume record but we have already handled this file in this session,\n                    // don't try to resume it & ensure we don't persist future check data\n                    if (getDataByUuid(persistedData.uuid)) {\n                        handler._markNotResumable(id);\n                    }\n                    else {\n                        log(qq.format(\"Identified file with ID {} and name of {} as resumable.\", id, getName(id)));\n\n                        onUuidChanged(id, persistedData.uuid);\n\n                        state.key = persistedData.key;\n                        state.chunking = persistedData.chunking;\n                        state.loaded = persistedData.loaded;\n                        state.attemptingResume = true;\n\n                        handler.moveInProgressToRemaining(id);\n                    }\n                }\n            }\n        },\n\n        // Persist any data needed to resume this upload in a new session.\n        _maybePersistChunkedState: function(id) {\n            var state = handler._getFileState(id),\n                localStorageId, persistedData;\n\n            // If local storage isn't supported by the browser, or if resume isn't enabled or possible, give up\n            if (resumeEnabled && handler.isResumable(id)) {\n                localStorageId = handler._getLocalStorageId(id);\n\n                persistedData = {\n                    name: getName(id),\n                    size: getSize(id),\n                    uuid: getUuid(id),\n                    key: state.key,\n                    chunking: state.chunking,\n                    loaded: state.loaded,\n                    lastUpdated: Date.now()\n                };\n\n                try {\n                    localStorage.setItem(localStorageId, JSON.stringify(persistedData));\n                }\n                catch (error) {\n                    log(qq.format(\"Unable to save resume data for '{}' due to error: '{}'.\", id, error.toString()), \"warn\");\n                }\n            }\n        },\n\n        _registerProgressHandler: function(id, chunkIdx, chunkSize) {\n            var xhr = handler._getXhr(id, chunkIdx),\n                name = getName(id),\n                progressCalculator = {\n                    simple: function(loaded, total) {\n                        var fileSize = getSize(id);\n\n                        if (loaded === total) {\n                            onProgress(id, name, fileSize, fileSize);\n                        }\n                        else {\n                            onProgress(id, name, (loaded >= fileSize ? fileSize - 1 : loaded), fileSize);\n                        }\n                    },\n\n                    chunked: function(loaded, total) {\n                        var chunkProgress = handler._getFileState(id).temp.chunkProgress,\n                            totalSuccessfullyLoadedForFile = handler._getFileState(id).loaded,\n                            loadedForRequest = loaded,\n                            totalForRequest = total,\n                            totalFileSize = getSize(id),\n                            estActualChunkLoaded = loadedForRequest - (totalForRequest - chunkSize),\n                            totalLoadedForFile = totalSuccessfullyLoadedForFile;\n\n                        chunkProgress[chunkIdx] = estActualChunkLoaded;\n\n                        qq.each(chunkProgress, function(chunkIdx, chunkLoaded) {\n                            totalLoadedForFile += chunkLoaded;\n                        });\n\n                        onProgress(id, name, totalLoadedForFile, totalFileSize);\n                    }\n                };\n\n            xhr.upload.onprogress = function(e) {\n                if (e.lengthComputable) {\n                    /* jshint eqnull: true */\n                    var type = chunkSize == null ? \"simple\" : \"chunked\";\n                    progressCalculator[type](e.loaded, e.total);\n                }\n            };\n        },\n\n        /**\n         * Registers an XHR transport instance created elsewhere.\n         *\n         * @param id ID of the associated file\n         * @param optChunkIdx The chunk index associated with this XHR, if applicable\n         * @param xhr XMLHttpRequest object instance\n         * @param optAjaxRequester `qq.AjaxRequester` associated with this request, if applicable.\n         * @returns {XMLHttpRequest}\n         */\n        _registerXhr: function(id, optChunkIdx, xhr, optAjaxRequester) {\n            var xhrsId = optChunkIdx == null ? -1 : optChunkIdx,\n                tempState = handler._getFileState(id).temp;\n\n            tempState.xhrs = tempState.xhrs || {};\n            tempState.ajaxRequesters = tempState.ajaxRequesters || {};\n\n            tempState.xhrs[xhrsId] = xhr;\n\n            if (optAjaxRequester) {\n                tempState.ajaxRequesters[xhrsId] = optAjaxRequester;\n            }\n\n            return xhr;\n        },\n\n        // Deletes any local storage records that are \"expired\".\n        _removeExpiredChunkingRecords: function() {\n            var expirationDays = resume.recordsExpireIn;\n\n            handler._iterateResumeRecords(function(key, uploadData) {\n                var expirationDate = new Date(uploadData.lastUpdated);\n\n                // transform updated date into expiration date\n                expirationDate.setDate(expirationDate.getDate() + expirationDays);\n\n                if (expirationDate.getTime() <= Date.now()) {\n                    log(\"Removing expired resume record with key \" + key);\n                    localStorage.removeItem(key);\n                }\n            });\n        },\n\n        /**\n         * Determine if the associated file should be chunked.\n         *\n         * @param id ID of the associated file\n         * @returns {*} true if chunking is enabled, possible, and the file can be split into more than 1 part\n         */\n        _shouldChunkThisFile: function(id) {\n            var state = handler._getFileState(id);\n\n            if (!state.chunking) {\n                handler.reevaluateChunking(id);\n            }\n\n            return state.chunking.enabled;\n        }\n    });\n};\n", "/*globals qq, XMLHttpRequest*/\nqq.DeleteFileAjaxRequester = function(o) {\n    \"use strict\";\n\n    var requester,\n        options = {\n            method: \"DELETE\",\n            uuidParamName: \"qquuid\",\n            endpointStore: {},\n            maxConnections: 3,\n            customHeaders: function(id) {return {};},\n            paramsStore: {},\n            cors: {\n                expected: false,\n                sendCredentials: false\n            },\n            log: function(str, level) {},\n            onDelete: function(id) {},\n            onDeleteComplete: function(id, xhrOrXdr, isError) {}\n        };\n\n    qq.extend(options, o);\n\n    function getMandatedParams() {\n        if (options.method.toUpperCase() === \"POST\") {\n            return {\n                _method: \"DELETE\"\n            };\n        }\n\n        return {};\n    }\n\n    requester = qq.extend(this, new qq.AjaxRequester({\n        acceptHeader: \"application/json\",\n        validMethods: [\"POST\", \"DELETE\"],\n        method: options.method,\n        endpointStore: options.endpointStore,\n        paramsStore: options.paramsStore,\n        mandatedParams: getMandatedParams(),\n        maxConnections: options.maxConnections,\n        customHeaders: function(id) {\n            return options.customHeaders.get(id);\n        },\n        log: options.log,\n        onSend: options.onDelete,\n        onComplete: options.onDeleteComplete,\n        cors: options.cors\n    }));\n\n    qq.extend(this, {\n        sendDelete: function(id, uuid, additionalMandatedParams) {\n            var additionalOptions = additionalMandatedParams || {};\n\n            options.log(\"Submitting delete file request for \" + id);\n\n            if (options.method === \"DELETE\") {\n                requester.initTransport(id)\n                    .withPath(uuid)\n                    .withParams(additionalOptions)\n                    .send();\n            }\n            else {\n                additionalOptions[options.uuidParamName] = uuid;\n                requester.initTransport(id)\n                    .withParams(additionalOptions)\n                    .send();\n            }\n        }\n    });\n};\n", "/*global qq, define */\n/*jshint strict:false,bitwise:false,nonew:false,asi:true,-W064,-W116,-W089 */\n/**\n * Mega pixel image rendering library for iOS6+\n *\n * Fixes iOS6+'s image file rendering issue for large size image (over mega-pixel),\n * which causes unexpected subsampling when drawing it in canvas.\n * By using this library, you can safely render the image with proper stretching.\n *\n * Copyright (c) 2012 Shinichi Tomita <<EMAIL>>\n * Released under the MIT license\n *\n * Heavily modified by Widen for Fine Uploader\n */\n(function() {\n\n    /**\n     * Detect subsampling in loaded image.\n     * In iOS, larger images than 2M pixels may be subsampled in rendering.\n     */\n    function detectSubsampling(img) {\n        var iw = img.naturalWidth,\n            ih = img.naturalHeight,\n            canvas = document.createElement(\"canvas\"),\n            ctx;\n\n        if (iw * ih > 1024 * 1024) { // subsampling may happen over megapixel image\n            canvas.width = canvas.height = 1;\n            ctx = canvas.getContext(\"2d\");\n            ctx.drawImage(img, -iw + 1, 0);\n            // subsampled image becomes half smaller in rendering size.\n            // check alpha channel value to confirm image is covering edge pixel or not.\n            // if alpha value is 0 image is not covering, hence subsampled.\n            return ctx.getImageData(0, 0, 1, 1).data[3] === 0;\n        } else {\n            return false;\n        }\n    }\n\n    /**\n     * Detecting vertical squash in loaded image.\n     * Fixes a bug which squash image vertically while drawing into canvas for some images.\n     */\n    function detectVerticalSquash(img, iw, ih) {\n        var canvas = document.createElement(\"canvas\"),\n            sy = 0,\n            ey = ih,\n            py = ih,\n            ctx, data, alpha, ratio;\n\n        canvas.width = 1;\n        canvas.height = ih;\n        ctx = canvas.getContext(\"2d\");\n        ctx.drawImage(img, 0, 0);\n        data = ctx.getImageData(0, 0, 1, ih).data;\n\n        // search image edge pixel position in case it is squashed vertically.\n        while (py > sy) {\n            alpha = data[(py - 1) * 4 + 3];\n            if (alpha === 0) {\n                ey = py;\n            } else {\n                sy = py;\n            }\n            py = (ey + sy) >> 1;\n        }\n\n        ratio = (py / ih);\n        return (ratio === 0) ? 1 : ratio;\n    }\n\n    /**\n     * Rendering image element (with resizing) and get its data URL\n     */\n    function renderImageToDataURL(img, blob, options, doSquash) {\n        var canvas = document.createElement(\"canvas\"),\n            mime = options.mime || \"image/jpeg\",\n            promise = new qq.Promise();\n\n        renderImageToCanvas(img, blob, canvas, options, doSquash)\n            .then(function() {\n                promise.success(\n                    canvas.toDataURL(mime, options.quality || 0.8)\n                );\n            });\n\n        return promise;\n    }\n\n    function maybeCalculateDownsampledDimensions(spec) {\n        var maxPixels = 5241000; //iOS specific value\n\n        if (!qq.ios()) {\n            throw new qq.Error(\"Downsampled dimensions can only be reliably calculated for iOS!\");\n        }\n\n        if (spec.origHeight * spec.origWidth > maxPixels) {\n            return {\n                newHeight: Math.round(Math.sqrt(maxPixels * (spec.origHeight / spec.origWidth))),\n                newWidth: Math.round(Math.sqrt(maxPixels * (spec.origWidth / spec.origHeight)))\n            };\n        }\n    }\n\n    /**\n     * Rendering image element (with resizing) into the canvas element\n     */\n    function renderImageToCanvas(img, blob, canvas, options, doSquash) {\n        var iw = img.naturalWidth,\n            ih = img.naturalHeight,\n            width = options.width,\n            height = options.height,\n            ctx = canvas.getContext(\"2d\"),\n            promise = new qq.Promise(),\n            modifiedDimensions;\n\n        ctx.save();\n\n        if (options.resize) {\n            return renderImageToCanvasWithCustomResizer({\n                blob: blob,\n                canvas: canvas,\n                image: img,\n                imageHeight: ih,\n                imageWidth: iw,\n                orientation: options.orientation,\n                resize: options.resize,\n                targetHeight: height,\n                targetWidth: width\n            });\n        }\n\n        if (!qq.supportedFeatures.unlimitedScaledImageSize) {\n            modifiedDimensions = maybeCalculateDownsampledDimensions({\n                origWidth: width,\n                origHeight: height\n            });\n\n            if (modifiedDimensions) {\n                qq.log(qq.format(\"Had to reduce dimensions due to device limitations from {}w / {}h to {}w / {}h\",\n                    width, height, modifiedDimensions.newWidth, modifiedDimensions.newHeight),\n                    \"warn\");\n\n                width = modifiedDimensions.newWidth;\n                height = modifiedDimensions.newHeight;\n            }\n        }\n\n        transformCoordinate(canvas, width, height, options.orientation);\n\n        // Fine Uploader specific: Save some CPU cycles if not using iOS\n        // Assumption: This logic is only needed to overcome iOS image sampling issues\n        if (qq.ios()) {\n            (function() {\n                if (detectSubsampling(img)) {\n                    iw /= 2;\n                    ih /= 2;\n                }\n\n                var d = 1024, // size of tiling canvas\n                    tmpCanvas = document.createElement(\"canvas\"),\n                    vertSquashRatio = doSquash ? detectVerticalSquash(img, iw, ih) : 1,\n                    dw = Math.ceil(d * width / iw),\n                    dh = Math.ceil(d * height / ih / vertSquashRatio),\n                    sy = 0,\n                    dy = 0,\n                    tmpCtx, sx, dx;\n\n                tmpCanvas.width = tmpCanvas.height = d;\n                tmpCtx = tmpCanvas.getContext(\"2d\");\n\n                while (sy < ih) {\n                    sx = 0;\n                    dx = 0;\n                    while (sx < iw) {\n                        tmpCtx.clearRect(0, 0, d, d);\n                        tmpCtx.drawImage(img, -sx, -sy);\n                        ctx.drawImage(tmpCanvas, 0, 0, d, d, dx, dy, dw, dh);\n                        sx += d;\n                        dx += dw;\n                    }\n                    sy += d;\n                    dy += dh;\n                }\n                ctx.restore();\n                tmpCanvas = tmpCtx = null;\n            }());\n        }\n        else {\n            ctx.drawImage(img, 0, 0, width, height);\n        }\n\n        canvas.qqImageRendered && canvas.qqImageRendered();\n        promise.success();\n\n        return promise;\n    }\n\n    function renderImageToCanvasWithCustomResizer(resizeInfo) {\n        var blob = resizeInfo.blob,\n            image = resizeInfo.image,\n            imageHeight = resizeInfo.imageHeight,\n            imageWidth = resizeInfo.imageWidth,\n            orientation = resizeInfo.orientation,\n            promise = new qq.Promise(),\n            resize = resizeInfo.resize,\n            sourceCanvas = document.createElement(\"canvas\"),\n            sourceCanvasContext = sourceCanvas.getContext(\"2d\"),\n            targetCanvas = resizeInfo.canvas,\n            targetHeight = resizeInfo.targetHeight,\n            targetWidth = resizeInfo.targetWidth;\n\n        transformCoordinate(sourceCanvas, imageWidth, imageHeight, orientation);\n\n        targetCanvas.height = targetHeight;\n        targetCanvas.width = targetWidth;\n\n        sourceCanvasContext.drawImage(image, 0, 0);\n\n        resize({\n            blob: blob,\n            height: targetHeight,\n            image: image,\n            sourceCanvas: sourceCanvas,\n            targetCanvas: targetCanvas,\n            width: targetWidth\n        })\n            .then(\n                function success() {\n                    targetCanvas.qqImageRendered && targetCanvas.qqImageRendered();\n                    promise.success();\n                },\n                promise.failure\n            );\n\n        return promise;\n    }\n\n    /**\n     * Transform canvas coordination according to specified frame size and orientation\n     * Orientation value is from EXIF tag\n     */\n    function transformCoordinate(canvas, width, height, orientation) {\n        switch (orientation) {\n            case 5:\n            case 6:\n            case 7:\n            case 8:\n                canvas.width = height;\n                canvas.height = width;\n                break;\n            default:\n                canvas.width = width;\n                canvas.height = height;\n        }\n        var ctx = canvas.getContext(\"2d\");\n        switch (orientation) {\n            case 2:\n                // horizontal flip\n                ctx.translate(width, 0);\n                ctx.scale(-1, 1);\n                break;\n            case 3:\n                // 180 rotate left\n                ctx.translate(width, height);\n                ctx.rotate(Math.PI);\n                break;\n            case 4:\n                // vertical flip\n                ctx.translate(0, height);\n                ctx.scale(1, -1);\n                break;\n            case 5:\n                // vertical flip + 90 rotate right\n                ctx.rotate(0.5 * Math.PI);\n                ctx.scale(1, -1);\n                break;\n            case 6:\n                // 90 rotate right\n                ctx.rotate(0.5 * Math.PI);\n                ctx.translate(0, -height);\n                break;\n            case 7:\n                // horizontal flip + 90 rotate right\n                ctx.rotate(0.5 * Math.PI);\n                ctx.translate(width, -height);\n                ctx.scale(-1, 1);\n                break;\n            case 8:\n                // 90 rotate left\n                ctx.rotate(-0.5 * Math.PI);\n                ctx.translate(-width, 0);\n                break;\n            default:\n                break;\n        }\n    }\n\n    /**\n     * MegaPixImage class\n     */\n    function MegaPixImage(srcImage, errorCallback) {\n        var self = this;\n\n        if (window.Blob && srcImage instanceof Blob) {\n            (function() {\n                var img = new Image(),\n                    URL = window.URL && window.URL.createObjectURL ? window.URL :\n                        window.webkitURL && window.webkitURL.createObjectURL ? window.webkitURL : null;\n                if (!URL) { throw Error(\"No createObjectURL function found to create blob url\"); }\n                img.src = URL.createObjectURL(srcImage);\n                self.blob = srcImage;\n                srcImage = img;\n            }());\n        }\n        if (!srcImage.naturalWidth && !srcImage.naturalHeight) {\n            srcImage.onload = function() {\n                var listeners = self.imageLoadListeners;\n                if (listeners) {\n                    self.imageLoadListeners = null;\n                    // IE11 doesn't reliably report actual image dimensions immediately after onload for small files,\n                    // so let's push this to the end of the UI thread queue.\n                    setTimeout(function() {\n                        for (var i = 0, len = listeners.length; i < len; i++) {\n                            listeners[i]();\n                        }\n                    }, 0);\n                }\n            };\n            srcImage.onerror = errorCallback;\n            this.imageLoadListeners = [];\n        }\n        this.srcImage = srcImage;\n    }\n\n    /**\n     * Rendering megapix image into specified target element\n     */\n    MegaPixImage.prototype.render = function(target, options) {\n        options = options || {};\n\n        var self = this,\n            imgWidth = this.srcImage.naturalWidth,\n            imgHeight = this.srcImage.naturalHeight,\n            width = options.width,\n            height = options.height,\n            maxWidth = options.maxWidth,\n            maxHeight = options.maxHeight,\n            doSquash = !this.blob || this.blob.type === \"image/jpeg\",\n            tagName = target.tagName.toLowerCase(),\n            opt;\n\n        if (this.imageLoadListeners) {\n            this.imageLoadListeners.push(function() { self.render(target, options); });\n            return;\n        }\n\n        if (width && !height) {\n            height = (imgHeight * width / imgWidth) << 0;\n        } else if (height && !width) {\n            width = (imgWidth * height / imgHeight) << 0;\n        } else {\n            width = imgWidth;\n            height = imgHeight;\n        }\n        if (maxWidth && width > maxWidth) {\n            width = maxWidth;\n            height = (imgHeight * width / imgWidth) << 0;\n        }\n        if (maxHeight && height > maxHeight) {\n            height = maxHeight;\n            width = (imgWidth * height / imgHeight) << 0;\n        }\n\n        opt = { width: width, height: height },\n        qq.each(options, function(optionsKey, optionsValue) {\n            opt[optionsKey] = optionsValue;\n        });\n\n        if (tagName === \"img\") {\n            (function() {\n                var oldTargetSrc = target.src;\n                renderImageToDataURL(self.srcImage, self.blob, opt, doSquash)\n                    .then(function(dataUri) {\n                        target.src = dataUri;\n                        oldTargetSrc === target.src && target.onload();\n                    });\n            }());\n        } else if (tagName === \"canvas\") {\n            renderImageToCanvas(this.srcImage, this.blob, target, opt, doSquash);\n        }\n        if (typeof this.onrender === \"function\") {\n            this.onrender(target);\n        }\n    };\n\n    qq.MegaPixImage = MegaPixImage;\n})();\n", "/*globals qq */\n/**\n * Draws a thumbnail of a Blob/File/URL onto an <img> or <canvas>.\n *\n * @constructor\n */\nqq.ImageGenerator = function(log) {\n    \"use strict\";\n\n    function isImg(el) {\n        return el.tagName.toLowerCase() === \"img\";\n    }\n\n    function isCanvas(el) {\n        return el.tagName.toLowerCase() === \"canvas\";\n    }\n\n    function isImgCorsSupported() {\n        return new Image().crossOrigin !== undefined;\n    }\n\n    function isCanvasSupported() {\n        var canvas = document.createElement(\"canvas\");\n\n        return canvas.getContext && canvas.getContext(\"2d\");\n    }\n\n    // This is only meant to determine the MIME type of a renderable image file.\n    // It is used to ensure images drawn from a URL that have transparent backgrounds\n    // are rendered correctly, among other things.\n    function determineMimeOfFileName(nameWithPath) {\n        /*jshint -W015 */\n        var pathSegments = nameWithPath.split(\"/\"),\n            name = pathSegments[pathSegments.length - 1].split(\"?\")[0],\n            extension = qq.getExtension(name);\n\n        extension = extension && extension.toLowerCase();\n\n        switch (extension) {\n            case \"jpeg\":\n            case \"jpg\":\n                return \"image/jpeg\";\n            case \"png\":\n                return \"image/png\";\n            case \"bmp\":\n                return \"image/bmp\";\n            case \"gif\":\n                return \"image/gif\";\n            case \"tiff\":\n            case \"tif\":\n                return \"image/tiff\";\n        }\n    }\n\n    // This will likely not work correctly in IE8 and older.\n    // It's only used as part of a formula to determine\n    // if a canvas can be used to scale a server-hosted thumbnail.\n    // If canvas isn't supported by the UA (IE8 and older)\n    // this method should not even be called.\n    function isCrossOrigin(url) {\n        var targetAnchor = document.createElement(\"a\"),\n            targetProtocol, targetHostname, targetPort;\n\n        targetAnchor.href = url;\n\n        targetProtocol = targetAnchor.protocol;\n        targetPort = targetAnchor.port;\n        targetHostname = targetAnchor.hostname;\n\n        if (targetProtocol.toLowerCase() !== window.location.protocol.toLowerCase()) {\n            return true;\n        }\n\n        if (targetHostname.toLowerCase() !== window.location.hostname.toLowerCase()) {\n            return true;\n        }\n\n        // IE doesn't take ports into consideration when determining if two endpoints are same origin.\n        if (targetPort !== window.location.port && !qq.ie()) {\n            return true;\n        }\n\n        return false;\n    }\n\n    function registerImgLoadListeners(img, promise) {\n        img.onload = function() {\n            img.onload = null;\n            img.onerror = null;\n            promise.success(img);\n        };\n\n        img.onerror = function() {\n            img.onload = null;\n            img.onerror = null;\n            log(\"Problem drawing thumbnail!\", \"error\");\n            promise.failure(img, \"Problem drawing thumbnail!\");\n        };\n    }\n\n    function registerCanvasDrawImageListener(canvas, promise) {\n        // The image is drawn on the canvas by a third-party library,\n        // and we want to know when this is completed.  Since the library\n        // may invoke drawImage many times in a loop, we need to be called\n        // back when the image is fully rendered.  So, we are expecting the\n        // code that draws this image to follow a convention that involves a\n        // function attached to the canvas instance be invoked when it is done.\n        canvas.qqImageRendered = function() {\n            promise.success(canvas);\n        };\n    }\n\n    // Fulfills a `qq.Promise` when an image has been drawn onto the target,\n    // whether that is a <canvas> or an <img>.  The attempt is considered a\n    // failure if the target is not an <img> or a <canvas>, or if the drawing\n    // attempt was not successful.\n    function registerThumbnailRenderedListener(imgOrCanvas, promise) {\n        var registered = isImg(imgOrCanvas) || isCanvas(imgOrCanvas);\n\n        if (isImg(imgOrCanvas)) {\n            registerImgLoadListeners(imgOrCanvas, promise);\n        }\n        else if (isCanvas(imgOrCanvas)) {\n            registerCanvasDrawImageListener(imgOrCanvas, promise);\n        }\n        else {\n            promise.failure(imgOrCanvas);\n            log(qq.format(\"Element container of type {} is not supported!\", imgOrCanvas.tagName), \"error\");\n        }\n\n        return registered;\n    }\n\n    // Draw a preview iff the current UA can natively display it.\n    // Also rotate the image if necessary.\n    function draw(fileOrBlob, container, options) {\n        var drawPreview = new qq.Promise(),\n            identifier = new qq.Identify(fileOrBlob, log),\n            maxSize = options.maxSize,\n            // jshint eqnull:true\n            orient = options.orient == null ? true : options.orient,\n            megapixErrorHandler = function() {\n                container.onerror = null;\n                container.onload = null;\n                log(\"Could not render preview, file may be too large!\", \"error\");\n                drawPreview.failure(container, \"Browser cannot render image!\");\n            };\n\n        identifier.isPreviewable().then(\n            function(mime) {\n                // If options explicitly specify that Orientation is not desired,\n                // replace the orient task with a dummy promise that \"succeeds\" immediately.\n                var dummyExif = {\n                        parse: function() {\n                            return new qq.Promise().success();\n                        }\n                    },\n                    exif = orient ? new qq.Exif(fileOrBlob, log) : dummyExif,\n                    mpImg = new qq.MegaPixImage(fileOrBlob, megapixErrorHandler);\n\n                if (registerThumbnailRenderedListener(container, drawPreview)) {\n                    exif.parse().then(\n                        function(exif) {\n                            var orientation = exif && exif.Orientation;\n\n                            mpImg.render(container, {\n                                maxWidth: maxSize,\n                                maxHeight: maxSize,\n                                orientation: orientation,\n                                mime: mime,\n                                resize: options.customResizeFunction\n                            });\n                        },\n\n                        function(failureMsg) {\n                            log(qq.format(\"EXIF data could not be parsed ({}).  Assuming orientation = 1.\", failureMsg));\n\n                            mpImg.render(container, {\n                                maxWidth: maxSize,\n                                maxHeight: maxSize,\n                                mime: mime,\n                                resize: options.customResizeFunction\n                            });\n                        }\n                    );\n                }\n            },\n\n            function() {\n                log(\"Not previewable\");\n                drawPreview.failure(container, \"Not previewable\");\n            }\n        );\n\n        return drawPreview;\n    }\n\n    function drawOnCanvasOrImgFromUrl(url, canvasOrImg, draw, maxSize, customResizeFunction) {\n        var tempImg = new Image(),\n            tempImgRender = new qq.Promise();\n\n        registerThumbnailRenderedListener(tempImg, tempImgRender);\n\n        if (isCrossOrigin(url)) {\n            tempImg.crossOrigin = \"anonymous\";\n        }\n\n        tempImg.src = url;\n\n        tempImgRender.then(\n            function rendered() {\n                registerThumbnailRenderedListener(canvasOrImg, draw);\n\n                var mpImg = new qq.MegaPixImage(tempImg);\n                mpImg.render(canvasOrImg, {\n                    maxWidth: maxSize,\n                    maxHeight: maxSize,\n                    mime: determineMimeOfFileName(url),\n                    resize: customResizeFunction\n                });\n            },\n\n            draw.failure\n        );\n    }\n\n    function drawOnImgFromUrlWithCssScaling(url, img, draw, maxSize) {\n        registerThumbnailRenderedListener(img, draw);\n        // NOTE: The fact that maxWidth/height is set on the thumbnail for scaled images\n        // that must drop back to CSS is known and exploited by the templating module.\n        // In this module, we pre-render \"waiting\" thumbs for all files immediately after they\n        // are submitted, and we must be sure to pass any style associated with the \"waiting\" preview.\n        qq(img).css({\n            maxWidth: maxSize + \"px\",\n            maxHeight: maxSize + \"px\"\n        });\n\n        img.src = url;\n    }\n\n    // Draw a (server-hosted) thumbnail given a URL.\n    // This will optionally scale the thumbnail as well.\n    // It attempts to use <canvas> to scale, but will fall back\n    // to max-width and max-height style properties if the UA\n    // doesn't support canvas or if the images is cross-domain and\n    // the UA doesn't support the crossorigin attribute on img tags,\n    // which is required to scale a cross-origin image using <canvas> &\n    // then export it back to an <img>.\n    function drawFromUrl(url, container, options) {\n        var draw = new qq.Promise(),\n            scale = options.scale,\n            maxSize = scale ? options.maxSize : null;\n\n        // container is an img, scaling needed\n        if (scale && isImg(container)) {\n            // Iff canvas is available in this UA, try to use it for scaling.\n            // Otherwise, fall back to CSS scaling\n            if (isCanvasSupported()) {\n                // Attempt to use <canvas> for image scaling,\n                // but we must fall back to scaling via CSS/styles\n                // if this is a cross-origin image and the UA doesn't support <img> CORS.\n                if (isCrossOrigin(url) && !isImgCorsSupported()) {\n                    drawOnImgFromUrlWithCssScaling(url, container, draw, maxSize);\n                }\n                else {\n                    drawOnCanvasOrImgFromUrl(url, container, draw, maxSize);\n                }\n            }\n            else {\n                drawOnImgFromUrlWithCssScaling(url, container, draw, maxSize);\n            }\n        }\n        // container is a canvas, scaling optional\n        else if (isCanvas(container)) {\n            drawOnCanvasOrImgFromUrl(url, container, draw, maxSize);\n        }\n        // container is an img & no scaling: just set the src attr to the passed url\n        else if (registerThumbnailRenderedListener(container, draw)) {\n            container.src = url;\n        }\n\n        return draw;\n    }\n\n    qq.extend(this, {\n        /**\n         * Generate a thumbnail.  Depending on the arguments, this may either result in\n         * a client-side rendering of an image (if a `Blob` is supplied) or a server-generated\n         * image that may optionally be scaled client-side using <canvas> or CSS/styles (as a fallback).\n         *\n         * @param fileBlobOrUrl a `File`, `Blob`, or a URL pointing to the image\n         * @param container <img> or <canvas> to contain the preview\n         * @param options possible properties include `maxSize` (int), `orient` (bool - default true), resize` (bool - default true), and `customResizeFunction`.\n         * @returns qq.Promise fulfilled when the preview has been drawn, or the attempt has failed\n         */\n        generate: function(fileBlobOrUrl, container, options) {\n            if (qq.isString(fileBlobOrUrl)) {\n                log(\"Attempting to update thumbnail based on server response.\");\n                return drawFromUrl(fileBlobOrUrl, container, options || {});\n            }\n            else {\n                log(\"Attempting to draw client-side image preview.\");\n                return draw(fileBlobOrUrl, container, options || {});\n            }\n        }\n    });\n\n    /*<testing>*/\n    this._testing = {};\n    this._testing.isImg = isImg;\n    this._testing.isCanvas = isCanvas;\n    this._testing.isCrossOrigin = isCrossOrigin;\n    this._testing.determineMimeOfFileName = determineMimeOfFileName;\n    /*</testing>*/\n};\n", "/*globals qq */\n/**\n * EXIF image data parser.  Currently only parses the Orientation tag value,\n * but this may be expanded to other tags in the future.\n *\n * @param fileOrBlob Attempt to parse EXIF data in this `Blob`\n * @constructor\n */\nqq.Exif = function(fileOrBlob, log) {\n    \"use strict\";\n\n    // Orientation is the only tag parsed here at this time.\n    var TAG_IDS = [274],\n        TAG_INFO = {\n            274: {\n                name: \"Orientation\",\n                bytes: 2\n            }\n        };\n\n    // Convert a little endian (hex string) to big endian (decimal).\n    function parseLittleEndian(hex) {\n        var result = 0,\n            pow = 0;\n\n        while (hex.length > 0) {\n            result += parseInt(hex.substring(0, 2), 16) * Math.pow(2, pow);\n            hex = hex.substring(2, hex.length);\n            pow += 8;\n        }\n\n        return result;\n    }\n\n    // Find the byte offset, of Application Segment 1 (EXIF).\n    // External callers need not supply any arguments.\n    function seekToApp1(offset, promise) {\n        var theOffset = offset,\n            thePromise = promise;\n        if (theOffset === undefined) {\n            theOffset = 2;\n            thePromise = new qq.Promise();\n        }\n\n        qq.readBlobToHex(fileOrBlob, theOffset, 4).then(function(hex) {\n            var match = /^ffe([0-9])/.exec(hex),\n                segmentLength;\n\n            if (match) {\n                if (match[1] !== \"1\") {\n                    segmentLength = parseInt(hex.slice(4, 8), 16);\n                    seekToApp1(theOffset + segmentLength + 2, thePromise);\n                }\n                else {\n                    thePromise.success(theOffset);\n                }\n            }\n            else {\n                thePromise.failure(\"No EXIF header to be found!\");\n            }\n        });\n\n        return thePromise;\n    }\n\n    // Find the byte offset of Application Segment 1 (EXIF) for valid JPEGs only.\n    function getApp1Offset() {\n        var promise = new qq.Promise();\n\n        qq.readBlobToHex(fileOrBlob, 0, 6).then(function(hex) {\n            if (hex.indexOf(\"ffd8\") !== 0) {\n                promise.failure(\"Not a valid JPEG!\");\n            }\n            else {\n                seekToApp1().then(function(offset) {\n                    promise.success(offset);\n                },\n                function(error) {\n                    promise.failure(error);\n                });\n            }\n        });\n\n        return promise;\n    }\n\n    // Determine the byte ordering of the EXIF header.\n    function isLittleEndian(app1Start) {\n        var promise = new qq.Promise();\n\n        qq.readBlobToHex(fileOrBlob, app1Start + 10, 2).then(function(hex) {\n            promise.success(hex === \"4949\");\n        });\n\n        return promise;\n    }\n\n    // Determine the number of directory entries in the EXIF header.\n    function getDirEntryCount(app1Start, littleEndian) {\n        var promise = new qq.Promise();\n\n        qq.readBlobToHex(fileOrBlob, app1Start + 18, 2).then(function(hex) {\n            if (littleEndian) {\n                return promise.success(parseLittleEndian(hex));\n            }\n            else {\n                promise.success(parseInt(hex, 16));\n            }\n        });\n\n        return promise;\n    }\n\n    // Get the IFD portion of the EXIF header as a hex string.\n    function getIfd(app1Start, dirEntries) {\n        var offset = app1Start + 20,\n            bytes = dirEntries * 12;\n\n        return qq.readBlobToHex(fileOrBlob, offset, bytes);\n    }\n\n    // Obtain an array of all directory entries (as hex strings) in the EXIF header.\n    function getDirEntries(ifdHex) {\n        var entries = [],\n            offset = 0;\n\n        while (offset + 24 <= ifdHex.length) {\n            entries.push(ifdHex.slice(offset, offset + 24));\n            offset += 24;\n        }\n\n        return entries;\n    }\n\n    // Obtain values for all relevant tags and return them.\n    function getTagValues(littleEndian, dirEntries) {\n        var TAG_VAL_OFFSET = 16,\n            tagsToFind = qq.extend([], TAG_IDS),\n            vals = {};\n\n        qq.each(dirEntries, function(idx, entry) {\n            var idHex = entry.slice(0, 4),\n                id = littleEndian ? parseLittleEndian(idHex) : parseInt(idHex, 16),\n                tagsToFindIdx = tagsToFind.indexOf(id),\n                tagValHex, tagName, tagValLength;\n\n            if (tagsToFindIdx >= 0) {\n                tagName = TAG_INFO[id].name;\n                tagValLength = TAG_INFO[id].bytes;\n                tagValHex = entry.slice(TAG_VAL_OFFSET, TAG_VAL_OFFSET + (tagValLength * 2));\n                vals[tagName] = littleEndian ? parseLittleEndian(tagValHex) : parseInt(tagValHex, 16);\n\n                tagsToFind.splice(tagsToFindIdx, 1);\n            }\n\n            if (tagsToFind.length === 0) {\n                return false;\n            }\n        });\n\n        return vals;\n    }\n\n    qq.extend(this, {\n        /**\n         * Attempt to parse the EXIF header for the `Blob` associated with this instance.\n         *\n         * @returns {qq.Promise} To be fulfilled when the parsing is complete.\n         * If successful, the parsed EXIF header as an object will be included.\n         */\n        parse: function() {\n            var parser = new qq.Promise(),\n                onParseFailure = function(message) {\n                    log(qq.format(\"EXIF header parse failed: '{}' \", message));\n                    parser.failure(message);\n                };\n\n            getApp1Offset().then(function(app1Offset) {\n                log(qq.format(\"Moving forward with EXIF header parsing for '{}'\", fileOrBlob.name === undefined ? \"blob\" : fileOrBlob.name));\n\n                isLittleEndian(app1Offset).then(function(littleEndian) {\n\n                    log(qq.format(\"EXIF Byte order is {} endian\", littleEndian ? \"little\" : \"big\"));\n\n                    getDirEntryCount(app1Offset, littleEndian).then(function(dirEntryCount) {\n\n                        log(qq.format(\"Found {} APP1 directory entries\", dirEntryCount));\n\n                        getIfd(app1Offset, dirEntryCount).then(function(ifdHex) {\n                            var dirEntries = getDirEntries(ifdHex),\n                                tagValues = getTagValues(littleEndian, dirEntries);\n\n                            log(\"Successfully parsed some EXIF tags\");\n\n                            parser.success(tagValues);\n                        }, onParseFailure);\n                    }, onParseFailure);\n                }, onParseFailure);\n            }, onParseFailure);\n\n            return parser;\n        }\n    });\n\n    /*<testing>*/\n    this._testing = {};\n    this._testing.parseLittleEndian = parseLittleEndian;\n    /*</testing>*/\n};\n", "/*globals qq */\nqq.Identify = function(fileOrBlob, log) {\n    \"use strict\";\n\n    function isIdentifiable(magicBytes, questionableBytes) {\n        var identifiable = false,\n            magicBytesEntries = [].concat(magicBytes);\n\n        qq.each(magicBytesEntries, function(idx, magicBytesArrayEntry) {\n            if (questionableBytes.indexOf(magicBytesArrayEntry) === 0) {\n                identifiable = true;\n                return false;\n            }\n        });\n\n        return identifiable;\n    }\n\n    qq.extend(this, {\n        /**\n         * Determines if a Blob can be displayed natively in the current browser.  This is done by reading magic\n         * bytes in the beginning of the file, so this is an asynchronous operation.  Before we attempt to read the\n         * file, we will examine the blob's type attribute to save CPU cycles.\n         *\n         * @returns {qq.Promise} Promise that is fulfilled when identification is complete.\n         * If successful, the MIME string is passed to the success handler.\n         */\n        isPreviewable: function() {\n            var self = this,\n                identifier = new qq.Promise(),\n                previewable = false,\n                name = fileOrBlob.name === undefined ? \"blob\" : fileOrBlob.name;\n\n            log(qq.format(\"Attempting to determine if {} can be rendered in this browser\", name));\n\n            log(\"First pass: check type attribute of blob object.\");\n\n            if (this.isPreviewableSync()) {\n                log(\"Second pass: check for magic bytes in file header.\");\n\n                qq.readBlobToHex(fileOrBlob, 0, 4).then(function(hex) {\n                    qq.each(self.PREVIEWABLE_MIME_TYPES, function(mime, bytes) {\n                        if (isIdentifiable(bytes, hex)) {\n                            // Safari is the only supported browser that can deal with TIFFs natively,\n                            // so, if this is a TIFF and the UA isn't Safari, declare this file \"non-previewable\".\n                            if (mime !== \"image/tiff\" || qq.supportedFeatures.tiffPreviews) {\n                                previewable = true;\n                                identifier.success(mime);\n                            }\n\n                            return false;\n                        }\n                    });\n\n                    log(qq.format(\"'{}' is {} able to be rendered in this browser\", name, previewable ? \"\" : \"NOT\"));\n\n                    if (!previewable) {\n                        identifier.failure();\n                    }\n                },\n                function() {\n                    log(\"Error reading file w/ name '\" + name + \"'.  Not able to be rendered in this browser.\");\n                    identifier.failure();\n                });\n            }\n            else {\n                identifier.failure();\n            }\n\n            return identifier;\n        },\n\n        /**\n         * Determines if a Blob can be displayed natively in the current browser.  This is done by checking the\n         * blob's type attribute.  This is a synchronous operation, useful for situations where an asynchronous operation\n         * would be challenging to support.  Note that the blob's type property is not as accurate as reading the\n         * file's magic bytes.\n         *\n         * @returns {Boolean} true if the blob can be rendered in the current browser\n         */\n        isPreviewableSync: function() {\n            var fileMime = fileOrBlob.type,\n                // Assumption: This will only ever be executed in browsers that support `Object.keys`.\n                isRecognizedImage = qq.indexOf(Object.keys(this.PREVIEWABLE_MIME_TYPES), fileMime) >= 0,\n                previewable = false,\n                name = fileOrBlob.name === undefined ? \"blob\" : fileOrBlob.name;\n\n            if (isRecognizedImage) {\n                if (fileMime === \"image/tiff\") {\n                    previewable = qq.supportedFeatures.tiffPreviews;\n                }\n                else {\n                    previewable = true;\n                }\n            }\n\n            !previewable && log(name + \" is not previewable in this browser per the blob's type attr\");\n\n            return previewable;\n        }\n    });\n};\n\nqq.Identify.prototype.PREVIEWABLE_MIME_TYPES = {\n    \"image/jpeg\": \"ffd8ff\",\n    \"image/gif\": \"474946\",\n    \"image/png\": \"89504e\",\n    \"image/bmp\": \"424d\",\n    \"image/tiff\": [\"49492a00\", \"4d4d002a\"]\n};\n", "/*globals qq*/\n/**\n * Attempts to validate an image, wherever possible.\n *\n * @param blob File or Blob representing a user-selecting image.\n * @param log Uses this to post log messages to the console.\n * @constructor\n */\nqq.ImageValidation = function(blob, log) {\n    \"use strict\";\n\n    /**\n     * @param limits Object with possible image-related limits to enforce.\n     * @returns {boolean} true if at least one of the limits has a non-zero value\n     */\n    function hasNonZeroLimits(limits) {\n        var atLeastOne = false;\n\n        qq.each(limits, function(limit, value) {\n            if (value > 0) {\n                atLeastOne = true;\n                return false;\n            }\n        });\n\n        return atLeastOne;\n    }\n\n    /**\n     * @returns {qq.Promise} The promise is a failure if we can't obtain the width & height.\n     * Otherwise, `success` is called on the returned promise with an object containing\n     * `width` and `height` properties.\n     */\n    function getWidthHeight() {\n        var sizeDetermination = new qq.Promise();\n\n        new qq.Identify(blob, log).isPreviewable().then(function() {\n            var image = new Image(),\n                url = window.URL && window.URL.createObjectURL ? window.URL :\n                      window.webkitURL && window.webkitURL.createObjectURL ? window.webkitURL :\n                      null;\n\n            if (url) {\n                image.onerror = function() {\n                    log(\"Cannot determine dimensions for image.  May be too large.\", \"error\");\n                    sizeDetermination.failure();\n                };\n\n                image.onload = function() {\n                    sizeDetermination.success({\n                        width: this.width,\n                        height: this.height\n                    });\n                };\n\n                image.src = url.createObjectURL(blob);\n            }\n            else {\n                log(\"No createObjectURL function available to generate image URL!\", \"error\");\n                sizeDetermination.failure();\n            }\n        }, sizeDetermination.failure);\n\n        return sizeDetermination;\n    }\n\n    /**\n     *\n     * @param limits Object with possible image-related limits to enforce.\n     * @param dimensions Object containing `width` & `height` properties for the image to test.\n     * @returns {String || undefined} The name of the failing limit.  Undefined if no failing limits.\n     */\n    function getFailingLimit(limits, dimensions) {\n        var failingLimit;\n\n        qq.each(limits, function(limitName, limitValue) {\n            if (limitValue > 0) {\n                var limitMatcher = /(max|min)(Width|Height)/.exec(limitName),\n                    dimensionPropName = limitMatcher[2].charAt(0).toLowerCase() + limitMatcher[2].slice(1),\n                    actualValue = dimensions[dimensionPropName];\n\n                /*jshint -W015*/\n                switch (limitMatcher[1]) {\n                    case \"min\":\n                        if (actualValue < limitValue) {\n                            failingLimit = limitName;\n                            return false;\n                        }\n                        break;\n                    case \"max\":\n                        if (actualValue > limitValue) {\n                            failingLimit = limitName;\n                            return false;\n                        }\n                        break;\n                }\n            }\n        });\n\n        return failingLimit;\n    }\n\n    /**\n     * Validate the associated blob.\n     *\n     * @param limits\n     * @returns {qq.Promise} `success` is called on the promise is the image is valid or\n     * if the blob is not an image, or if the image is not verifiable.\n     * Otherwise, `failure` with the name of the failing limit.\n     */\n    this.validate = function(limits) {\n        var validationEffort = new qq.Promise();\n\n        log(\"Attempting to validate image.\");\n\n        if (hasNonZeroLimits(limits)) {\n            getWidthHeight().then(function(dimensions) {\n                var failingLimit = getFailingLimit(limits, dimensions);\n\n                if (failingLimit) {\n                    validationEffort.failure(failingLimit);\n                }\n                else {\n                    validationEffort.success();\n                }\n            }, validationEffort.success);\n        }\n        else {\n            validationEffort.success();\n        }\n\n        return validationEffort;\n    };\n};\n", "/* globals qq */\n/**\n * Module used to control populating the initial list of files.\n *\n * @constructor\n */\nqq.Session = function(spec) {\n    \"use strict\";\n\n    var options = {\n        endpoint: null,\n        params: {},\n        customHeaders: {},\n        cors: {},\n        addFileRecord: function(sessionData) {},\n        log: function(message, level) {}\n    };\n\n    qq.extend(options, spec, true);\n\n    function isJsonResponseValid(response) {\n        if (qq.isArray(response)) {\n            return true;\n        }\n\n        options.log(\"Session response is not an array.\", \"error\");\n    }\n\n    function handleFileItems(fileItems, success, xhrOrXdr, promise) {\n        var someItemsIgnored = false;\n\n        success = success && isJsonResponseValid(fileItems);\n\n        if (success) {\n            qq.each(fileItems, function(idx, fileItem) {\n                /* jshint eqnull:true */\n                if (fileItem.uuid == null) {\n                    someItemsIgnored = true;\n                    options.log(qq.format(\"Session response item {} did not include a valid UUID - ignoring.\", idx), \"error\");\n                }\n                else if (fileItem.name == null) {\n                    someItemsIgnored = true;\n                    options.log(qq.format(\"Session response item {} did not include a valid name - ignoring.\", idx), \"error\");\n                }\n                else {\n                    try {\n                        options.addFileRecord(fileItem);\n                        return true;\n                    }\n                    catch (err) {\n                        someItemsIgnored = true;\n                        options.log(err.message, \"error\");\n                    }\n                }\n\n                return false;\n            });\n        }\n\n        promise[success && !someItemsIgnored ? \"success\" : \"failure\"](fileItems, xhrOrXdr);\n    }\n\n    // Initiate a call to the server that will be used to populate the initial file list.\n    // Returns a `qq.Promise`.\n    this.refresh = function() {\n        /*jshint indent:false */\n        var refreshEffort = new qq.Promise(),\n            refreshCompleteCallback = function(response, success, xhrOrXdr) {\n                handleFileItems(response, success, xhrOrXdr, refreshEffort);\n            },\n            requesterOptions = qq.extend({}, options),\n            requester = new qq.SessionAjaxRequester(\n                qq.extend(requesterOptions, {onComplete: refreshCompleteCallback})\n            );\n\n        requester.queryServer();\n\n        return refreshEffort;\n    };\n};\n", "/*globals qq, XMLHttpRequest*/\n/**\n * Thin module used to send GET requests to the server, expecting information about session\n * data used to initialize an uploader instance.\n *\n * @param spec Various options used to influence the associated request.\n * @constructor\n */\nqq.SessionAjaxRequester = function(spec) {\n    \"use strict\";\n\n    var requester,\n        options = {\n            endpoint: null,\n            customHeaders: {},\n            params: {},\n            cors: {\n                expected: false,\n                sendCredentials: false\n            },\n            onComplete: function(response, success, xhrOrXdr) {},\n            log: function(str, level) {}\n        };\n\n    qq.extend(options, spec);\n\n    function onComplete(id, xhrOrXdr, isError) {\n        var response = null;\n\n        /* jshint eqnull:true */\n        if (xhrOrXdr.responseText != null) {\n            try {\n                response = qq.parseJson(xhrOrXdr.responseText);\n            }\n            catch (err) {\n                options.log(\"Problem parsing session response: \" + err.message, \"error\");\n                isError = true;\n            }\n        }\n\n        options.onComplete(response, !isError, xhrOrXdr);\n    }\n\n    requester = qq.extend(this, new qq.AjaxRequester({\n        acceptHeader: \"application/json\",\n        validMethods: [\"GET\"],\n        method: \"GET\",\n        endpointStore: {\n            get: function() {\n                return options.endpoint;\n            }\n        },\n        customHeaders: options.customHeaders,\n        log: options.log,\n        onComplete: onComplete,\n        cors: options.cors\n    }));\n\n    qq.extend(this, {\n        queryServer: function() {\n            var params = qq.extend({}, options.params);\n\n            options.log(\"Session query request.\");\n\n            requester.initTransport(\"sessionRefresh\")\n                .withParams(params)\n                .withCacheBuster()\n                .send();\n        }\n    });\n};\n", "/* globals qq, ExifRestorer */\n/**\n * Controls generation of scaled images based on a reference image encapsulated in a `File` or `Blob`.\n * Scaled images are generated and converted to blobs on-demand.\n * Multiple scaled images per reference image with varying sizes and other properties are supported.\n *\n * @param spec Information about the scaled images to generate.\n * @param log Logger instance\n * @constructor\n */\nqq.Scaler = function(spec, log) {\n    \"use strict\";\n\n    var self = this,\n        customResizeFunction = spec.customResizer,\n        includeOriginal = spec.sendOriginal,\n        orient = spec.orient,\n        defaultType = spec.defaultType,\n        defaultQuality = spec.defaultQuality / 100,\n        failedToScaleText = spec.failureText,\n        includeExif = spec.includeExif,\n        sizes = this._getSortedSizes(spec.sizes);\n\n    // Revealed API for instances of this module\n    qq.extend(this, {\n        // If no targeted sizes have been declared or if this browser doesn't support\n        // client-side image preview generation, there is no scaling to do.\n        enabled: qq.supportedFeatures.scaling && sizes.length > 0,\n\n        getFileRecords: function(originalFileUuid, originalFileName, originalBlobOrBlobData) {\n            var self = this,\n                records = [],\n                originalBlob = originalBlobOrBlobData.blob ? originalBlobOrBlobData.blob : originalBlobOrBlobData,\n                identifier = new qq.Identify(originalBlob, log);\n\n            // If the reference file cannot be rendered natively, we can't create scaled versions.\n            if (identifier.isPreviewableSync()) {\n                // Create records for each scaled version & add them to the records array, smallest first.\n                qq.each(sizes, function(idx, sizeRecord) {\n                    var outputType = self._determineOutputType({\n                        defaultType: defaultType,\n                        requestedType: sizeRecord.type,\n                        refType: originalBlob.type\n                    });\n\n                    records.push({\n                        uuid: qq.getUniqueId(),\n                        name: self._getName(originalFileName, {\n                            name: sizeRecord.name,\n                            type: outputType,\n                            refType: originalBlob.type\n                        }),\n                        blob: new qq.BlobProxy(originalBlob,\n                        qq.bind(self._generateScaledImage, self, {\n                            customResizeFunction: customResizeFunction,\n                            maxSize: sizeRecord.maxSize,\n                            orient: orient,\n                            type: outputType,\n                            quality: defaultQuality,\n                            failedText: failedToScaleText,\n                            includeExif: includeExif,\n                            log: log\n                        }))\n                    });\n                });\n\n                records.push({\n                    uuid: originalFileUuid,\n                    name: originalFileName,\n                    size: originalBlob.size,\n                    blob: includeOriginal ? originalBlob : null\n                });\n            }\n            else {\n                records.push({\n                    uuid: originalFileUuid,\n                    name: originalFileName,\n                    size: originalBlob.size,\n                    blob: originalBlob\n                });\n            }\n\n            return records;\n        },\n\n        handleNewFile: function(file, name, uuid, size, fileList, batchId, uuidParamName, api) {\n            var self = this,\n                buttonId = file.qqButtonId || (file.blob && file.blob.qqButtonId),\n                scaledIds = [],\n                originalId = null,\n                addFileToHandler = api.addFileToHandler,\n                uploadData = api.uploadData,\n                paramsStore = api.paramsStore,\n                proxyGroupId = qq.getUniqueId();\n\n            qq.each(self.getFileRecords(uuid, name, file), function(idx, record) {\n                var blobSize = record.size,\n                    id;\n\n                if (record.blob instanceof qq.BlobProxy) {\n                    blobSize = -1;\n                }\n\n                id = uploadData.addFile({\n                    uuid: record.uuid,\n                    name: record.name,\n                    size: blobSize,\n                    batchId: batchId,\n                    proxyGroupId: proxyGroupId\n                });\n\n                if (record.blob instanceof qq.BlobProxy) {\n                    scaledIds.push(id);\n                }\n                else {\n                    originalId = id;\n                }\n\n                if (record.blob) {\n                    addFileToHandler(id, record.blob);\n                    fileList.push({id: id, file: record.blob});\n                }\n                else {\n                    uploadData.setStatus(id, qq.status.REJECTED);\n                }\n            });\n\n            // If we are potentially uploading an original file and some scaled versions,\n            // ensure the scaled versions include reference's to the parent's UUID and size\n            // in their associated upload requests.\n            if (originalId !== null) {\n                qq.each(scaledIds, function(idx, scaledId) {\n                    var params = {\n                        qqparentuuid: uploadData.retrieve({id: originalId}).uuid,\n                        qqparentsize: uploadData.retrieve({id: originalId}).size\n                    };\n\n                    // Make sure the UUID for each scaled image is sent with the upload request,\n                    // to be consistent (since we may need to ensure it is sent for the original file as well).\n                    params[uuidParamName] = uploadData.retrieve({id: scaledId}).uuid;\n\n                    uploadData.setParentId(scaledId, originalId);\n                    paramsStore.addReadOnly(scaledId, params);\n                });\n\n                // If any scaled images are tied to this parent image, be SURE we send its UUID as an upload request\n                // parameter as well.\n                if (scaledIds.length) {\n                    (function() {\n                        var param = {};\n                        param[uuidParamName] = uploadData.retrieve({id: originalId}).uuid;\n                        paramsStore.addReadOnly(originalId, param);\n                    }());\n                }\n            }\n        }\n    });\n};\n\nqq.extend(qq.Scaler.prototype, {\n    scaleImage: function(id, specs, api) {\n        \"use strict\";\n\n        if (!qq.supportedFeatures.scaling) {\n            throw new qq.Error(\"Scaling is not supported in this browser!\");\n        }\n\n        var scalingEffort = new qq.Promise(),\n            log = api.log,\n            file = api.getFile(id),\n            uploadData = api.uploadData.retrieve({id: id}),\n            name = uploadData && uploadData.name,\n            uuid = uploadData && uploadData.uuid,\n            scalingOptions = {\n                customResizer: specs.customResizer,\n                sendOriginal: false,\n                orient: specs.orient,\n                defaultType: specs.type || null,\n                defaultQuality: specs.quality,\n                failedToScaleText: \"Unable to scale\",\n                sizes: [{name: \"\", maxSize: specs.maxSize}]\n            },\n            scaler = new qq.Scaler(scalingOptions, log);\n\n        if (!qq.Scaler || !qq.supportedFeatures.imagePreviews || !file) {\n            scalingEffort.failure();\n\n            log(\"Could not generate requested scaled image for \" + id + \".  \" +\n                \"Scaling is either not possible in this browser, or the file could not be located.\", \"error\");\n        }\n        else {\n            (qq.bind(function() {\n                // Assumption: There will never be more than one record\n                var record = scaler.getFileRecords(uuid, name, file)[0];\n\n                if (record && record.blob instanceof qq.BlobProxy) {\n                    record.blob.create().then(scalingEffort.success, scalingEffort.failure);\n                }\n                else {\n                    log(id + \" is not a scalable image!\", \"error\");\n                    scalingEffort.failure();\n                }\n            }, this)());\n        }\n\n        return scalingEffort;\n    },\n\n    // NOTE: We cannot reliably determine at this time if the UA supports a specific MIME type for the target format.\n    // image/jpeg and image/png are the only safe choices at this time.\n    _determineOutputType: function(spec) {\n        \"use strict\";\n\n        var requestedType = spec.requestedType,\n            defaultType = spec.defaultType,\n            referenceType = spec.refType;\n\n        // If a default type and requested type have not been specified, this should be a\n        // JPEG if the original type is a JPEG, otherwise, a PNG.\n        if (!defaultType && !requestedType) {\n            if (referenceType !== \"image/jpeg\") {\n                return \"image/png\";\n            }\n            return referenceType;\n        }\n\n        // A specified default type is used when a requested type is not specified.\n        if (!requestedType) {\n            return defaultType;\n        }\n\n        // If requested type is specified, use it, as long as this recognized type is supported by the current UA\n        if (qq.indexOf(Object.keys(qq.Identify.prototype.PREVIEWABLE_MIME_TYPES), requestedType) >= 0) {\n            if (requestedType === \"image/tiff\") {\n                return qq.supportedFeatures.tiffPreviews ? requestedType : defaultType;\n            }\n\n            return requestedType;\n        }\n\n        return defaultType;\n    },\n\n    // Get a file name for a generated scaled file record, based on the provided scaled image description\n    _getName: function(originalName, scaledVersionProperties) {\n        \"use strict\";\n\n        var startOfExt = originalName.lastIndexOf(\".\"),\n            versionType = scaledVersionProperties.type || \"image/png\",\n            referenceType = scaledVersionProperties.refType,\n            scaledName = \"\",\n            scaledExt = qq.getExtension(originalName),\n            nameAppendage = \"\";\n\n        if (scaledVersionProperties.name && scaledVersionProperties.name.trim().length) {\n            nameAppendage = \" (\" + scaledVersionProperties.name + \")\";\n        }\n\n        if (startOfExt >= 0) {\n            scaledName = originalName.substr(0, startOfExt);\n\n            if (referenceType !== versionType) {\n                scaledExt = versionType.split(\"/\")[1];\n            }\n\n            scaledName += nameAppendage + \".\" + scaledExt;\n        }\n        else {\n            scaledName = originalName + nameAppendage;\n        }\n\n        return scaledName;\n    },\n\n    // We want the smallest scaled file to be uploaded first\n    _getSortedSizes: function(sizes) {\n        \"use strict\";\n\n        sizes = qq.extend([], sizes);\n\n        return sizes.sort(function(a, b) {\n            if (a.maxSize > b.maxSize) {\n                return 1;\n            }\n            if (a.maxSize < b.maxSize) {\n                return -1;\n            }\n            return 0;\n        });\n    },\n\n    _generateScaledImage: function(spec, sourceFile) {\n        \"use strict\";\n\n        var self = this,\n            customResizeFunction = spec.customResizeFunction,\n            log = spec.log,\n            maxSize = spec.maxSize,\n            orient = spec.orient,\n            type = spec.type,\n            quality = spec.quality,\n            failedText = spec.failedText,\n            includeExif = spec.includeExif && sourceFile.type === \"image/jpeg\" && type === \"image/jpeg\",\n            scalingEffort = new qq.Promise(),\n            imageGenerator = new qq.ImageGenerator(log),\n            canvas = document.createElement(\"canvas\");\n\n        log(\"Attempting to generate scaled version for \" + sourceFile.name);\n\n        imageGenerator.generate(sourceFile, canvas, {maxSize: maxSize, orient: orient, customResizeFunction: customResizeFunction}).then(function() {\n            var scaledImageDataUri = canvas.toDataURL(type, quality),\n                signalSuccess = function() {\n                    log(\"Success generating scaled version for \" + sourceFile.name);\n                    var blob = qq.dataUriToBlob(scaledImageDataUri);\n                    scalingEffort.success(blob);\n                };\n\n            if (includeExif) {\n                self._insertExifHeader(sourceFile, scaledImageDataUri, log).then(function(scaledImageDataUriWithExif) {\n                    scaledImageDataUri = scaledImageDataUriWithExif;\n                    signalSuccess();\n                },\n                function() {\n                    log(\"Problem inserting EXIF header into scaled image.  Using scaled image w/out EXIF data.\", \"error\");\n                    signalSuccess();\n                });\n            }\n            else {\n                signalSuccess();\n            }\n        }, function() {\n            log(\"Failed attempt to generate scaled version for \" + sourceFile.name, \"error\");\n            scalingEffort.failure(failedText);\n        });\n\n        return scalingEffort;\n    },\n\n    // Attempt to insert the original image's EXIF header into a scaled version.\n    _insertExifHeader: function(originalImage, scaledImageDataUri, log) {\n        \"use strict\";\n\n        var reader = new FileReader(),\n            insertionEffort = new qq.Promise(),\n            originalImageDataUri = \"\";\n\n        reader.onload = function() {\n            originalImageDataUri = reader.result;\n            insertionEffort.success(qq.ExifRestorer.restore(originalImageDataUri, scaledImageDataUri));\n        };\n\n        reader.onerror = function() {\n            log(\"Problem reading \" + originalImage.name + \" during attempt to transfer EXIF data to scaled version.\", \"error\");\n            insertionEffort.failure();\n        };\n\n        reader.readAsDataURL(originalImage);\n\n        return insertionEffort;\n    },\n\n    _dataUriToBlob: function(dataUri) {\n        \"use strict\";\n\n        var byteString, mimeString, arrayBuffer, intArray;\n\n        // convert base64 to raw binary data held in a string\n        if (dataUri.split(\",\")[0].indexOf(\"base64\") >= 0) {\n            byteString = atob(dataUri.split(\",\")[1]);\n        }\n        else {\n            byteString = decodeURI(dataUri.split(\",\")[1]);\n        }\n\n        // extract the MIME\n        mimeString = dataUri.split(\",\")[0]\n            .split(\":\")[1]\n            .split(\";\")[0];\n\n        // write the bytes of the binary string to an ArrayBuffer\n        arrayBuffer = new ArrayBuffer(byteString.length);\n        intArray = new Uint8Array(arrayBuffer);\n        qq.each(byteString, function(idx, character) {\n            intArray[idx] = character.charCodeAt(0);\n        });\n\n        return this._createBlob(arrayBuffer, mimeString);\n    },\n\n    _createBlob: function(data, mime) {\n        \"use strict\";\n\n        var BlobBuilder = window.BlobBuilder ||\n                window.WebKitBlobBuilder ||\n                window.MozBlobBuilder ||\n                window.MSBlobBuilder,\n            blobBuilder = BlobBuilder && new BlobBuilder();\n\n        if (blobBuilder) {\n            blobBuilder.append(data);\n            return blobBuilder.getBlob(mime);\n        }\n        else {\n            return new Blob([data], {type: mime});\n        }\n    }\n});\n", "//Based on MinifyJpeg\n//http://elicon.blog57.fc2.com/blog-entry-206.html\n\nqq.ExifRestorer = (function()\n{\n   \n\tvar ExifRestorer = {};\n\t \n    ExifRestorer.KEY_STR = \"ABCDEFGHIJKLMNOP\" +\n                         \"QRSTUVWXYZabcdef\" +\n                         \"ghijklmnopqrstuv\" +\n                         \"wxyz0123456789+/\" +\n                         \"=\";\n\n    ExifRestorer.encode64 = function(input)\n    {\n        var output = \"\",\n            chr1, chr2, chr3 = \"\",\n            enc1, enc2, enc3, enc4 = \"\",\n            i = 0;\n\n        do {\n            chr1 = input[i++];\n            chr2 = input[i++];\n            chr3 = input[i++];\n\n            enc1 = chr1 >> 2;\n            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\n            enc4 = chr3 & 63;\n\n            if (isNaN(chr2)) {\n               enc3 = enc4 = 64;\n            } else if (isNaN(chr3)) {\n               enc4 = 64;\n            }\n\n            output = output +\n               this.KEY_STR.charAt(enc1) +\n               this.KEY_STR.charAt(enc2) +\n               this.KEY_STR.charAt(enc3) +\n               this.KEY_STR.charAt(enc4);\n            chr1 = chr2 = chr3 = \"\";\n            enc1 = enc2 = enc3 = enc4 = \"\";\n        } while (i < input.length);\n\n        return output;\n    };\n    \n    ExifRestorer.restore = function(origFileBase64, resizedFileBase64)\n    {\n        var expectedBase64Header = \"data:image/jpeg;base64,\";\n\n        if (!origFileBase64.match(expectedBase64Header))\n        {\n        \treturn resizedFileBase64;\n        }       \n        \n        var rawImage = this.decode64(origFileBase64.replace(expectedBase64Header, \"\"));\n        var segments = this.slice2Segments(rawImage);\n                \n        var image = this.exifManipulation(resizedFileBase64, segments);\n        \n        return expectedBase64Header + this.encode64(image);\n        \n    };\n\n\n    ExifRestorer.exifManipulation = function(resizedFileBase64, segments)\n    {\n            var exifArray = this.getExifArray(segments),\n                newImageArray = this.insertExif(resizedFileBase64, exifArray),\n                aBuffer = new Uint8Array(newImageArray);\n\n            return aBuffer;\n    };\n\n\n    ExifRestorer.getExifArray = function(segments)\n    {\n            var seg;\n            for (var x = 0; x < segments.length; x++)\n            {\n                seg = segments[x];\n                if (seg[0] == 255 & seg[1] == 225) //(ff e1)\n                {\n                    return seg;\n                }\n            }\n            return [];\n    };\n\n\n    ExifRestorer.insertExif = function(resizedFileBase64, exifArray)\n    {\n            var imageData = resizedFileBase64.replace(\"data:image/jpeg;base64,\", \"\"),\n                buf = this.decode64(imageData),\n                separatePoint = buf.indexOf(255,3),\n                mae = buf.slice(0, separatePoint),\n                ato = buf.slice(separatePoint),\n                array = mae;\n\n            array = array.concat(exifArray);\n            array = array.concat(ato);\n           return array;\n    };\n\n\n    \n    ExifRestorer.slice2Segments = function(rawImageArray)\n    {\n        var head = 0,\n            segments = [];\n\n        while (1)\n        {\n            if (rawImageArray[head] == 255 & rawImageArray[head + 1] == 218){break;}\n            if (rawImageArray[head] == 255 & rawImageArray[head + 1] == 216)\n            {\n                head += 2;\n            }\n            else\n            {\n                var length = rawImageArray[head + 2] * 256 + rawImageArray[head + 3],\n                    endPoint = head + length + 2,\n                    seg = rawImageArray.slice(head, endPoint);\n                segments.push(seg);\n                head = endPoint;\n            }\n            if (head > rawImageArray.length){break;}\n        }\n\n        return segments;\n    };\n\n\n    \n    ExifRestorer.decode64 = function(input) \n    {\n        var output = \"\",\n            chr1, chr2, chr3 = \"\",\n            enc1, enc2, enc3, enc4 = \"\",\n            i = 0,\n            buf = [];\n\n        // remove all characters that are not A-Z, a-z, 0-9, +, /, or =\n        var base64test = /[^A-Za-z0-9\\+\\/\\=]/g;\n        if (base64test.exec(input)) {\n            throw new Error(\"There were invalid base64 characters in the input text.  \" +\n                \"Valid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\");\n        }\n        input = input.replace(/[^A-Za-z0-9\\+\\/\\=]/g, \"\");\n\n        do {\n            enc1 = this.KEY_STR.indexOf(input.charAt(i++));\n            enc2 = this.KEY_STR.indexOf(input.charAt(i++));\n            enc3 = this.KEY_STR.indexOf(input.charAt(i++));\n            enc4 = this.KEY_STR.indexOf(input.charAt(i++));\n\n            chr1 = (enc1 << 2) | (enc2 >> 4);\n            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n            chr3 = ((enc3 & 3) << 6) | enc4;\n\n            buf.push(chr1);\n\n            if (enc3 != 64) {\n               buf.push(chr2);\n            }\n            if (enc4 != 64) {\n               buf.push(chr3);\n            }\n\n            chr1 = chr2 = chr3 = \"\";\n            enc1 = enc2 = enc3 = enc4 = \"\";\n\n        } while (i < input.length);\n\n        return buf;\n    };\n\n    \n    return ExifRestorer;\n})();\n", "/* globals qq */\n/**\n * Keeps a running tally of total upload progress for a batch of files.\n *\n * @param callback Invoked when total progress changes, passing calculated total loaded & total size values.\n * @param getSize Function that returns the size of a file given its ID\n * @constructor\n */\nqq.TotalProgress = function(callback, getSize) {\n    \"use strict\";\n\n    var perFileProgress = {},\n        totalLoaded = 0,\n        totalSize = 0,\n\n        lastLoadedSent = -1,\n        lastTotalSent = -1,\n        callbackProxy = function(loaded, total) {\n            if (loaded !== lastLoadedSent || total !== lastTotalSent) {\n                callback(loaded, total);\n            }\n\n            lastLoadedSent = loaded;\n            lastTotalSent = total;\n        },\n\n        /**\n         * @param failed Array of file IDs that have failed\n         * @param retryable Array of file IDs that are retryable\n         * @returns true if none of the failed files are eligible for retry\n         */\n        noRetryableFiles = function(failed, retryable) {\n            var none = true;\n\n            qq.each(failed, function(idx, failedId) {\n                if (qq.indexOf(retryable, failedId) >= 0) {\n                    none = false;\n                    return false;\n                }\n            });\n\n            return none;\n        },\n\n        onCancel = function(id) {\n            updateTotalProgress(id, -1, -1);\n            delete perFileProgress[id];\n        },\n\n        onAllComplete = function(successful, failed, retryable) {\n            if (failed.length === 0 || noRetryableFiles(failed, retryable)) {\n                callbackProxy(totalSize, totalSize);\n                this.reset();\n            }\n        },\n\n        onNew = function(id) {\n            var size = getSize(id);\n\n            // We might not know the size yet, such as for blob proxies\n            if (size > 0) {\n                updateTotalProgress(id, 0, size);\n                perFileProgress[id] = {loaded: 0, total: size};\n            }\n        },\n\n        /**\n         * Invokes the callback with the current total progress of all files in the batch.  Called whenever it may\n         * be appropriate to re-calculate and disseminate this data.\n         *\n         * @param id ID of a file that has changed in some important way\n         * @param newLoaded New loaded value for this file.  -1 if this value should no longer be part of calculations\n         * @param newTotal New total size of the file.  -1 if this value should no longer be part of calculations\n         */\n        updateTotalProgress = function(id, newLoaded, newTotal) {\n            var oldLoaded = perFileProgress[id] ? perFileProgress[id].loaded : 0,\n                oldTotal = perFileProgress[id] ? perFileProgress[id].total : 0;\n\n            if (newLoaded === -1 && newTotal === -1) {\n                totalLoaded -= oldLoaded;\n                totalSize -= oldTotal;\n            }\n            else {\n                if (newLoaded) {\n                    totalLoaded += newLoaded - oldLoaded;\n                }\n                if (newTotal) {\n                    totalSize += newTotal - oldTotal;\n                }\n            }\n\n            callbackProxy(totalLoaded, totalSize);\n        };\n\n    qq.extend(this, {\n        // Called when a batch of files has completed uploading.\n        onAllComplete: onAllComplete,\n\n        // Called when the status of a file has changed.\n        onStatusChange: function(id, oldStatus, newStatus) {\n            if (newStatus === qq.status.CANCELED || newStatus === qq.status.REJECTED) {\n                onCancel(id);\n            }\n            else if (newStatus === qq.status.SUBMITTING) {\n                onNew(id);\n            }\n        },\n\n        // Called whenever the upload progress of an individual file has changed.\n        onIndividualProgress: function(id, loaded, total) {\n            updateTotalProgress(id, loaded, total);\n            perFileProgress[id] = {loaded: loaded, total: total};\n        },\n\n        // Called whenever the total size of a file has changed, such as when the size of a generated blob is known.\n        onNewSize: function(id) {\n            onNew(id);\n        },\n\n        reset: function() {\n            perFileProgress = {};\n            totalLoaded = 0;\n            totalSize = 0;\n        }\n    });\n};\n", "/*globals qq*/\nqq.PasteSupport = function(o) {\n    \"use strict\";\n\n    var options, detachPasteHandler;\n\n    options = {\n        targetElement: null,\n        callbacks: {\n            log: function(message, level) {},\n            pasteReceived: function(blob) {}\n        }\n    };\n\n    function isImage(item) {\n        return item.type &&\n            item.type.indexOf(\"image/\") === 0;\n    }\n\n    function registerPasteHandler() {\n        detachPasteHandler = qq(options.targetElement).attach(\"paste\", function(event) {\n            var clipboardData = event.clipboardData;\n\n            if (clipboardData) {\n                qq.each(clipboardData.items, function(idx, item) {\n                    if (isImage(item)) {\n                        var blob = item.getAsFile();\n                        options.callbacks.pasteReceived(blob);\n                    }\n                });\n            }\n        });\n    }\n\n    function unregisterPasteHandler() {\n        if (detachPasteHandler) {\n            detachPasteHandler();\n        }\n    }\n\n    qq.extend(options, o);\n    registerPasteHandler();\n\n    qq.extend(this, {\n        reset: function() {\n            unregisterPasteHandler();\n        }\n    });\n};\n", "/* globals qq */\n/**\n * Module that handles support for existing forms.\n *\n * @param options Options passed from the integrator-supplied options related to form support.\n * @param startUpload Callback to invoke when files \"stored\" should be uploaded.\n * @param log Proxy for the logger\n * @constructor\n */\nqq.FormSupport = function(options, startUpload, log) {\n    \"use strict\";\n    var self  = this,\n        interceptSubmit = options.interceptSubmit,\n        formEl = options.element,\n        autoUpload = options.autoUpload;\n\n    // Available on the public API associated with this module.\n    qq.extend(this, {\n        // To be used by the caller to determine if the endpoint will be determined by some processing\n        // that occurs in this module, such as if the form has an action attribute.\n        // Ignore if `attachToForm === false`.\n        newEndpoint: null,\n\n        // To be used by the caller to determine if auto uploading should be allowed.\n        // Ignore if `attachToForm === false`.\n        newAutoUpload: autoUpload,\n\n        // true if a form was detected and is being tracked by this module\n        attachedToForm: false,\n\n        // Returns an object with names and values for all valid form elements associated with the attached form.\n        getFormInputsAsObject: function() {\n            /* jshint eqnull:true */\n            if (formEl == null) {\n                return null;\n            }\n\n            return self._form2Obj(formEl);\n        }\n    });\n\n    // If the form contains an action attribute, this should be the new upload endpoint.\n    function determineNewEndpoint(formEl) {\n        if (formEl.getAttribute(\"action\")) {\n            self.newEndpoint = formEl.getAttribute(\"action\");\n        }\n    }\n\n    // Return true only if the form is valid, or if we cannot make this determination.\n    // If the form is invalid, ensure invalid field(s) are highlighted in the UI.\n    function validateForm(formEl, nativeSubmit) {\n        if (formEl.checkValidity && !formEl.checkValidity()) {\n            log(\"Form did not pass validation checks - will not upload.\", \"error\");\n            nativeSubmit();\n        }\n        else {\n            return true;\n        }\n    }\n\n    // Intercept form submit attempts, unless the integrator has told us not to do this.\n    function maybeUploadOnSubmit(formEl) {\n        var nativeSubmit = formEl.submit;\n\n        // Intercept and squelch submit events.\n        qq(formEl).attach(\"submit\", function(event) {\n            event = event || window.event;\n\n            if (event.preventDefault) {\n                event.preventDefault();\n            }\n            else {\n                event.returnValue = false;\n            }\n\n            validateForm(formEl, nativeSubmit) && startUpload();\n        });\n\n        // The form's `submit()` function may be called instead (i.e. via jQuery.submit()).\n        // Intercept that too.\n        formEl.submit = function() {\n            validateForm(formEl, nativeSubmit) && startUpload();\n        };\n    }\n\n    // If the element value passed from the uploader is a string, assume it is an element ID - select it.\n    // The rest of the code in this module depends on this being an HTMLElement.\n    function determineFormEl(formEl) {\n        if (formEl) {\n            if (qq.isString(formEl)) {\n                formEl = document.getElementById(formEl);\n            }\n\n            if (formEl) {\n                log(\"Attaching to form element.\");\n                determineNewEndpoint(formEl);\n                interceptSubmit && maybeUploadOnSubmit(formEl);\n            }\n        }\n\n        return formEl;\n    }\n\n    formEl = determineFormEl(formEl);\n    this.attachedToForm = !!formEl;\n};\n\nqq.extend(qq.FormSupport.prototype, {\n    // Converts all relevant form fields to key/value pairs.  This is meant to mimic the data a browser will\n    // construct from a given form when the form is submitted.\n    _form2Obj: function(form) {\n        \"use strict\";\n        var obj = {},\n            notIrrelevantType = function(type) {\n                var irrelevantTypes = [\n                    \"button\",\n                    \"image\",\n                    \"reset\",\n                    \"submit\"\n                ];\n\n                return qq.indexOf(irrelevantTypes, type.toLowerCase()) < 0;\n            },\n            radioOrCheckbox = function(type) {\n                return qq.indexOf([\"checkbox\", \"radio\"], type.toLowerCase()) >= 0;\n            },\n            ignoreValue = function(el) {\n                if (radioOrCheckbox(el.type) && !el.checked) {\n                    return true;\n                }\n\n                return el.disabled && el.type.toLowerCase() !== \"hidden\";\n            },\n            selectValue = function(select) {\n                var value = null;\n\n                qq.each(qq(select).children(), function(idx, child) {\n                    if (child.tagName.toLowerCase() === \"option\" && child.selected) {\n                        value = child.value;\n                        return false;\n                    }\n                });\n\n                return value;\n            };\n\n        qq.each(form.elements, function(idx, el) {\n            if ((qq.isInput(el, true) || el.tagName.toLowerCase() === \"textarea\") &&\n                notIrrelevantType(el.type) &&\n                !ignoreValue(el)) {\n\n                obj[el.name] = el.value;\n            }\n            else if (el.tagName.toLowerCase() === \"select\" && !ignoreValue(el)) {\n                var value = selectValue(el);\n\n                if (value !== null) {\n                    obj[el.name] = value;\n                }\n            }\n        });\n\n        return obj;\n    }\n});\n", "/*globals qq*/\n/**\n * Upload handler used that assumes the current user agent does not have any support for the\n * File API, and, therefore, makes use of iframes and forms to submit the files directly to\n * a generic server.\n *\n * @param options Options passed from the base handler\n * @param proxy Callbacks & methods used to query for or push out data/changes\n */\nqq.traditional = qq.traditional || {};\nqq.traditional.FormUploadHandler = function(options, proxy) {\n    \"use strict\";\n\n    var handler = this,\n        getName = proxy.getName,\n        getUuid = proxy.getUuid,\n        log = proxy.log;\n\n    /**\n     * Returns json object received by iframe from server.\n     */\n    function getIframeContentJson(id, iframe) {\n        /*jshint evil: true*/\n\n        var response, doc, innerHtml;\n\n        //IE may throw an \"access is denied\" error when attempting to access contentDocument on the iframe in some cases\n        try {\n            // iframe.contentWindow.document - for IE<7\n            doc = iframe.contentDocument || iframe.contentWindow.document;\n            innerHtml = doc.body.innerHTML;\n\n            log(\"converting iframe's innerHTML to JSON\");\n            log(\"innerHTML = \" + innerHtml);\n            //plain text response may be wrapped in <pre> tag\n            if (innerHtml && innerHtml.match(/^<pre/i)) {\n                innerHtml = doc.body.firstChild.firstChild.nodeValue;\n            }\n\n            response = handler._parseJsonResponse(innerHtml);\n        }\n        catch (error) {\n            log(\"Error when attempting to parse form upload response (\" + error.message + \")\", \"error\");\n            response = {success: false};\n        }\n\n        return response;\n    }\n\n    /**\n     * Creates form, that will be submitted to iframe\n     */\n    function createForm(id, iframe) {\n        var params = options.paramsStore.get(id),\n            method = options.method.toLowerCase() === \"get\" ? \"GET\" : \"POST\",\n            endpoint = options.endpointStore.get(id),\n            name = getName(id);\n\n        params[options.uuidName] = getUuid(id);\n        params[options.filenameParam] = name;\n\n        return handler._initFormForUpload({\n            method: method,\n            endpoint: endpoint,\n            params: params,\n            paramsInBody: options.paramsInBody,\n            targetName: iframe.name\n        });\n    }\n\n    this.uploadFile = function(id) {\n        var input = handler.getInput(id),\n            iframe = handler._createIframe(id),\n            promise = new qq.Promise(),\n            form;\n\n        form = createForm(id, iframe);\n        form.appendChild(input);\n\n        handler._attachLoadEvent(iframe, function(responseFromMessage) {\n            log(\"iframe loaded\");\n\n            var response = responseFromMessage ? responseFromMessage : getIframeContentJson(id, iframe);\n\n            handler._detachLoadEvent(id);\n\n            //we can't remove an iframe if the iframe doesn't belong to the same domain\n            if (!options.cors.expected) {\n                qq(iframe).remove();\n            }\n\n            if (response.success) {\n                promise.success(response);\n            }\n            else {\n                promise.failure(response);\n            }\n        });\n\n        log(\"Sending upload request for \" + id);\n        form.submit();\n        qq(form).remove();\n\n        return promise;\n    };\n\n    qq.extend(this, new qq.FormUploadHandler({\n        options: {\n            isCors: options.cors.expected,\n            inputName: options.inputName\n        },\n\n        proxy: {\n            onCancel: options.onCancel,\n            getName: getName,\n            getUuid: getUuid,\n            log: log\n        }\n    }));\n};\n", "/*globals qq*/\n/**\n * Upload handler used to upload to traditional endpoints.  It depends on File API support, and, therefore,\n * makes use of `XMLHttpRequest` level 2 to upload `File`s and `Blob`s to a generic server.\n *\n * @param spec Options passed from the base handler\n * @param proxy Callbacks & methods used to query for or push out data/changes\n */\nqq.traditional = qq.traditional || {};\nqq.traditional.XhrUploadHandler = function(spec, proxy) {\n    \"use strict\";\n\n    var handler = this,\n        getName = proxy.getName,\n        getSize = proxy.getSize,\n        getUuid = proxy.getUuid,\n        log = proxy.log,\n        multipart = spec.forceMultipart || spec.paramsInBody,\n\n        addChunkingSpecificParams = function(id, params, chunkData) {\n            var size = getSize(id),\n                name = getName(id);\n\n            params[spec.chunking.paramNames.partIndex] = chunkData.part;\n            params[spec.chunking.paramNames.partByteOffset] = chunkData.start;\n            params[spec.chunking.paramNames.chunkSize] = chunkData.size;\n            params[spec.chunking.paramNames.totalParts] = chunkData.count;\n            params[spec.totalFileSizeName] = size;\n\n            /**\n             * When a Blob is sent in a multipart request, the filename value in the content-disposition header is either \"blob\"\n             * or an empty string.  So, we will need to include the actual file name as a param in this case.\n             */\n            if (multipart) {\n                params[spec.filenameParam] = name;\n            }\n        },\n\n        allChunksDoneRequester = new qq.traditional.AllChunksDoneAjaxRequester({\n            cors: spec.cors,\n            endpoint: spec.chunking.success.endpoint,\n            log: log\n        }),\n\n        createReadyStateChangedHandler = function(id, xhr) {\n            var promise = new qq.Promise();\n\n            xhr.onreadystatechange = function() {\n                if (xhr.readyState === 4) {\n                    var result = onUploadOrChunkComplete(id, xhr);\n\n                    if (result.success) {\n                        promise.success(result.response, xhr);\n                    }\n                    else {\n                        promise.failure(result.response, xhr);\n                    }\n                }\n            };\n\n            return promise;\n        },\n\n        getChunksCompleteParams = function(id) {\n            var params = spec.paramsStore.get(id),\n                name = getName(id),\n                size = getSize(id);\n\n            params[spec.uuidName] = getUuid(id);\n            params[spec.filenameParam] = name;\n            params[spec.totalFileSizeName] = size;\n            params[spec.chunking.paramNames.totalParts] = handler._getTotalChunks(id);\n\n            return params;\n        },\n\n        isErrorUploadResponse = function(xhr, response) {\n            return qq.indexOf([200, 201, 202, 203, 204], xhr.status) < 0 ||\n                !response.success ||\n                response.reset;\n        },\n\n        onUploadOrChunkComplete = function(id, xhr) {\n            var response;\n\n            log(\"xhr - server response received for \" + id);\n            log(\"responseText = \" + xhr.responseText);\n\n            response = parseResponse(true, xhr);\n\n            return {\n                success: !isErrorUploadResponse(xhr, response),\n                response: response\n            };\n        },\n\n        // If this is an upload response, we require a JSON payload, otherwise, it is optional.\n        parseResponse = function(upload, xhr) {\n            var response = {};\n\n            try {\n                log(qq.format(\"Received response status {} with body: {}\", xhr.status, xhr.responseText));\n                response = qq.parseJson(xhr.responseText);\n            }\n            catch (error) {\n                upload && log(\"Error when attempting to parse xhr response text (\" + error.message + \")\", \"error\");\n            }\n\n            return response;\n        },\n\n        sendChunksCompleteRequest = function(id) {\n            var promise = new qq.Promise();\n\n            allChunksDoneRequester.complete(\n                    id,\n                    handler._createXhr(id),\n                    getChunksCompleteParams(id),\n                    spec.customHeaders.get(id)\n                )\n                .then(function(xhr) {\n                    promise.success(parseResponse(false, xhr), xhr);\n                }, function(xhr) {\n                    promise.failure(parseResponse(false, xhr), xhr);\n                });\n\n            return promise;\n        },\n\n        setParamsAndGetEntityToSend = function(params, xhr, fileOrBlob, id) {\n            var formData = new FormData(),\n                method = spec.method,\n                endpoint = spec.endpointStore.get(id),\n                name = getName(id),\n                size = getSize(id);\n\n            params[spec.uuidName] = getUuid(id);\n            params[spec.filenameParam] = name;\n\n            if (multipart) {\n                params[spec.totalFileSizeName] = size;\n            }\n\n            //build query string\n            if (!spec.paramsInBody) {\n                if (!multipart) {\n                    params[spec.inputName] = name;\n                }\n                endpoint = qq.obj2url(params, endpoint);\n            }\n\n            xhr.open(method, endpoint, true);\n\n            if (spec.cors.expected && spec.cors.sendCredentials) {\n                xhr.withCredentials = true;\n            }\n\n            if (multipart) {\n                if (spec.paramsInBody) {\n                    qq.obj2FormData(params, formData);\n                }\n\n                formData.append(spec.inputName, fileOrBlob);\n                return formData;\n            }\n\n            return fileOrBlob;\n        },\n\n        setUploadHeaders = function(id, xhr) {\n            var extraHeaders = spec.customHeaders.get(id),\n                fileOrBlob = handler.getFile(id);\n\n            xhr.setRequestHeader(\"Accept\", \"application/json\");\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\n            xhr.setRequestHeader(\"Cache-Control\", \"no-cache\");\n\n            if (!multipart) {\n                xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\n                //NOTE: return mime type in xhr works on chrome 16.0.9 firefox 11.0a2\n                xhr.setRequestHeader(\"X-Mime-Type\", fileOrBlob.type);\n            }\n\n            qq.each(extraHeaders, function(name, val) {\n                xhr.setRequestHeader(name, val);\n            });\n        };\n\n    qq.extend(this, {\n        uploadChunk: function(id, chunkIdx, resuming) {\n            var chunkData = handler._getChunkData(id, chunkIdx),\n                xhr = handler._createXhr(id, chunkIdx),\n                size = getSize(id),\n                promise, toSend, params;\n\n            promise = createReadyStateChangedHandler(id, xhr);\n            handler._registerProgressHandler(id, chunkIdx, chunkData.size);\n            params = spec.paramsStore.get(id);\n            addChunkingSpecificParams(id, params, chunkData);\n\n            if (resuming) {\n                params[spec.resume.paramNames.resuming] = true;\n            }\n\n            toSend = setParamsAndGetEntityToSend(params, xhr, chunkData.blob, id);\n            setUploadHeaders(id, xhr);\n            xhr.send(toSend);\n\n            return promise;\n        },\n\n        uploadFile: function(id) {\n            var fileOrBlob = handler.getFile(id),\n                promise, xhr, params, toSend;\n\n            xhr = handler._createXhr(id);\n            handler._registerProgressHandler(id);\n            promise = createReadyStateChangedHandler(id, xhr);\n            params = spec.paramsStore.get(id);\n            toSend = setParamsAndGetEntityToSend(params, xhr, fileOrBlob, id);\n            setUploadHeaders(id, xhr);\n            xhr.send(toSend);\n\n            return promise;\n        }\n    });\n\n    qq.extend(this, new qq.XhrUploadHandler({\n        options: qq.extend({namespace: \"traditional\"}, spec),\n        proxy: qq.extend({getEndpoint: spec.endpointStore.get}, proxy)\n    }));\n\n    qq.override(this, function(super_) {\n        return {\n            finalizeChunks: function(id) {\n                if (spec.chunking.success.endpoint) {\n                    return sendChunksCompleteRequest(id);\n                }\n                else {\n                    return super_.finalizeChunks(id, qq.bind(parseResponse, this, true));\n                }\n            }\n        };\n    });\n};\n", "/*globals qq*/\n/**\n * Ajax requester used to send a POST to a traditional endpoint once all chunks for a specific file have uploaded\n * successfully.\n *\n * @param o Options from the caller - will override the defaults.\n * @constructor\n */\nqq.traditional.AllChunksDoneAjaxRequester = function(o) {\n    \"use strict\";\n\n    var requester,\n        method = \"POST\",\n        options = {\n            cors: {\n                allowXdr: false,\n                expected: false,\n                sendCredentials: false\n            },\n            endpoint: null,\n            log: function(str, level) {}\n        },\n        promises = {},\n        endpointHandler = {\n            get: function(id) {\n                return options.endpoint;\n            }\n        };\n\n    qq.extend(options, o);\n\n    requester = qq.extend(this, new qq.AjaxRequester({\n        acceptHeader: \"application/json\",\n        validMethods: [method],\n        method: method,\n        endpointStore: endpointHandler,\n        allowXRequestedWithAndCacheControl: false,\n        cors: options.cors,\n        log: options.log,\n        onComplete: function(id, xhr, isError) {\n            var promise = promises[id];\n\n            delete promises[id];\n\n            if (isError) {\n                promise.failure(xhr);\n            }\n            else {\n                promise.success(xhr);\n            }\n        }\n    }));\n\n    qq.extend(this, {\n        complete: function(id, xhr, params, headers) {\n            var promise = new qq.Promise();\n\n            options.log(\"Submitting All Chunks Done request for \" + id);\n\n            promises[id] = promise;\n\n            requester.initTransport(id)\n                .withParams(params)\n                .withHeaders(headers)\n                .send(xhr);\n\n            return promise;\n        }\n    });\n};\n"]}